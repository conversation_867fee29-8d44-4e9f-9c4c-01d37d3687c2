import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:psm_app/core/core.dart';
import 'package:psm_app/data_sources/api_servies.dart';
import 'package:psm_app/globals.dart';
import 'package:psm_app/view/home.dart';
import 'package:psm_app/view/wellcome.dart';

import '../main.dart';

class Splash extends StatefulWidget {
  const Splash({Key? key}) : super(key: key);

  @override
  _SplashState createState() => _SplashState();
}

class _SplashState extends State<Splash> {
  Future<void> backend() async {
    await Future.delayed(const Duration(seconds: 1));
    if (Common.username == '') {
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(builder: (context) => const Wellcome()),
      );
    } else {
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(builder: (context) => const Home()),
      );
    }
  }

  @override
  void initState() {
    super.initState();
    //Future.delayed(const Duration(seconds: 1), () {
    backend();
    //});
  }

  @override
  void didChangeDependencies() {
    precacheImage(
        const AssetImage("assets/images/home_background.png"), context);
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Padding(
              padding: const EdgeInsets.only(top: 60),
              child: SizedBox(
                  width: 200,
                  child: Image.asset('assets/splash.png', fit: BoxFit.fill)),
            ),
            Padding(
              padding: const EdgeInsets.only(top: 20),
              child: CupertinoActivityIndicator(
                color: AppColors.greenPrimary,
                radius: 20,
              ),
            )
          ],
        ),
      ),
    );
  }
}
