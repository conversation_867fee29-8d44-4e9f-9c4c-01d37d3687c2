import 'package:flutter/material.dart';
import 'package:psm_app/core/app_colors.dart';
import 'package:psm_app/core/app_styles.dart';
import 'package:psm_app/globals.dart';
import 'package:psm_app/localization.dart';

class App_Info extends StatefulWidget {
  late String Title;
  late String Description;
  App_Info({
    Key? key,
    required this.Title,
    required this.Description,
  }) : super(key: key);
  @override
  _MyAppState createState() => _MyAppState();
}

_goBack(BuildContext context) {
  Navigator.pop(context);
}

class _MyAppState extends State<App_Info> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
        title: AppLocalizations.of(context).appName,
        debugShowCheckedModeBanner: false,
        home: Scaffold(
            appBar: AppBar(
              centerTitle: true,
              leading: IconButton(
                icon: Icon(Icons.arrow_back_ios_new),
                iconSize: 20.0,
                color: AppColors.blackText,
                onPressed: () {
                  _goBack(context);
                },
              ),
              title: Text(
                widget.Title,
                style: AppStyles.appBarTitle,
              ),
              backgroundColor: Colors.transparent,
              elevation: 0,
            ),
            body: Padding(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 0),
              child: Container(
                child: Text(
                  widget.Description,
                  style: AppStyles.body
                      .copyWith(color: Colors.black, fontSize: 14),
                ),
              ),
            )));
  }
}
