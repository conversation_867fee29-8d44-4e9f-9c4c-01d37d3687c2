import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:psm_app/core/app_colors.dart';
import 'package:psm_app/globals.dart';
import 'package:psm_app/localization.dart';

import 'package:psm_app/view/home.dart';
import 'package:psm_app/core/app_styles.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../provider/locale_provider.dart';

String userName = '';

class Wellcome extends StatefulWidget {
  const Wellcome({Key? key}) : super(key: key);

  @override
  State<Wellcome> createState() => _WellcomeState();
}

class _WellcomeState extends State<Wellcome> {
  final Map languageString = {'de': 'German', 'hi': 'Hindi', 'fr': 'French'};
  final String defaultLocale = Platform.localeName.contains('_')
      ? Platform.localeName.split('_')[0]
      : Platform.localeName;
  @override
  void initState() {
    super.initState();
    checkLanguage();
  }

  void checkLanguage() async {
    print(defaultLocale);
    await Future.delayed(const Duration(milliseconds: 300));
    if (Common.supportAnotherLanguage.contains(defaultLocale)) {
      _showConfirmDialog();
    } else if (defaultLocale == 'vi') {
      saveLanguage('vi');
      Provider.of<LocaleProvider>(context, listen: false)
          .setLocale(const Locale('vi'));
    } else {
      saveLanguage('en');
      Provider.of<LocaleProvider>(context, listen: false)
          .setLocale(const Locale('en'));
    }
  }

  void saveLanguage(String lang) async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    await preferences.setString('lang', lang);
  }

  void _showConfirmDialog() {
    showDialog<String>(
      context: context,
      builder: (BuildContext context) => Dialog(
        // contentPadding: EdgeInsets.fromLTRB(16, 30, 16, 24),
        insetPadding: const EdgeInsets.symmetric(horizontal: 16),
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.fromLTRB(16, 30, 16, 24),
            child: Column(
              children: [
                Container(
                  width: 196,
                  height: 139,
                  decoration: const BoxDecoration(
                      image: DecorationImage(
                    image: AssetImage("assets/images/info_dialog.png"),
                    fit: BoxFit.fill,
                  )),
                ),
                Text(
                    "Do you want to switch to ${languageString[defaultLocale]} language?",
                    style: AppStyles.dialogText,
                    textAlign: TextAlign.center),
                Padding(
                  padding: const EdgeInsets.only(top: 24),
                  child: SizedBox(
                    width: MediaQuery.of(context).size.width * 0.7,
                    child: Row(
                      children: [
                        Expanded(
                            child: Padding(
                          padding: const EdgeInsets.only(right: 6),
                          child: ElevatedButton(
                            onPressed: () {
                              Navigator.pop(context);
                              saveLanguage('en');
                              Provider.of<LocaleProvider>(context,
                                      listen: false)
                                  .setLocale(const Locale('en'));
                            },
                            child: Text(
                              AppLocalizations.of(context).no,
                              style: AppStyles.secondaryButton,
                              textAlign: TextAlign.center,
                            ),
                            style: ElevatedButton.styleFrom(
                              foregroundColor: AppColors.greenPrimary,
                              backgroundColor: Colors.white,
                              shadowColor: Colors.white,
                              elevation: 0,
                              minimumSize: const Size(20, 44),
                              side: BorderSide(
                                  color: AppColors.greenPrimary,
                                  width: 1.0,
                                  style: BorderStyle.solid),
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(4.0)),
                            ),
                          ),
                        )),
                        Expanded(
                            child: Padding(
                          padding: const EdgeInsets.only(left: 6),
                          child: ElevatedButton(
                            onPressed: () async {
                              Navigator.of(context).pop();
                              saveLanguage(defaultLocale);
                              Provider.of<LocaleProvider>(context,
                                      listen: false)
                                  .setLocale(Locale(defaultLocale));
                            },
                            child: Text(
                              "OK",
                              style: AppStyles.primaryButton,
                            ),
                            style: ElevatedButton.styleFrom(
                              foregroundColor: AppColors.white,
                              backgroundColor: AppColors.greenPrimary,
                              shadowColor:
                                  const Color.fromARGB(92, 0, 166, 144),
                              elevation: 0,
                              minimumSize: const Size(20, 44),
                              side: BorderSide(
                                  color: AppColors.greenPrimary,
                                  width: 1.0,
                                  style: BorderStyle.solid),
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(4.0)),
                            ),
                          ),
                        ))
                      ],
                    ),
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> saveUserName() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    await preferences.setString('userName', userName);
    Common.username = userName;
  }

  bool isButtonActive = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: /* now.hour > 6 && now.hour < 18
                ? AssetImage("assets/images/After Noon.png")
                : AssetImage("assets/images/Night.png") */
                AssetImage("assets/images/bg.png"),
            fit: BoxFit.cover,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            children: [
              Expanded(
                child: Container(
                  alignment: Alignment.center,
                  child: Padding(
                    padding: const EdgeInsets.only(bottom: 10, top: 0),
                    child: SingleChildScrollView(
                      physics: const NeverScrollableScrollPhysics(),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          //color: Colors.amberAccent,
                          AnimatedContainer(
                            duration: const Duration(milliseconds: 200),
                            height: MediaQuery.of(context).viewInsets.bottom > 0
                                ? MediaQuery.of(context).size.height * 0.3
                                : MediaQuery.of(context).size.height * 0.5,
                            child: SvgPicture.asset(
                              'assets/images/Splash.svg',
                              fit: BoxFit.cover,
                            ),
                          ),

                          Padding(
                            padding: const EdgeInsets.only(
                                left: 20.0, right: 20, bottom: 10),
                            child: Text(
                              AppLocalizations.of(context).wellcomeTitle,
                              textAlign: TextAlign.center,
                              style: /* now.hour > 6 && now.hour < 18
                                  ? AppStyles.bodyBold.copyWith(
                                      fontSize: 24, color: Color(0xFF00326C))
                                  : AppStyles.bodyBold.copyWith(
                                      color: Colors.white,
                                      fontSize:
                                          24) */
                                  AppStyles.bodyBold.copyWith(
                                      fontSize: 24,
                                      color: const Color(
                                          0xFF00326C)), /* AppStyles.appBarTitle.copyWith(
                                    color: Color(0xff00326C), fontSize: 24), */
                            ),
                          ),
                          Image.asset(
                            'assets/images/Rectangle.png',
                            fit: BoxFit.contain,
                            /* color: now.hour > 6 && now.hour < 18
                                ? null
                                : Colors.white, */
                          ),
                          /* Text(AppLocalizations.of(context).wellcomeTitle,
                              style: now.hour > 6 && now.hour < 18
                                  ? AppStyles.body
                                  : AppStyles.bodyWhite), */
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              /* Expanded(
                flex: 1,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(bottom: 10, top: 50),
                      child: Text(
                        AppLocalizations.of(context).wellcomeEnterName,
                        style: now.hour > 6 && now.hour < 18
                            ? AppStyles.body
                            : AppStyles.bodyWhite,
                      ),
                    ),
                  ],
                ),
              ), */
              Container(
                child: Column(
                  children: [
                    Align(
                      alignment: Alignment.centerLeft,
                      child: Text(
                        AppLocalizations.of(context).wellcomeEnterName,
                        style: /* now.hour > 6 && now.hour < 18
                            ? AppStyles.body.copyWith(
                                fontSize: 18,
                                color: Colors.black,
                                fontWeight: FontWeight.bold)
                            : AppStyles.bodyWhite.copyWith(
                                fontSize: 18, fontWeight: FontWeight.bold) */
                            AppStyles.body.copyWith(
                                fontSize: 18,
                                color: Colors.black,
                                fontWeight: FontWeight.bold),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(top: 8.0, bottom: 15),
                      child: IntrinsicHeight(
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            Expanded(
                              child: TextFormField(
                                textInputAction: TextInputAction.go,
                                onFieldSubmitted: (value) {
                                  if (isButtonActive) {
                                    saveUserName();
                                    Navigator.pushReplacement(
                                        context,
                                        MaterialPageRoute(
                                            builder: (context) => Home()));
                                  }
                                },
                                inputFormatters: [
                                  LengthLimitingTextInputFormatter(18),
                                ],
                                style: AppStyles.body.copyWith(
                                    color: Colors.black, fontSize: 16),
                                textCapitalization: TextCapitalization.words,
                                autofocus: true,
                                decoration: InputDecoration(
                                  fillColor: Colors.white,
                                  contentPadding: const EdgeInsets.all(12),
                                  hintText:
                                      AppLocalizations.of(context).yourName,
                                  filled: true,
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(4),
                                    borderSide: const BorderSide(
                                      width: 0,
                                      style: BorderStyle.none,
                                    ),
                                  ),
                                ),
                                onChanged: (text) {
                                  userName = text;
                                  if (text.isEmpty) {
                                    setState(() => isButtonActive = false);
                                  } else {
                                    setState(() => isButtonActive = true);
                                  }
                                },
                              ),
                            ),
                            GestureDetector(
                              onTap: isButtonActive
                                  ? () {
                                      saveUserName();
                                      Navigator.pushReplacement(
                                          context,
                                          MaterialPageRoute(
                                              builder: (context) => Home()));
                                    }
                                  : null,
                              child: Padding(
                                padding: const EdgeInsets.only(left: 8.0),
                                child: Container(
                                  decoration: isButtonActive
                                      ? BoxDecoration(
                                          boxShadow: [
                                            BoxShadow(
                                              color: const Color(0xFF00A690)
                                                  .withOpacity(0.2),
                                              blurRadius: 4,
                                              offset: const Offset(4,
                                                  8), // changes position of shadow
                                            ),
                                          ],
                                        )
                                      : null,
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(8.0),
                                    //padding: const EdgeInsets.only(top: 4.0),
                                    child: Container(
                                      height: 46,
                                      width: 43,
                                      color: isButtonActive
                                          ? AppColors.greenPrimary
                                          : const Color(0xFFE8E8E8),
                                      child: Align(
                                        alignment: Alignment.center,
                                        child: SvgPicture.asset(
                                          'assets/images/right_arrow.svg',
                                          color: isButtonActive
                                              ? Colors.white
                                              : const Color(0xFF6A6A6A),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            )
                          ],
                        ),
                      ),
                    ),
                    /* Padding(
                      padding: const EdgeInsets.only(bottom: 5),
                      child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          minimumSize: Size.fromHeight(40),
                          primary: AppColors.greenButton,
                        ),
                        child: Text(
                          'OK',
                          style: AppStyles.bodyBold
                              .copyWith(color: AppColors.white),
                        ),
                        onPressed: isButtonActive
                            ? () {
                                saveUserName();
                                Navigator.pushReplacement(
                                    context,
                                    MaterialPageRoute(
                                        builder: (context) => Home()));
                              }
                            : null,
                      ),
                    ), */
                    /* Padding(
                      padding: const EdgeInsets.only(bottom: 5),
                      child: Text(
                        AppLocalizations.of(context).productOfScrumPass,
                        style: now.hour > 6 && now.hour < 18
                            ? AppStyles.body
                            : AppStyles.bodyWhite,
                      ),
                    ) */
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
