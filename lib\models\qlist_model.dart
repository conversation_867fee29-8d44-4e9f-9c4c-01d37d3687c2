import 'dart:convert';

class Qlist {
  Qlist({
    required this.quid,
    required this.quiz_name,
    required this.noq,
    required this.duration,
    required this.pass_percentage,
    required this.start_date,
    required this.end_date,
    required this.question_selection,
    required this.description,
    required this.exam_quiz,
  });

  String quid;
  String quiz_name;
  String noq;
  String duration;
  String pass_percentage;
  String start_date;
  String end_date;
  String description;
  String question_selection;
  String exam_quiz;
  double highscore = 0;

  factory Qlist.fromMap(Map<String, dynamic> json) => Qlist(
        quid: json["quid"],
        quiz_name: json["quiz_name"],
        noq: json["noq"],
        duration: json["duration"],
        pass_percentage: json["pass_percentage"],
        start_date: json["start_date"],
        end_date: json["end_date"],
        exam_quiz: json["exam_quiz"],
        question_selection: json["question_selection"],
        description: json["description"],
      );
  Map<String, dynamic> toMap() {
    return {
      'quid': quid,
      'quiz_name': quiz_name,
      'noq': noq,
      'duration': duration,
      'pass_percentage': pass_percentage,
      'start_date': start_date,
      'end_date': end_date,
      'question_selection': question_selection,
      'exam_quiz': exam_quiz,
      'description': description,
      'highscore': highscore
    };
  }

  String toJson() => json.encode(toMap());

  factory Qlist.fromJson(String source) => Qlist.fromMap(json.decode(source));
}
