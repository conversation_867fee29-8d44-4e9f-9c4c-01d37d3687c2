import 'dart:convert';
import 'dart:developer';

import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/services.dart';
import 'package:psm_app/data_sources/api_servies.dart';
import 'package:psm_app/globals.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'dart:io' show Platform;

import 'package:shared_preferences/shared_preferences.dart';

class PurchaseApi {
  static Future<void> initPlatformState() async {
    await Purchases.setLogLevel(LogLevel.debug);
    if (Platform.isAndroid) {
      await Purchases.configure(
          PurchasesConfiguration(Common.revenuecatAndroid));
    } else if (Platform.isIOS) {
      await Purchases.configure(PurchasesConfiguration(Common.revenuecatiOS));
    }
  }

  static Future<List<Offering>> fetchOffer() async {
    try {
      Offerings offerings = await Purchases.getOfferings();
      final current = offerings.current;
      return current == null ? [] : [current];
    } on PlatformException {
      return [];
    }
  }

  static Future<dynamic> purchasePackage(Package package) async {
    try {
      Purchases.setDisplayName(Common.username);
      Purchases.setAttributes({"platform": Common.platform});
      final purchase = await Purchases.purchasePackage(package);
      inspect(purchase);
      if (purchase.entitlements.all.isNotEmpty &&
          purchase.entitlements.all['Premium']!.isActive) {
        Common.premium = true;
        Common.premiumType =
            purchase.entitlements.all['Premium']!.productIdentifier;
        if (purchase.entitlements.all['Premium']!.periodType ==
            PeriodType.trial) {
          Common.trial = true;
          Common.trialEndDate =
              purchase.entitlements.all['Premium']!.expirationDate ?? "";
        }
        await ApiServices().savePurchaseInfo(jsonEncode(purchase));
        return 'success';
      } else {
        Common.premium = false;
        return 'error';
      }
    } on PlatformException catch (e) {
      final data = {'type': 'Purchase', 'error': e.message};
      ApiServices().sendErrorReport(jsonEncode(data));
      var errorCode = PurchasesErrorHelper.getErrorCode(e);
      if (errorCode != PurchasesErrorCode.purchaseCancelledError) {
        return 'error';
      } else {
        return 'canceled';
      }
    }
  }

  static Future<bool> purchaserInfo() async {
    try {
      CustomerInfo purchaserInfo = await Purchases.getCustomerInfo();
      print(purchaserInfo.toString());
      if (purchaserInfo.entitlements.all.isNotEmpty &&
          purchaserInfo.entitlements.all['Premium']!.isActive) {
        Common.premium = true;
        Common.premiumType =
            purchaserInfo.entitlements.all['Premium']!.productIdentifier;
        if (purchaserInfo.entitlements.all['Premium']!.periodType ==
            PeriodType.trial) {
          Common.trial = true;
          Common.trialEndDate =
              purchaserInfo.entitlements.all['Premium']!.expirationDate ?? "";
        }
        return true;
      } else {
        Common.premium = false;
        SharedPreferences preferences = await SharedPreferences.getInstance();
        final premium = preferences.getBool('premium');
        if (premium ?? false) {
          preferences.remove('quizList');
          preferences.remove('quizListTimer');
          preferences.remove('premium');
        }
        return false;
      }
    } on PlatformException catch (e) {
      log(e.toString(), error: e);
      return false;
    }
  }

  static Future<void> setCustomerAttribute() async {
    try {
      final instanceId = await FirebaseAnalytics.instance.appInstanceId;
      log("instanceId: $instanceId");
      if (instanceId != null) {
        await Purchases.setFirebaseAppInstanceId(instanceId);
      }
    } on PlatformException catch (e) {
      log(e.toString(), error: e);
    }
  }

  static Future<bool> restorePurchase() async {
    try {
      CustomerInfo restoredInfo = await Purchases.restorePurchases();
      log(restoredInfo.toString());
      if (restoredInfo.entitlements.all.isNotEmpty &&
          restoredInfo.entitlements.all['Premium']!.isActive) {
        Common.premium = true;
        Common.premiumType =
            restoredInfo.entitlements.all['Premium']!.productIdentifier;
        return true;
      } else {
        Common.premium = false;
        SharedPreferences preferences = await SharedPreferences.getInstance();
        final premium = preferences.getBool('premium');
        if (premium ?? false) {
          preferences.remove('quizList');
          preferences.remove('quizListTimer');
          preferences.remove('premium');
        }
        return false;
      }
    } on PlatformException catch (e) {
      log(e.toString(), error: e);
      return false;
    }
  }
}
