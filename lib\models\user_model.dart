import 'dart:convert';

class UserModel {
  String name;
  int passpercentage;
  int failpercentage;

  UserModel({
    required this.name,
    required this.passpercentage,
    required this.failpercentage,
  });
  set setUserName(String value) {
    name = value;
  }

  set setPasspercentage(int value) {
    passpercentage = value;
  }

  set setFailpercentage(int value) {
    failpercentage = value;
  }

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'passpercentage': passpercentage,
      'failpercentage': failpercentage,
    };
  }
}
