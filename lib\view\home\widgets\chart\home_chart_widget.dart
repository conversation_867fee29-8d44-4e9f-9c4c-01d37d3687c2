import 'dart:developer';

import 'package:psm_app/core/app_colors.dart';
import 'package:psm_app/core/core.dart';
import 'package:flutter/material.dart';
import 'package:percent_indicator/percent_indicator.dart';

class HomeChartWidget extends StatefulWidget {
  final double percent;
  const HomeChartWidget({Key? key, required this.percent}) : super(key: key);

  @override
  _HomeChartWidgetState createState() => _HomeChartWidgetState();
}

class _HomeChartWidgetState extends State<HomeChartWidget>
    with SingleTickerProviderStateMixin {
  //animação
  late AnimationController _controller;
  late Animation<double> _animation;
  @override
  dispose() {
    _controller.dispose(); // you need this
    super.dispose();
  }

  void _initAnimation() {
    _controller = AnimationController(
        vsync: this,
        duration: Duration(seconds: widget.percent >= 0.5 ? 2 : 1));
    _animation =
        Tween<double>(begin: 0.0, end: widget.percent).animate(_controller);
    _controller.forward();
  }

  @override
  void initState() {
    _initAnimation();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        height: 85,
        width: 85,
        child: AnimatedBuilder(
          animation: _animation,
          builder: (context, _) => Stack(children: [
            Center(
              child: Container(
                height: 85,
                width: 85,
                child: CircularPercentIndicator(
                  radius: 85,
                  lineWidth: 14,
                  percent: double.parse(_animation.value.toStringAsFixed(2)) < 0
                      ? 0
                      : _animation.value > 1
                          ? 1
                          : double.parse(_animation.value.toStringAsFixed(2)),
                  backgroundColor: AppColors.homeChartBackground,
                  progressColor: AppColors.homeChartPrimary,
                  circularStrokeCap: CircularStrokeCap.round,
                  center: Text.rich(
                    TextSpan(
                        text: _animation.value < 0
                            ? "N/A"
                            : "${_animation.value > 1 ? 100 : (_animation.value * 100).toStringAsFixed(0)}",
                        style:
                            AppStyles.homeScoreCardTitle.copyWith(fontSize: 20),
                        children: [
                          TextSpan(
                              text: _animation.value < 0 ? "" : "%",
                              style: AppStyles.homeScoreCardText
                                  .copyWith(fontWeight: FontWeight.w600))
                        ]),
                  ),
                ),
              ),
            ),
          ]),
        ));
  }
}
