import 'package:flutter/material.dart';

import '../../localization.dart';

class FlashCardTutorial extends StatefulWidget {
  const FlashCardTutorial({Key? key}) : super(key: key);

  @override
  State<FlashCardTutorial> createState() => _FlashCardTutorialState();
}

class _FlashCardTutorialState extends State<FlashCardTutorial> {
  int page = 0;
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Image.asset(
          page == 0
              ? 'assets/images/swipe_left.png'
              : page == 1
                  ? 'assets/images/swipe_right.png'
                  : 'assets/images/click.png',
          width: 132,
          height: 147,
        ),
        Text(
          page == 0
              ? AppLocalizations.of(context).tutorialSwipeLeft
              : page == 1
                  ? AppLocalizations.of(context).tutorialSwipeRight
                  : AppLocalizations.of(context).tutorialTap,
          style: const TextStyle(
              fontSize: 16, fontWeight: FontWeight.w500, color: Colors.white),
        ),
        const SizedBox(
          height: 80,
        ),
        SizedBox(
          width: 150,
          height: 48,
          child: TextButton(
              onPressed: () {
                if (page == 2) {
                  Navigator.pop(context);
                } else {
                  setState(() {
                    page++;
                  });
                }
              },
              style: ButtonStyle(
                  side: MaterialStateProperty.all(const BorderSide(
                      color: Colors.white,
                      width: 1.0,
                      style: BorderStyle.solid))),
              child: Text(page != 2 ? AppLocalizations.of(context).next : "OK",
                  style: const TextStyle(fontSize: 16, color: Colors.white))),
        ),
      ],
    );
  }
}
