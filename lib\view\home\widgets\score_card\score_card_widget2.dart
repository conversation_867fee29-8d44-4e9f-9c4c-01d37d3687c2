import 'package:flutter/cupertino.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter/material.dart';
import 'package:psm_app/core/core.dart';
import 'package:psm_app/globals.dart';

import 'package:psm_app/localization.dart';
import 'package:psm_app/models/result_model.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../chart/home_chart_widget.dart';

class ScoreCardWidget2 extends StatefulWidget {
  const ScoreCardWidget2({Key? key}) : super(key: key);

  @override
  State<ScoreCardWidget2> createState() => _ScoreCardWidget2State();
}

class _ScoreCardWidget2State extends State<ScoreCardWidget2> {
  int doneQuiz = 0;
  double passRate2 = 0;
  Future<int> getCountQuizDone() async {
    doneQuiz = 0;
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    // Fetch and decode data
    final String resultString = await prefs.getString('result') ?? "";
    final List<Result> musics;
    if (resultString != '')
      musics = Result.decode(resultString);
    else
      musics = [];

    doneQuiz = musics.length;
    return doneQuiz;
  }

  Future<double> getCountQuiz() async {
    passRate2 = 0;
    double avg = 0;
    int passCount = 0;
    double latestScore = 0;
    int streak = 0;
    int questionDone = 0;
    int wrongCount = 0;
    int rightCount = 0;
    int markCount = 0;
    int totalTime = 0;
    double each = 0;
    double totalScoreRecent = 0;
    double totalScore = 0;
    int numX = 0;
    bool status = false;
    double scoreDoneQuiz = 0;
    double scoreAvg10 = 0;
    double scoreAvg = 0;
    double scoreAvgEach = 0;
    double scoreQuestionDone = 0;
    double scorePass = 0;
    int passCountRecent = 0;
    String totalCategoryQuestion = "";
    int totalCategoryQuestionCount = 0;
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    // Fetch and decode data
    final String resultString = await prefs.getString('result') ?? "";
    totalCategoryQuestion = await prefs.getString('numCategory') ?? "600";
    final List<Result> results = [];
    int quizDone = 0;
    if (resultString != '') {
      for (var i = 0; i < Result.decode(resultString).length; i++) {
        if (Result.decode(resultString)[i].environment == null ||
            Result.decode(resultString)[i].environment == environment.text) {
          results.add(Result.decode(resultString)[i]);
        }
      }
    }

    //Số lần pass 10 lần gần nhất

    int count = 0;
    int countRecent = 0;
    int countExam = 0;
    List uniqueQuestion = [];
    for (var i = results.length - 1; i >= 0; i--) {
      // đếm lần pass liên tiếp dài nhất
      if (count < 10) {
        count++;
        // Lấy tổng số điểm để tính avg 10 bai gan nhat
        totalScoreRecent += double.parse(double.parse(results[i].percentage).toStringAsFixed(0));
      }
      if (results[i].status == 'Pass') {
        passCountRecent++;
      }
      //Tim question unique
      for (var t = 0; t < results[i].questions.length; t++) {
        if (!uniqueQuestion.contains(results[i].questions[t].id)) {
          uniqueQuestion.add(results[i].questions[t].id);
        }
      }
      //Tổng quiz làm
      quizDone++;
      // Lấy tổng số điểm để tính avg
      if (results[i].exam_quiz == "1") {
        totalScore += double.parse(double.parse(results[i].percentage).toStringAsFixed(0));
        countExam++;
      }
      if (countRecent < 10) {
        //Lấy tổng thời gian
        totalTime += results[i].timeDoQuiz;
        // đếm số câu đã làm
        for (var j = 0; j < results[i].answers.length; j++) {
          if (results[i].answers[j].length != 0 && results[i].answers[j].length != null) {
            questionDone++;
          }
        }
        countRecent++;
      }
    }
    //Điểm số bài test làm
    scoreDoneQuiz = quizDone / 40 * 100;
    // Điểm pass 10
    if (passCountRecent >= 8) {
      scorePass = 100;
    } else {
      scorePass = passCountRecent * 100 / 8;
    }
    // TB 10
    scoreAvg10 = totalScoreRecent / count;
    // TB All
    if (countExam > 0)
      scoreAvg = totalScore / countExam;
    else
      scoreAvg = 0;
    // thoi gian TB moi cau 10 klan gan nhat

    if (questionDone > 0)
      scoreAvgEach = double.parse(((totalTime / 60) / questionDone).toStringAsFixed(2));
    else
      scoreAvgEach = 1;

    if (Common.appid == "com.scrumpass.psm" ||
        Common.appid == "com.scrumpass.psd" ||
        Common.appid == "com.scrumpass.pspo") {
      if (scoreAvgEach <= 0.4) {
        scoreAvgEach = 100;
      } else if (0.4 < scoreAvgEach && scoreAvgEach <= 0.45) {
        scoreAvgEach = 95;
      } else if (0.45 < scoreAvgEach && scoreAvgEach <= 0.5) {
        scoreAvgEach = 92;
      } else if (0.5 < scoreAvgEach && scoreAvgEach <= 0.55) {
        scoreAvgEach = 75;
      } else if (0.55 < scoreAvgEach && scoreAvgEach <= 0.6) {
        scoreAvgEach = 67;
      } else if (0.6 < scoreAvgEach && scoreAvgEach <= 0.65) {
        scoreAvgEach = 58;
      } else if (0.65 < scoreAvgEach && scoreAvgEach <= 0.7) {
        scoreAvgEach = 50;
      } else if (0.7 < scoreAvgEach && scoreAvgEach <= 0.75) {
        scoreAvgEach = 46;
      } else if (0.75 < scoreAvgEach && scoreAvgEach <= 0.8) {
        scoreAvgEach = 42;
      } else if (0.8 < scoreAvgEach && scoreAvgEach <= 0.85) {
        scoreAvgEach = 25;
      } else if (0.85 < scoreAvgEach && scoreAvgEach <= 0.9) {
        scoreAvgEach = 17;
      } else if (0.9 < scoreAvgEach && scoreAvgEach <= 0.95) {
        scoreAvgEach = 10;
      } else if (0.95 < scoreAvgEach && scoreAvgEach <= 1) {
        scoreAvgEach = 0;
      } else if (scoreAvgEach > 1) {
        scoreAvgEach = 0;
      }
    } else if (Common.appid == "com.scrumpass.psk" ||
        Common.appid == "com.scrumpass.pmp" ||
        Common.appid == "com.scrumpass.sps" ||
        Common.appid == "com.scrumpass.acp" ||
        Common.appid == "com.scrumpass.pspo2") {
      if (scoreAvgEach <= 0.75) {
        scoreAvgEach = 100;
      } else if (0.75 < scoreAvgEach && scoreAvgEach <= 0.8) {
        scoreAvgEach = 90;
      } else if (0.8 < scoreAvgEach && scoreAvgEach <= 0.85) {
        scoreAvgEach = 85;
      } else if (0.85 < scoreAvgEach && scoreAvgEach <= 0.9) {
        scoreAvgEach = 80;
      } else if (0.9 < scoreAvgEach && scoreAvgEach <= 0.95) {
        scoreAvgEach = 75;
      } else if (0.95 < scoreAvgEach && scoreAvgEach <= 1) {
        scoreAvgEach = 60;
      } else if (1 < scoreAvgEach && scoreAvgEach <= 1.25) {
        scoreAvgEach = 50;
      } else if (1.25 < scoreAvgEach && scoreAvgEach <= 1.5) {
        scoreAvgEach = 35;
      } else if (1.5 < scoreAvgEach && scoreAvgEach <= 2) {
        scoreAvgEach = 25;
      } else if (2 < scoreAvgEach && scoreAvgEach <= 2.25) {
        scoreAvgEach = 10;
      } else if (2.25 < scoreAvgEach && scoreAvgEach <= 2.5) {
        scoreAvgEach = 0;
      } else if (scoreAvgEach > 2.5) {
        scoreAvgEach = 0;
      }
    } else if (Common.appid == "com.scrumpass.pal") {
      if (scoreAvgEach <= 0.8) {
        scoreAvgEach = 100;
      } else if (0.8 < scoreAvgEach && scoreAvgEach <= 0.85) {
        scoreAvgEach = 90;
      } else if (0.85 < scoreAvgEach && scoreAvgEach <= 0.9) {
        scoreAvgEach = 80;
      } else if (0.9 < scoreAvgEach && scoreAvgEach <= 0.95) {
        scoreAvgEach = 75;
      } else if (0.95 < scoreAvgEach && scoreAvgEach <= 1) {
        scoreAvgEach = 70;
      } else if (1 < scoreAvgEach && scoreAvgEach <= 1.25) {
        scoreAvgEach = 60;
      } else if (1.25 < scoreAvgEach && scoreAvgEach <= 1.5) {
        scoreAvgEach = 50;
      } else if (1.5 < scoreAvgEach && scoreAvgEach <= 2) {
        scoreAvgEach = 35;
      } else if (2 < scoreAvgEach && scoreAvgEach <= 2.25) {
        scoreAvgEach = 25;
      } else if (2.25 < scoreAvgEach && scoreAvgEach <= 2.5) {
        scoreAvgEach = 10;
      } else if (2.5 < scoreAvgEach && scoreAvgEach <= 2.75) {
        scoreAvgEach = 0;
      } else if (scoreAvgEach > 2.75) {
        scoreAvgEach = 0;
      }
    } else /* if (Common.appid == "com.scrumpass.psm2") */ {
      if (scoreAvgEach <= 1.5) {
        scoreAvgEach = 100;
      } else if (1.5 < scoreAvgEach && scoreAvgEach <= 2) {
        scoreAvgEach = 90;
      } else if (2 < scoreAvgEach && scoreAvgEach <= 2.25) {
        scoreAvgEach = 85;
      } else if (2.25 < scoreAvgEach && scoreAvgEach <= 2.5) {
        scoreAvgEach = 75;
      } else if (2.5 < scoreAvgEach && scoreAvgEach <= 2.75) {
        scoreAvgEach = 65;
      } else if (2.75 < scoreAvgEach && scoreAvgEach <= 3) {
        scoreAvgEach = 50;
      } else if (3 < scoreAvgEach && scoreAvgEach <= 3.25) {
        scoreAvgEach = 35;
      } else if (3.25 < scoreAvgEach && scoreAvgEach <= 3.5) {
        scoreAvgEach = 25;
      } else if (3.5 < scoreAvgEach && scoreAvgEach <= 3.75) {
        scoreAvgEach = 10;
      } else if (3.75 < scoreAvgEach && scoreAvgEach <= 4) {
        scoreAvgEach = 0;
      } else if (scoreAvgEach > 4) {
        scoreAvgEach = 0;
      }
    }

    //So cau da lam
    totalCategoryQuestionCount = int.parse(totalCategoryQuestion);
    scoreQuestionDone = uniqueQuestion.length / int.parse(totalCategoryQuestion) * 100;
    if (scoreQuestionDone > 100) {
      scoreQuestionDone = 100;
    }
    var passRate = scoreDoneQuiz * 0.10 +
        scorePass * 0.15 +
        scoreAvg10 * 0.2 +
        scoreAvg * 0.25 +
        scoreAvgEach * 0.1 +
        scoreQuestionDone * 0.2;
    if (passRate.isNaN || quizDone < 10) {
      passRate = -1;
    }
    passRate2 = double.parse(passRate.toStringAsFixed(0)) / 100;
    return passRate2;
  }

  Future<double> getQuizPercentage() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    double percent = 0;
    // Fetch and decode data
    final String resultString = await prefs.getString('result') ?? "";
    if (resultString == "") {
      return percent;
    }
    final List<Result> musics;
    if (resultString != '')
      musics = Result.decode(resultString);
    else
      musics = [];
    double count = 0;
    double total = 0;
    for (var i = 0; i < musics.length; i++) {
      total += double.parse(musics[i].percentage);
      count++;
    }
    percent = (total / count) / 100;
    return percent;
  }

  @override
  void initState() {
    super.initState();
    //Future<String?> token = attempPost();
    // calculateData();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 0, right: 0),
      child: Container(
        height: 136,
        decoration: BoxDecoration(
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.0275),
                spreadRadius: 0,
                blurRadius: 6.52,
                offset: Offset(0, 8.15), // changes position of shadow
              ),
            ],
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                AppColors.gradientBannerTop,
                AppColors.gradientBannerBottom,
              ],
            ),
            color: AppColors.white,
            borderRadius: BorderRadius.circular(8)),
        child: Stack(
          children: [
            Positioned(
                top: 8,
                right: 8,
                child: GestureDetector(
                  onTap: () {
                    _showConfirmDialog();
                  },
                  child: SvgPicture.asset(
                    'assets/images/readMore.svg',
                    fit: BoxFit.contain,
                  ),
                )),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 18),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  FutureBuilder<double>(
                      future: getCountQuiz(), // async work
                      builder: (BuildContext context, AsyncSnapshot<double> snapshot) {
                        switch (snapshot.connectionState) {
                          case ConnectionState.waiting:
                            return Padding(
                              key: UniqueKey(),
                              padding: const EdgeInsets.only(),
                              child: HomeChartWidget(
                                percent: -1,
                              ),
                            );
                          default:
                            if (snapshot.hasError)
                              return new Text('Error: ${snapshot.error}');
                            else
                              return Padding(
                                padding: const EdgeInsets.only(),
                                child: HomeChartWidget(
                                  key: UniqueKey(),
                                  percent: snapshot.data == null
                                      ? -1
                                      : double.parse(snapshot.data!.toStringAsFixed(2)),
                                ),
                              );
                        }
                      }),
                  Expanded(
                    flex: 3,
                    child: Padding(
                      padding: const EdgeInsets.only(left: 20),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Flexible(
                                child: Text(AppLocalizations.of(context).examPassRate,
                                    style: AppStyles.homeScoreCardTitle),
                              ),
                              Icon(
                                Icons.arrow_forward_ios_rounded,
                                color: Colors.white,
                                size: 16,
                              ),
                            ],
                          ),
                          /* SizedBox(
                            height: 3,
                          ),
                          FutureBuilder<int>(
                              future: getCountQuiz('amount'), // async work
                              builder: (BuildContext context,
                                  AsyncSnapshot<int> snapshot) {
                                switch (snapshot.connectionState) {
                                  case ConnectionState.waiting:
                                    return Text('Loading....',
                                        style: AppStyles.homeScoreCardText);
                                  default:
                                    return Text(
                                        AppLocalizations.of(context)
                                                .totalQuizDone +
                                            ": ${snapshot.data}",
                                        style: AppStyles.homeScoreCardText);
                                }
                              }),
                          SizedBox(
                            height: 3,
                          ),
                          FutureBuilder<int>(
                              future: getCountQuiz('consecutive'), // async work
                              builder: (BuildContext context,
                                  AsyncSnapshot<int> snapshot) {
                                switch (snapshot.connectionState) {
                                  case ConnectionState.waiting:
                                    return Text('Loading....',
                                        style: AppStyles.homeScoreCardText);
                                  default:
                                    return Text(
                                        AppLocalizations.of(context).highScore +
                                            ": ${snapshot.data}",
                                        style: AppStyles.homeScoreCardText);
                                }
                              }),
                          SizedBox(
                            height: 3,
                          ),
                          FutureBuilder<int>(
                              future: getCountQuiz('recent'), // async work
                              builder: (BuildContext context,
                                  AsyncSnapshot<int> snapshot) {
                                switch (snapshot.connectionState) {
                                  case ConnectionState.waiting:
                                    return Text('Loading....',
                                        style: AppStyles.homeScoreCardText);
                                  default:
                                    return Text(
                                        AppLocalizations.of(context)
                                                .recentScore +
                                            ": ${snapshot.data}%",
                                        style: AppStyles.homeScoreCardText);
                                }
                              }),
                          SizedBox(
                            height: 3,
                          ),
                          FutureBuilder<int>(
                              future: getCountQuiz(
                                  'totalDoneQuestion'), // async work
                              builder: (BuildContext context,
                                  AsyncSnapshot<int> snapshot) {
                                switch (snapshot.connectionState) {
                                  case ConnectionState.waiting:
                                    return Text('Loading....',
                                        style: AppStyles.homeScoreCardText);
                                  default:
                                    return Text(
                                        "Số câu hỏi đã làm" +
                                            ": ${snapshot.data}",
                                        style: AppStyles.homeScoreCardText);
                                }
                              }),
                          SizedBox(
                            height: 3,
                          ),
                          FutureBuilder<int>(
                              future: getCountQuiz('countWrong'), // async work
                              builder: (BuildContext context,
                                  AsyncSnapshot<int> snapshot) {
                                switch (snapshot.connectionState) {
                                  case ConnectionState.waiting:
                                    return Text('Loading....',
                                        style: AppStyles.homeScoreCardText);
                                  default:
                                    return Text(
                                        "Số câu hỏi sai" + ": ${snapshot.data}",
                                        style: AppStyles.homeScoreCardText);
                                }
                              }),
                          SizedBox(
                            height: 3,
                          ),
                          FutureBuilder<int>(
                              future: getCountQuiz('countMarked'), // async work
                              builder: (BuildContext context,
                                  AsyncSnapshot<int> snapshot) {
                                switch (snapshot.connectionState) {
                                  case ConnectionState.waiting:
                                    return Text('Loading....',
                                        style: AppStyles.homeScoreCardText);
                                  default:
                                    return Text(
                                        "Số câu hỏi đánh dấu" +
                                            ": ${snapshot.data}",
                                        style: AppStyles.homeScoreCardText);
                                }
                              }), */
                          const SizedBox(
                            height: 10,
                          ),
                          FutureBuilder<int>(
                              future: getCountQuizDone(),
                              builder: (BuildContext context, AsyncSnapshot<int> snapshot) {
                                switch (snapshot.connectionState) {
                                  case ConnectionState.waiting:
                                    return CupertinoActivityIndicator(
                                      radius: 14,
                                      color: AppColors.grey,
                                    );
                                  default:
                                    if (snapshot.hasError) {
                                      return Text(AppLocalizations.of(context).notEnough);
                                    } else {
                                      return Text(
                                        snapshot.data! < 10
                                            ? AppLocalizations.of(context).notEnough
                                            : (passRate2 * 100) <= 60
                                                ? AppLocalizations.of(context).notReady
                                                : (passRate2 * 100) < 81
                                                    ? AppLocalizations.of(context).keepGoing
                                                    : AppLocalizations.of(context).goodToGo,
                                        style: AppStyles.homeScoreCardText,
                                      );
                                    }
                                }
                              }),
                          /* SizedBox(
                            height: 10,
                          ),
                          Text(
                            "Số lần pass ( 10 lần gần nhất ) : " +
                                passCountRecent.toString(),
                            style: AppStyles.homeScoreCardText,
                          ),
                          SizedBox(
                            height: 10,
                          ),
                          Text(
                            "Số bài test đã làm : " + quizDone.toString(),
                            style: AppStyles.homeScoreCardText,
                          ),
                          SizedBox(
                            height: 10,
                          ),
                          Text(
                            "Điểm TB 10 bài gần nhất : " +
                                scoreAvg10.toStringAsFixed(0),
                            style: AppStyles.homeScoreCardText,
                          ),
                          SizedBox(
                            height: 10,
                          ),
                          Text(
                            "Điểm TB bài thi exam : " +
                                scoreAvg.toStringAsFixed(0),
                            style: AppStyles.homeScoreCardText,
                          ),
                          SizedBox(
                            height: 10,
                          ),
                          Text(
                            "Thời gian TB mỗi câu theo phút ( 10 lần gần nhất ) : " +
                                (totalTime / questionDone).toStringAsFixed(2),
                            style: AppStyles.homeScoreCardText,
                          ),
                          SizedBox(
                            height: 10,
                          ),
                          Text(
                            "Điểm số Thời gian TB mỗi câu ( 10 lần gần nhất) : " +
                                scoreAvgEach.toStringAsFixed(2),
                            style: AppStyles.homeScoreCardText,
                          ),
                          SizedBox(
                            height: 10,
                          ),
                          Text(
                            "Tổng thời gian ( phut ): " + totalTime.toString(),
                            style: AppStyles.homeScoreCardText,
                          ),
                          SizedBox(
                            height: 10,
                          ),
                          Text(
                            "Điểm pass rate: " +
                                (passRate2 * 100).toStringAsFixed(0),
                            style: AppStyles.homeScoreCardText,
                          ), */
                        ],
                      ),
                    ),
                  )
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showConfirmDialog() {
    showDialog<String>(
      context: context,
      builder: (BuildContext context) => Dialog(
        // contentPadding: EdgeInsets.fromLTRB(16, 30, 16, 24),
        insetPadding: EdgeInsets.symmetric(horizontal: 16),
        child: SingleChildScrollView(
          child: ConstrainedBox(
            constraints: new BoxConstraints(
              maxWidth: 600.0,
            ),
            child: Padding(
              padding: const EdgeInsets.fromLTRB(16, 30, 16, 12),
              child: Column(
                children: [
                  Container(
                    width: 196,
                    height: 139,
                    decoration: BoxDecoration(
                        image: DecorationImage(
                      image: AssetImage("assets/images/info_dialog.png"),
                      fit: BoxFit.fill,
                    )),
                  ),
                  Text(
                    AppLocalizations.of(context).disclamer,
                    style: AppStyles.dialogText,
                    textAlign: TextAlign.center,
                  ),
                  Padding(
                    padding: const EdgeInsets.only(top: 24, bottom: 8),
                    child: Container(
                      width: MediaQuery.of(context).size.width,
                      child: Row(
                        children: [
                          Expanded(
                              child: Padding(
                            padding: const EdgeInsets.only(right: 0),
                            child: ElevatedButton(
                              onPressed: () {
                                Navigator.pop(context);
                              },
                              child: Text(
                                "OK",
                                style: AppStyles.primaryButton,
                              ),
                              style: ElevatedButton.styleFrom(
                                foregroundColor: AppColors.white, backgroundColor: AppColors.greenPrimary,
                                shadowColor: Color.fromARGB(92, 0, 166, 144),
                                elevation: 0,
                                minimumSize: Size(20, 44),
                                side: BorderSide(
                                    color: AppColors.greenPrimary,
                                    width: 1.0,
                                    style: BorderStyle.solid),
                                shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(4.0)),
                              ),
                            ),
                          )),
                        ],
                      ),
                    ),
                  )
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
