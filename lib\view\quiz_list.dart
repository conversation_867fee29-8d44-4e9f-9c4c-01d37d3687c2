// ignore_for_file: prefer_const_constructors, sized_box_for_whitespace, unnecessary_new, prefer_const_declarations, unused_local_variable
import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:firebase_performance/firebase_performance.dart';
import 'package:flutter/material.dart';
import 'package:psm_app/globals.dart';
import 'package:psm_app/models/qlist_model.dart';
import 'package:http/http.dart' as http;
import 'package:psm_app/view/exam_detail.dart';
import 'package:psm_app/view/widgets/error_dialog.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'home.dart';

Future<List<Qlist>> fetchPost() async {
  SharedPreferences preferences = await SharedPreferences.getInstance();
  var token = preferences.getString('token');
  String url = getUserInfor().url;
  String appkey = getUserInfor().appkey;
  String info = await GetDeviceInfo() + "-Quiz List Request";
  try {
    final response = await dio.post(url,
        data: FormData.fromMap({"key": appkey, "token": token, "info": info}));
    if (response.statusCode == 200) {
      final parsed = json.decode(response.data).cast<Map<String, dynamic>>();

      return parsed.map<Qlist>((json) => Qlist.fromMap(json)).toList();
    }
  } catch (e) {
    print(e);
  }
  return [];
}

/* void main() => runApp(MyApp()); */
List<Qlist> postFromJson(String str) =>
    List<Qlist>.from(json.decode(str).map((x) => Qlist.fromMap(x)));

class Widget_Qlist extends StatefulWidget {
  @override
  _MyAppState createState() => _MyAppState();
}

_goBack(BuildContext context) {
  Navigator.pop(context);
}

class _MyAppState extends State<Widget_Qlist> {
  late Future<List<Qlist>> futurePost;

  late bool status;

  @override
  void initState() {
    super.initState();
    //Future<String?> token = attempPost();
    futurePost = fetchPost();
  }

  String TimeHeader = "Thời lượng \n"
      "(Tính theo phút)";
  final ButtonStyle raisedButtonStyle = ElevatedButton.styleFrom(
    foregroundColor: Colors.black87,
    backgroundColor: Colors.lightBlueAccent,
    minimumSize: Size(88, 36),
    padding: EdgeInsets.symmetric(horizontal: 16),
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.all(Radius.circular(5)),
    ),
  );
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Fetch Data Example',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primaryColor: Colors.lightBlueAccent,
      ),
      home: Scaffold(
        appBar: AppBar(
          leading: IconButton(
            icon: Icon(Icons.arrow_back),
            iconSize: 20.0,
            onPressed: () {
              _goBack(context);
            },
          ),
          title: Text('Danh sách QUIZ'),
        ),
        body: Container(
          color: Colors.white,
          child: FutureBuilder<List<Qlist>>(
            future: futurePost,
            builder: (context, snapshot) {
              if (snapshot.hasData) {
                return SingleChildScrollView(
                    scrollDirection: Axis.vertical,
                    child: SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        padding: EdgeInsets.all(1.2),
                        child: FittedBox(
                          fit: BoxFit.fill,
                          child: DataTable(
                            dataRowHeight: 80,
                            //columnSpacing: 100,
                            sortColumnIndex: 0,
                            showCheckboxColumn: false,

                            // ignore: prefer_const_literals_to_create_immutables
                            columns: [
                              DataColumn(
                                  label: Text(
                                    "Tên Quiz",
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                      fontSize: 16.0,
                                    ),
                                  ),
                                  numeric: false,
                                  tooltip: "Quiz name"),
                              DataColumn(
                                label: Text(
                                  "Số câu hỏi",
                                  style: TextStyle(
                                    fontSize: 16.0,
                                  ),
                                ),
                                numeric: false,
                                tooltip: "Amount",
                              ),
                              DataColumn(
                                label: Text(
                                  TimeHeader,
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    fontSize: 16.0,
                                  ),
                                ),
                                numeric: false,
                                tooltip: "Time",
                              ),
                              DataColumn(
                                label: Text(
                                  "Hành động",
                                  style: TextStyle(
                                    fontSize: 16.0,
                                  ),
                                ),
                                numeric: false,
                                tooltip: "Action",
                              ),
                            ],
                            rows: snapshot.data!
                                .map(
                                  (quiz) => DataRow(
                                      /* onSelectChanged: (b) {
                            print(sale.uid);
                          }, */
                                      cells: [
                                        DataCell(Text(quiz.quiz_name)),
                                        DataCell(
                                          Align(
                                            alignment: Alignment.center,
                                            child: Text(quiz.noq),
                                          ),
                                        ),
                                        DataCell(
                                          Align(
                                            alignment: Alignment.center,
                                            child: Text(quiz.duration),
                                          ),
                                        ),
                                        DataCell(ElevatedButton(
                                          style: raisedButtonStyle,
                                          onPressed: () {
                                            Navigator.push(
                                                context,
                                                MaterialPageRoute(
                                                    builder: (context) =>
                                                        ExamDetail(
                                                            user: quiz)));
                                          },
                                          child: Text('Làm bài'),
                                        )),
                                      ]),
                                )
                                .toList(),
                          ),
                        )));
              } else if (snapshot.hasError) {
                return ErrorDialog();
              } else {
                return Center(child: CircularProgressIndicator());
              }
            },
          ),
        ),
      ),
    );
  }
}
