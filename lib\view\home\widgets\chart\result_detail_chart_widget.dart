import 'package:percent_indicator/percent_indicator.dart';
import 'package:psm_app/core/app_colors.dart';
import 'package:psm_app/core/core.dart';
import 'package:flutter/material.dart';

class ResultDetailChartWidget extends StatefulWidget {
  final double percent;
  final String status;

  const ResultDetailChartWidget(
      {Key? key, required this.percent, required this.status})
      : super(key: key);

  @override
  _ResultDetailChartWidgetState createState() =>
      _ResultDetailChartWidgetState();
}

class _ResultDetailChartWidgetState extends State<ResultDetailChartWidget>
    with SingleTickerProviderStateMixin {
  //animação
  late AnimationController _controller;
  late Animation<double> _animation;
  @override
  dispose() {
    _controller.dispose(); // you need this
    super.dispose();
  }

  void _initAnimation() {
    _controller = AnimationController(
        vsync: this,
        duration: Duration(seconds: widget.percent >= 0.5 ? 2 : 1));
    _animation =
        Tween<double>(begin: 0.0, end: widget.percent).animate(_controller);
    _controller.forward();
  }

  @override
  void initState() {
    _initAnimation();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        height: 106,
        width: 87,
        child: AnimatedBuilder(
          animation: _animation,
          builder: (context, _) => Stack(children: [
            Center(
              child: Container(
                height: 106,
                width: 87,
                child: CircularPercentIndicator(
                  radius: 87,
                  lineWidth: 14,
                  percent: _animation.value,
                  backgroundColor: widget.status == 'Fail'
                      ? AppColors.resultFailChartBackground
                      : AppColors.resultPassChartBackground,
                  progressColor: widget.status == 'Fail'
                      ? AppColors.redText
                      : AppColors.greenText,
                  circularStrokeCap: CircularStrokeCap.round,
                  center: Text.rich(
                    TextSpan(
                        text: "${(_animation.value * 100).toInt()}",
                        style: widget.status == 'Fail'
                            ? AppStyles.listQuizTitle.copyWith(
                                color: AppColors.redText, fontSize: 24)
                            : AppStyles.listQuizTitle.copyWith(
                                color: AppColors.greenText, fontSize: 24),
                        children: [
                          TextSpan(
                              text: "%",
                              style: widget.status == 'Fail'
                                  ? AppStyles.listQuizTitle.copyWith(
                                      fontWeight: FontWeight.w600,
                                      fontSize: 12,
                                      color: AppColors.redText)
                                  : AppStyles.listQuizTitle.copyWith(
                                      fontWeight: FontWeight.w600,
                                      fontSize: 12,
                                      color: AppColors.greenText))
                        ]),
                  ),
                  footer: Padding(
                    padding: const EdgeInsets.only(top: 2),
                    child: Text(
                      widget.status,
                      style: widget.status == 'Fail'
                          ? AppStyles.listFail.copyWith(fontSize: 14)
                          : AppStyles.listPass.copyWith(fontSize: 14),
                    ),
                  ),
                ),
              ),
            ),
          ]),
        ));
  }
}
