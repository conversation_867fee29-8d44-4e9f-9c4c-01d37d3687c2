import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:psm_app/globals.dart';
import 'package:psm_app/models/flashcard_model.dart';
import 'package:psm_app/view/flashcard/flashcard.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../core/core.dart';
import '../../helper/helper.dart';
import '../../localization.dart';
import '../home.dart';

class FlashCardFavorite extends StatefulWidget {
  const FlashCardFavorite({Key? key}) : super(key: key);

  @override
  State<FlashCardFavorite> createState() => _FlashCardFavoriteState();
}

class _FlashCardFavoriteState extends State<FlashCardFavorite> {
  List<Flashcard> favFlashcard = [];
  Future<bool>? isDataInit;

  @override
  void initState() {
    logPage("Flashcard Favorite");
    initData();
    super.initState();
  }

  Future<dynamic> initData() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    final json = preferences.getString('flashcard_favorite');
    final List list = jsonDecode(json ?? '[]');
    favFlashcard = list.map((e) => Flashcard.fromJson(e)).toList();
    isDataInit = Future.value(true);
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFAFAFA),
      appBar: PreferredSize(
          preferredSize: const Size.fromHeight(kToolbarHeight),
          child: Container(
            decoration: const BoxDecoration(boxShadow: [
              BoxShadow(
                color: Color.fromRGBO(0, 0, 0, 0.16),
                offset: Offset(0, 4),
                blurRadius: 12,
              )
            ]),
            child: AppBar(
              title: Text(AppLocalizations.of(context).favoriteWords),
              centerTitle: true,
              backgroundColor: Colors.white,
              elevation: 0,
              shadowColor: const Color.fromRGBO(0, 0, 0, 0.16),
              leading: IconButton(
                icon: const Icon(Icons.arrow_back_ios_new),
                iconSize: 20.0,
                color: AppColors.blackText,
                onPressed: () {
                  Navigator.pop(context);
                },
              ),
              titleTextStyle: TextStyle(
                  color: AppColors.blackText,
                  fontSize: 18,
                  fontWeight: FontWeight.w700),
              actions: [
                Padding(
                  padding: const EdgeInsets.only(right: 18),
                  child: SizedBox(
                    width: 21,
                    height: 21,
                    child: GestureDetector(
                      onTap: _onResetPressed,
                      child: SvgPicture.asset('assets/images/restart.svg'),
                    ),
                  ),
                )
              ],
            ),
          )),
      body: FutureBuilder(
        future: isDataInit,
        builder: (context, snapshot) {
          if (snapshot.hasData) {
            if (favFlashcard.isNotEmpty) {
              return ListView.builder(
                itemCount: favFlashcard.length,
                itemBuilder: (BuildContext context, int index) {
                  if (index == 0) {
                    return Padding(
                      padding: const EdgeInsets.only(top: 10),
                      child: favItem(favFlashcard[index]),
                    );
                  }
                  if (index == favFlashcard.length - 1 &&
                      Common.platform == 'Android') {
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 6),
                      child: favItem(favFlashcard[index]),
                    );
                  }
                  return favItem(favFlashcard[index]);
                },
              );
            } else {
              return Center(
                  child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(10),
                    child: SvgPicture.asset('assets/images/no_item.svg'),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(top: 20),
                    child: Column(
                      children: [
                        Text(AppLocalizations.of(context).noData,
                            style: AppStyles.body
                                .copyWith(color: Colors.black, fontSize: 16)),
                      ],
                    ),
                  ),
                ],
              ));
            }
          }

          return Container();
        },
      ),
    );
  }

  Widget favItem(Flashcard flashcard) {
    return Container(
      padding: const EdgeInsets.all(11),
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
      decoration: BoxDecoration(boxShadow: const [
        BoxShadow(
          color: Color.fromRGBO(0, 0, 0, 0.0275),
          spreadRadius: 0,
          blurRadius: 6.52,
          offset: Offset(0, 8.15), // changes position of shadow
        ),
        BoxShadow(
          color: Color.fromRGBO(0, 0, 0, 0.0531),
          spreadRadius: 0,
          blurRadius: 46.85,
          offset: Offset(0, 64.81), // changes position of shadow
        ),
      ], color: Colors.white, borderRadius: BorderRadius.circular(8)),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              Helper().parseHtmlString(flashcard.term ?? ""),
              style: TextStyle(
                  color: AppColors.blackText,
                  fontWeight: FontWeight.w700,
                  fontSize: 14,
                  height: 1.57),
            ),
            GestureDetector(
              child: const Icon(
                Icons.favorite,
                color: Color(0xFFFF7245),
                size: 18,
              ),
            )
          ],
        ),
        Padding(
          padding: const EdgeInsets.only(top: 12),
          child: Text(
            Helper().parseHtmlString(
                (Localizations.localeOf(context).toString() == "vi"
                        ? flashcard.explanationVn == ''
                            ? flashcard.explanation
                            : flashcard.explanationVn
                        : flashcard.explanation) ??
                    ""),
            style: TextStyle(
                color: AppColors.blackText, fontSize: 14, height: 1.57),
          ),
        )
      ]),
    );
  }

  Future<bool> _onResetPressed() async {
    return (await showDialog(
          context: context,
          builder: (BuildContext context) => Dialog(
            // contentPadding: EdgeInsets.fromLTRB(16, 30, 16, 24),
            insetPadding: const EdgeInsets.symmetric(horizontal: 16),
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.fromLTRB(16, 30, 16, 24),
                child: Column(
                  children: [
                    Container(
                      width: 196,
                      height: 139,
                      decoration: const BoxDecoration(
                          image: DecorationImage(
                        image: AssetImage("assets/images/info_dialog.png"),
                        fit: BoxFit.fill,
                      )),
                    ),
                    Text(
                      AppLocalizations.of(context).restartFlashCardPopup,
                      style: AppStyles.dialogText,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(top: 24),
                      child: Container(
                        width: MediaQuery.of(context).size.width * 0.9,
                        child: Row(
                          children: [
                            Expanded(
                                child: Padding(
                              padding: const EdgeInsets.only(right: 6),
                              child: ElevatedButton(
                                onPressed: () {
                                  Navigator.of(context).pop(false);
                                },
                                child: Text(
                                  AppLocalizations.of(context).no,
                                  style: AppStyles.secondaryButton,
                                  textAlign: TextAlign.center,
                                ),
                                style: ElevatedButton.styleFrom(
                                  foregroundColor: AppColors.greenPrimary,
                                  backgroundColor: Colors.white,
                                  shadowColor: Colors.white,
                                  elevation: 0,
                                  minimumSize: Size(20, 44),
                                  side: BorderSide(
                                      color: AppColors.greenPrimary,
                                      width: 1.0,
                                      style: BorderStyle.solid),
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(4.0)),
                                ),
                              ),
                            )),
                            Expanded(
                                child: Padding(
                              padding: const EdgeInsets.only(left: 6),
                              child: ElevatedButton(
                                onPressed: onPressReset,
                                child: Text(
                                  "OK",
                                  style: AppStyles.primaryButton,
                                ),
                                style: ElevatedButton.styleFrom(
                                  foregroundColor: AppColors.white,
                                  backgroundColor: AppColors.greenPrimary,
                                  shadowColor: Color.fromARGB(92, 0, 166, 144),
                                  elevation: 0,
                                  minimumSize: Size(20, 44),
                                  side: BorderSide(
                                      color: AppColors.greenPrimary,
                                      width: 1.0,
                                      style: BorderStyle.solid),
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(4.0)),
                                ),
                              ),
                            ))
                          ],
                        ),
                      ),
                    )
                  ],
                ),
              ),
            ),
          ),
        )) ??
        false;
  }

  void onPressReset() {
    Navigator.of(context).pop(false);
    resetFlashCard().then((value) {
      Navigator.pop(context);
      Navigator.pushReplacement(
          context, MaterialPageRoute(builder: (context) => const FlashCard()));
    });
  }

  Future<void> resetFlashCard() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    preferences.remove('flashcard_favorite');
    preferences.remove('flashcard_skip');
    preferences.remove('flashcard_done');
    preferences.remove('flashcard_current_index');
    preferences.remove('flashcard_show_end');
    await getFlashcard();
  }

  Future<void> getFlashcard() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    bool loadRemoteDatatSucceed = false;
    bool connect = await InternetConnectionChecker().hasConnection;
    if (connect) {
      var token = preferences.getString('token');
      String info = await GetDeviceInfo() + "- Get Flashcard";
      String data = Common.flashcardCertId;
      String getFlashcardUrl = apiUrl + 'get_flashcard';
      try {
        final response = await dio.post(
          getFlashcardUrl,
          data: FormData.fromMap({
            'key': appkey,
            'token': token,
            'info': info,
            'data': data,
            "debugId": Common.debugId
          }),
        );
        if (response.statusCode == 200) {
          preferences.setString('flashcard', response.data);
          loadRemoteDatatSucceed = true;
        }
      } catch (e) {
        if (loadRemoteDatatSucceed == false) print(e);
      }
    }
  }
}
