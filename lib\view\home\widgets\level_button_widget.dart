import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:psm_app/core/app_colors.dart';

class LevelButtonWidget extends StatelessWidget {
  final String label;
  final String type;

  // Aqui no construtor criamos um assert para garantir que a label so vai assumir um desses valores
  LevelButtonWidget({Key? key, required this.label, required this.type})
      : assert(["NotSelect", "Selected"].contains(label)),
        super(key: key);

  final config = {
    "NotSelect": {
      "color": Colors.transparent,
      "borderColor": Colors.transparent,
      "fontColor": AppColors.blackText,
    },
    "Selected": {
      "color": AppColors.scoreCardText,
      "borderColor": AppColors.scoreCardText,
      "fontColor": AppColors.lightBlueText,
    },
  };

  // aqui temos o ! para indicar que nao sera nulo
  Color get color => config[label]!['color']!;
  Color get borderColor => config[label]!['borderColor']!;
  Color get fontColor => config[label]!['fontColor']!;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(20),
        border: Border.fromBorderSide(
          BorderSide(color: borderColor),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 5,
          vertical: 5,
        ),
        child: Text(
          type,
          style: GoogleFonts.roboto(fontSize: 14, fontWeight: FontWeight.w600),
        ),
      ),
    );
  }
}
