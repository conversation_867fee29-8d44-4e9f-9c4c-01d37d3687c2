name: psm_app
description: A new Flutter project.

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 2.20.0+132

environment:
  sdk: ">=3.1.3 <4.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  shared_preferences: ^2.0.9
  intl: ^0.18.0
  provider: ^6.0.2
  google_fonts: ^4.0.4
  flutter_html: ^3.0.0-beta.2
  webview_flutter: ^4.2.3
  device_info_plus: ^9.0.3
  dart_ipify: ^1.1.1
  http: ^0.13.0+2
  url_launcher: ^6.0.17
  flutter_countdown_timer: ^4.1.0
  flutter_sheet_localization:
    git:
      url: https://gitlab.com/chithanh1998/flutter_sheet_localization
      path: flutter_sheet_localization
  internet_connection_checker: ^1.0.0+1
  package_info_plus: ^4.1.0
  firebase_core: ^3.15.1
  firebase_analytics: ^11.5.2
  firebase_crashlytics: ^4.3.9
  store_redirect: ^2.0.1
  rating_dialog: ^2.0.3
  launch_review: ^3.0.1
  percent_indicator: ^3.4.0
  flutter_svg: ^2.0.7
  purchases_flutter: ^8.10.6
  loader_overlay: ^2.0.6
  flutter_spinkit: ^5.1.0
  flutter_scroll_shadow: ^1.0.2
  cached_network_image: ^3.2.0
  flutter_local_notifications: ^15.1.1
  # fix version for m1 / flutter 3.0
  syncfusion_flutter_charts: ^22.2.10
  syncfusion_flutter_core: ^22.2.10
  back_button_interceptor: ^6.0.2
  firebase_performance: ^0.10.1+9
  firebase_performance_dio: ^0.7.1
  dio: ^5.3.2
  fl_chart: ^0.63.0
  scrollable_positioned_list: ^0.3.5
  notification_permissions: ^0.6.1
  firebase_core_platform_interface: ^6.0.0
  swipeable_card_stack:
    git:
      url: https://gitlab.com/chithanh1998/scrumpass-swipe-card.git
      ref: main
    # path: "../swipe_card"
  flash_card:
    git:
      url: https://gitlab.com/chithanh1998/scrumpass-flashcard.git
      ref: main
  tap_debouncer: ^2.0.2
  share_plus: ^7.1.0
  showcaseview: ^4.0.1
  rxdart: ^0.27.7
  dotted_border: ^2.0.0+1
  pull_to_refresh: ^2.0.0
  cloud_firestore: ^5.6.11
  sentry_flutter: ^8.14.2
  sentry_dio: ^8.14.2
  amplitude_flutter: ^3.13.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_sheet_localization_generator:
    git:
      url: https://gitlab.com/chithanh1998/flutter_sheet_localization
      path: flutter_sheet_localization_generator
  build_runner: ^2.1.7
  dependency_validator: ^3.0.0

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^2.0.2

  flutter_native_splash: ^2.3.2
  flutter_launcher_icons: ^0.13.1
  sentry_dart_plugin: ^1.0.0  

dependency_overrides:
  intl: ^0.19.0
  http: ^1.4.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  generate: false
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  assets:
    - assets/
    - assets/images/
    - assets/launcher_icon/
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Roboto
      fonts:
        - asset: assets/fonts/Roboto-Regular.ttf
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
flappy_translator:
  input_file_path: "assets/languages.xlsx"
  output_dir: "lib/l10n"
  file_name: "i18n"
  delimiter: ","
  start_index: 1
  depend_on_context: true
  use_single_quotes: false
  replace_no_break_spaces: false
  expose_get_string: false
  expose_loca_strings: false
  expose_locale_maps: false

flutter_native_splash:
  color: "#ffffff"
  image: assets/splash.png

flutter_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/launcher_icon/icon_production_new.png"

sentry:
  project: "psm-exam"
  org: "thanhnc"
  auth_token: "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"