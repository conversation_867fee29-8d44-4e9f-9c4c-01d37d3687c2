import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:psm_app/core/app_colors.dart';
import 'package:psm_app/core/app_styles.dart';
import 'package:psm_app/globals.dart';
import 'package:psm_app/localization.dart';
import 'package:psm_app/models/other_app_model.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';

class NewAboutUs extends StatefulWidget {
  @override
  _MyAppState createState() => _MyAppState();
}

_goBack(BuildContext context) {
  logPage("Main");
  Navigator.pop(context);
}

class _MyAppState extends State<NewAboutUs> {
  late Future<List<Other>> futurePost;
  Map vi = {
    'about_us_content':
        'ScrumPass là một tổ chức chuyên nghiệp chuyên cung cấp dịch vụ cố vấn và luyện thi các chứng chỉ <PERSON>gile nh<PERSON>M, PSPO, PMI-ACP. Với các khóa học được cam kết cao, chúng tôi đã giúp hàng trăm học viên vượt qua các kỳ thi mỗi năm. Bên cạnh đó, các dịch vụ tư vấn, mô phỏng bài thi, công cụ dành cho Scrum Master của chúng tôi đã được nhiều công ty và cá nhân tin tưởng.',
    'description':
        'Chào mừng bạn đến với ứng dụng ${Common.appName}. Với ${Common.appName} bạn có thể ôn tập, kiểm tra lại kiến thức và chuẩn bị cho chứng chỉ Scrum Master chuyên nghiệp của bạn. Các bài kiểm tra của chúng tôi chắc chắn là một nguồn tham khảo giá trị giúp bạn vượt qua bài kiểm tra chứng chỉ PSM của Scrum.org. \n\nChứng chỉ Scrum Master chuyên nghiệp (PSM) là một trong những chứng chỉ giá trị dành cho vị trị Scrum Master hoặc bất kỳ ai đã, đang và sẽ áp dụng khung làm việc Scrum cho đội nhóm của mình. Chứng chỉ PSM sẽ giúp bạn chứng minh sự hiểu biết của mình về khung làm việc Scrum cũng như sẽ giúp bạn tăng thu nhập, lương của mình. \n\n${Common.appName} bao gồm nhiều bộ câu hỏi đã được chúng tôi lựa chọn cẩn thận để giúp bạn làm quen với đề thi cũng như tăng cao khả năng vượt qua bài thi chứng chỉ PSM ngay ở lần thi đầu tiên. \n\n${Common.appName} là một sản phẩm của ScrumPass.'
  };

  Map en = {
    'about_us_content':
        'ScrumPass is a professional organization specializing in providing mentoring and exam preparation services for Agile certifications such as PSM, PSPO, PMI-ACP. With highly committed courses, we have helped hundreds of students pass the exams every year. Besides, our consulting services, exam simulator, Scrum tools have been trusted by many companies and individuals.',
    'description':
        'Welcome to ${Common.appName} application. You can improve and validate your basic knowledge of the Professional Scrum Master Exam. With ${Common.appName} you can improve your Agile & Scrum knowledge and help you to pass on your first try scrum.org or PMI certifications. \n\nAll the test content on the app are curated by ScrumPass experts. As you take the exams, you will be able to track a summary of your progress directly on the app. You will know which areas you need to improve on to improve your Scrum Knowledge and to become a Scrum professional. \n\nTake the scrum examinations in different areas \nTrack your result and performance summary \nDetailed explanations on each question and answers \nReal exam style full mock exam with timed interface \nContains large number of questions that covers all syllabus area. \n\nWe regularly update the question set with the real exam content. If you take our Scrum exams frequently and aim at achieving at least 85% on all the exams you take, you will easily pass the real Scrum examination. Download ${Common.appName} and get yourself familiarize with the Scrum exam. You can use our application from anywhere and at any time.'
  };
  Future<List<Other>> fetchOtherApp() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    final String resultString = preferences.getString('otherApp') ?? "";
    final List<Other> musics;

    if (resultString != '') {
      List<dynamic> map = jsonDecode(resultString);
      musics = Other.decode(resultString);
      return musics;
    } else {
      throw Exception('Failed to load list');
    }
  }

  @override
  void initState() {
    super.initState();
    logPage("About Us");
    //futurePost = fetchOtherApp();
    //inspect(futurePost);
  }

  bool appDetail = true;
  bool scrumpassDetail = true;
  bool appIntroductionDetail = true;
  @override
  Widget build(BuildContext context) {
    bool isKeyboardShowing = MediaQuery.of(context).viewInsets.vertical > 0;
    var language = Localizations.localeOf(context).toString();
    return MaterialApp(
        title: AppLocalizations.of(context).aboutUs,
        debugShowCheckedModeBanner: false,
        home: Scaffold(
            backgroundColor: const Color(0xFFFAFAFA),
            appBar: AppBar(
              leading: Padding(
                padding: const EdgeInsets.only(top: 10.0),
                child: IconButton(
                  icon: const Icon(Icons.arrow_back_ios_new),
                  iconSize: 20.0,
                  color: AppColors.blackText,
                  onPressed: () => _goBack(context),
                ),
              ),
              centerTitle: true,
              backgroundColor: const Color(0xffFAFAFA),
              title: Padding(
                padding: const EdgeInsets.only(top: 15.0),
                child: Text(
                  AppLocalizations.of(context).aboutUs,
                  style: AppStyles.appBarTitle.copyWith(fontSize: 20, fontWeight: FontWeight.w700),
                ),
              ),
              bottom: const PreferredSize(
                  preferredSize: Size.zero,
                  child: Divider(
                    color: Color(0xffE6E6E7),
                    thickness: 2,
                  )),
              elevation: 0,
            ),
            body: GestureDetector(
              onTap: () {
                FocusScope.of(context).requestFocus(new FocusNode());
              },
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      child: Padding(
                        padding: const EdgeInsets.fromLTRB(0, 12, 0, 16),
                        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                          Container(
                            margin: const EdgeInsets.fromLTRB(16, 0, 16, 0),
                            width: MediaQuery.of(context).size.width,
                            decoration: const BoxDecoration(
                                color: Color(0xffD8E3FF),
                                borderRadius: BorderRadius.all(Radius.circular(4))),
                            child: Stack(
                              children: [
                                Padding(
                                  padding: const EdgeInsets.all(12.0),
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      RichText(
                                        text: TextSpan(
                                          style: AppStyles.titleBold
                                              .copyWith(color: const Color(0xff52628D)),
                                          children: const <TextSpan>[
                                            TextSpan(text: '95', style: TextStyle(fontSize: 30)),
                                            TextSpan(text: '%', style: TextStyle(fontSize: 20)),
                                          ],
                                        ),
                                      ),
                                      Text(
                                        AppLocalizations.of(context).certificationPassRate,
                                        style: AppStyles.titleBold
                                            .copyWith(color: const Color(0xff52628D), fontSize: 14),
                                      ),
                                    ],
                                  ),
                                ),
                                Positioned(
                                  bottom: 7,
                                  right: 7,
                                  child: SvgPicture.asset(
                                    'assets/images/Arrow.svg',
                                    fit: BoxFit.contain,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(
                            height: 12,
                          ),
                          Container(
                            margin: const EdgeInsets.fromLTRB(16, 0, 16, 0),
                            width: MediaQuery.of(context).size.width,
                            decoration: const BoxDecoration(
                                color: Color(0xffFDE2C4),
                                borderRadius: BorderRadius.all(Radius.circular(4))),
                            child: Stack(
                              children: [
                                Padding(
                                  padding: const EdgeInsets.all(12.0),
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      RichText(
                                        text: TextSpan(
                                          style: AppStyles.titleBold
                                              .copyWith(color: const Color(0xffC98940)),
                                          children: const <TextSpan>[
                                            TextSpan(text: '92', style: TextStyle(fontSize: 30)),
                                            TextSpan(text: '%', style: TextStyle(fontSize: 20)),
                                          ],
                                        ),
                                      ),
                                      Text(
                                        AppLocalizations.of(context).serviceSatisfactionRate,
                                        style: AppStyles.titleBold
                                            .copyWith(color: const Color(0xffC98940), fontSize: 14),
                                      ),
                                    ],
                                  ),
                                ),
                                Positioned(
                                  bottom: 8,
                                  right: 16,
                                  child: SvgPicture.asset(
                                    'assets/images/LikeComment.svg',
                                    fit: BoxFit.contain,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(
                            height: 12,
                          ),
                          Container(
                            margin: const EdgeInsets.fromLTRB(16, 0, 16, 0),
                            width: MediaQuery.of(context).size.width,
                            decoration: const BoxDecoration(
                                color: Color(0xffB2EEE6),
                                borderRadius: BorderRadius.all(Radius.circular(4))),
                            child: Stack(
                              children: [
                                Padding(
                                  padding: const EdgeInsets.all(12.0),
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      RichText(
                                        text: TextSpan(
                                          style: AppStyles.titleBold
                                              .copyWith(color: const Color(0xff486A66)),
                                          children: const <TextSpan>[
                                            TextSpan(text: '95', style: TextStyle(fontSize: 30)),
                                            TextSpan(text: '%', style: TextStyle(fontSize: 20)),
                                          ],
                                        ),
                                      ),
                                      Text(
                                        AppLocalizations.of(context).averageExamResult,
                                        style: AppStyles.titleBold
                                            .copyWith(color: const Color(0xff486A66), fontSize: 14),
                                      ),
                                    ],
                                  ),
                                ),
                                Positioned(
                                  bottom: 0,
                                  right: 12,
                                  child: SvgPicture.asset(
                                    'assets/images/ExamStudent.svg',
                                    fit: BoxFit.contain,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(
                            height: 12,
                          ),
                          Padding(
                            padding: const EdgeInsets.fromLTRB(16, 0, 16, 0),
                            child: Column(
                              children: [
                                GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      if (!appDetail)
                                        appDetail = true;
                                      else
                                        appDetail = false;
                                    });
                                  },
                                  child: Container(
                                    color: Colors.transparent,
                                    margin: const EdgeInsets.symmetric(vertical: 8),
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          "Exam Simulator",
                                          style: AppStyles.body.copyWith(
                                              color: const Color(0xff3D3D3D),
                                              fontSize: 16,
                                              fontWeight: FontWeight.w700),
                                        ),
                                        Icon(
                                          appDetail
                                              ? Icons.keyboard_arrow_up_rounded
                                              : Icons.keyboard_arrow_down_rounded,
                                          size: 30,
                                          color: const Color(0xff7A8694),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                                ExpandedSection(
                                  expand: appDetail,
                                  child: Padding(
                                    padding: const EdgeInsets.only(bottom: 20.0),
                                    child: Text(
                                      AppLocalizations.of(context)
                                          .descriptionContent
                                          .replaceAll('Common.appName', Common.appName),
                                      style: AppStyles.body.copyWith(
                                          color: const Color(0xff666666),
                                          fontSize: 14,
                                          fontWeight: FontWeight.w400),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.fromLTRB(16, 0, 16, 0),
                            child: Column(
                              children: [
                                GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      if (!scrumpassDetail)
                                        scrumpassDetail = true;
                                      else
                                        scrumpassDetail = false;
                                    });
                                  },
                                  child: Container(
                                    color: Colors.transparent,
                                    margin: const EdgeInsets.symmetric(vertical: 8),
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          "ScrumPass",
                                          style: AppStyles.body.copyWith(
                                              color: const Color(0xff3D3D3D),
                                              fontSize: 16,
                                              fontWeight: FontWeight.w700),
                                        ),
                                        Icon(
                                          scrumpassDetail
                                              ? Icons.keyboard_arrow_up_rounded
                                              : Icons.keyboard_arrow_down_rounded,
                                          size: 30,
                                          color: const Color(0xff7A8694),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                                ExpandedSection(
                                  expand: scrumpassDetail,
                                  child: Padding(
                                    padding: const EdgeInsets.only(bottom: 20.0),
                                    child: Text(
                                      AppLocalizations.of(context)
                                          .aboutUsContent
                                          .replaceAll('Common.appName', Common.appName),
                                      style: AppStyles.body.copyWith(
                                          color: const Color(0xff666666),
                                          fontSize: 14,
                                          fontWeight: FontWeight.w400),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                            child: Column(
                              children: [
                                GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      appIntroductionDetail = !appIntroductionDetail;
                                    });
                                  },
                                  child: Container(
                                    color: Colors.transparent,
                                    margin: const EdgeInsets.symmetric(vertical: 8),
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        Expanded(
                                          child: Text(
                                            AppLocalizations.of(context).introduceTitle,
                                            style: AppStyles.body.copyWith(
                                                color: const Color(0xff3D3D3D),
                                                fontSize: 16,
                                                fontWeight: FontWeight.w700),
                                          ),
                                        ),
                                        Icon(
                                          appIntroductionDetail
                                              ? Icons.keyboard_arrow_up_rounded
                                              : Icons.keyboard_arrow_down_rounded,
                                          size: 30,
                                          color: const Color(0xff7A8694),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                                ExpandedSection(
                                  expand: appIntroductionDetail,
                                  child: /* Text(
                                        AppLocalizations.of(context)
                                            .introduceDescription
                                            .replaceAll('Common.appName',
                                                Common.appName),
                                        style: AppStyles.body.copyWith(
                                            color: const Color(0xff666666),
                                            fontSize: 14,
                                            fontWeight: FontWeight.w400),
                                      ) */
                                      Html(
                                    data: AppLocalizations.of(context)
                                        .introduceDescription
                                        .replaceAll('Common.appName', Common.appName),
                                    style: {
                                      "body": Style(
                                          color: const Color(0xff666666),
                                          lineHeight: LineHeight.number(1)),
                                      'b': Style(
                                          fontFamily: 'Roboto',
                                          margin: Margins.zero,
                                          padding: HtmlPaddings.zero,
                                          fontSize: FontSize(14),
                                          color: const Color(0xff1F1F1F),
                                          fontWeight: FontWeight.w600)
                                    },
                                  ),
                                ),
                              ],
                            ),
                          )
                        ]),
                      ),
                    ),
                  ),
                ],
              ),
            )));
  }
}

sendMessage(String phone, String body) async {
  String urlsms = '';
  if (Platform.isAndroid) {
    //FOR Android
    urlsms = 'sms:$phone?body=$body';
  } else if (Platform.isIOS) {
    //FOR IOS
    urlsms = 'sms:$phone&body=$body';
  }
  await launch(urlsms);
}

class ExpandedSection extends StatefulWidget {
  final Widget child;
  final bool expand;
  ExpandedSection({this.expand = false, required this.child});

  @override
  _ExpandedSectionState createState() => _ExpandedSectionState();
}

class _ExpandedSectionState extends State<ExpandedSection> with SingleTickerProviderStateMixin {
  late AnimationController expandController;
  late Animation<double> animation;

  @override
  void initState() {
    super.initState();
    prepareAnimations();
    _runExpandCheck();
    expandController.duration = const Duration(milliseconds: 500);
  }

  ///Setting up the animation
  void prepareAnimations() {
    expandController = AnimationController(vsync: this, duration: const Duration(milliseconds: 0));
    animation = CurvedAnimation(
      parent: expandController,
      curve: Curves.fastOutSlowIn,
    );
  }

  void _runExpandCheck() {
    if (widget.expand) {
      expandController.forward();
    } else {
      expandController.reverse();
    }
  }

  @override
  void didUpdateWidget(ExpandedSection oldWidget) {
    super.didUpdateWidget(oldWidget);
    _runExpandCheck();
  }

  @override
  void dispose() {
    expandController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizeTransition(axisAlignment: 1.0, sizeFactor: animation, child: widget.child);
  }
}
