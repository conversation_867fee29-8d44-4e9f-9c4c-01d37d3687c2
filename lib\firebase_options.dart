// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyApm5XEgPAwanVcu_viS6Yspsj4tXvlGT0',
    appId: '1:45860944130:android:b931db118dd2283afa6a1b',
    messagingSenderId: '45860944130',
    projectId: 'scrumpass-psm-exam-simulator',
    storageBucket: 'scrumpass-psm-exam-simulator.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDflQYyM9t845RNx0mdjlBaXOt7-SPIZ_U',
    appId: '1:45860944130:ios:9dc32030cf23947bfa6a1b',
    messagingSenderId: '45860944130',
    projectId: 'scrumpass-psm-exam-simulator',
    storageBucket: 'scrumpass-psm-exam-simulator.appspot.com',
    iosClientId: '45860944130-q6t1pngt99haeajvdv198mkmbsk6ind0.apps.googleusercontent.com',
    iosBundleId: 'com.scrumpass.psm',
  );
}
