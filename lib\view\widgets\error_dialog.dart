import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:psm_app/core/core.dart';
import 'package:psm_app/localization.dart';

import '../../data_sources/api_servies.dart';

class ErrorDialog extends StatelessWidget {
  const ErrorDialog({Key? key, this.error = '', this.getList = false})
      : super(key: key);
  final String error;
  final bool getList;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SingleChildScrollView(
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            color: Colors.white,
          ),
          child: Padding(
            padding: const EdgeInsets.fromLTRB(16, 30, 16, 24),
            child: Column(
              children: [
                Container(
                  width: 196,
                  height: 139,
                  decoration: BoxDecoration(
                      image: DecorationImage(
                    image: AssetImage("assets/images/error_dialog.png"),
                    fit: BoxFit.fill,
                  )),
                ),
                Material(
                  child: Text(
                    AppLocalizations.of(context).errorDialog,
                    style: AppStyles.dialogText,
                    textAlign: TextAlign.center,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 24),
                  child: Container(
                    width: MediaQuery.of(context).size.width * 0.81,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          width: MediaQuery.of(context).size.width * 0.81,
                          child: ElevatedButton(
                            onPressed: () {
                              Navigator.of(context).pop();
                              if (error != '') {
                                final data = {
                                  'type': 'Runtime Error',
                                  'error': error
                                };
                                ApiServices().sendErrorReport(jsonEncode(data));
                                if (getList) ApiServices().getListQuiz();
                              }
                            },
                            child: Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Text(
                                "OK",
                                style: AppStyles.primaryButton,
                              ),
                            ),
                            style: ElevatedButton.styleFrom(
                              foregroundColor: AppColors.white,
                              backgroundColor: AppColors.greenPrimary,
                              shadowColor: Color.fromARGB(92, 0, 166, 144),
                              elevation: 0,
                              minimumSize: Size(20, 44),
                              side: BorderSide(
                                  color: AppColors.greenPrimary,
                                  width: 1.0,
                                  style: BorderStyle.solid),
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(4.0)),
                            ),
                          ),
                        )
                      ],
                    ),
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
