import 'package:percent_indicator/percent_indicator.dart';
import 'package:psm_app/core/app_colors.dart';
import 'package:psm_app/core/core.dart';
import 'package:flutter/material.dart';
import 'package:psm_app/localization.dart';

class ResultListChartWidget extends StatefulWidget {
  final double percent;
  final String status;
  const ResultListChartWidget(
      {Key? key, required this.percent, required this.status})
      : super(key: key);

  @override
  _ResultListChartWidgetState createState() => _ResultListChartWidgetState();
}

class _ResultListChartWidgetState extends State<ResultListChartWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  @override
  dispose() {
    _controller.dispose(); // you need this
    super.dispose();
  }

  void _initAnimation() {
    _controller = AnimationController(
        vsync: this,
        duration: Duration(seconds: widget.percent >= 0.5 ? 2 : 1));
    _animation =
        Tween<double>(begin: 0.0, end: widget.percent).animate(_controller);
    _controller.forward();
  }

  @override
  void initState() {
    _initAnimation();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, _) => CircularPercentIndicator(
        radius: 40,
        lineWidth: 4,
        percent: _animation.value,
        backgroundColor: widget.status == "Fail"
            ? AppColors.resultListBgChartFail
            : AppColors.resultListBgChartWin,
        progressColor: widget.status == "Fail"
            ? AppColors.resultListProgessChartFail
            : AppColors.resultListProgessChartWin,
        circularStrokeCap: CircularStrokeCap.round,
        center: Text.rich(
          TextSpan(
              text: "${(_animation.value * 100).toInt()}",
              style: AppStyles.body.copyWith(
                  fontWeight: FontWeight.w600,
                  fontSize: 10,
                  color: widget.status == "Fail"
                      ? const Color(0xffAF3610)
                      : const Color(0xff219653)),
              children: [
                TextSpan(
                    text: "%",
                    style: AppStyles.body.copyWith(
                        fontWeight: FontWeight.w600,
                        fontSize: 10,
                        color: widget.status == "Fail"
                            ? const Color(0xffAF3610)
                            : const Color(0xff219653)))
              ]),
        ),
      ),
    );
  }
}
