import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:psm_app/globals.dart';
import 'package:psm_app/helper/helper.dart';

import '../../core/core.dart';
import '../../localization.dart';

class Description extends StatefulWidget {
  Description(
      {Key? key,
      required this.description,
      required this.wrong,
      required this.questionid,
      required this.callback,
      required this.index,
      required this.type,
      required this.deleteMode})
      : super(key: key);

  final String description;
  final String questionid;
  final int index;
  final int wrong;
  final int type;
  final bool deleteMode;
  Function(int, String, int) callback;

  @override
  _VariableSizeContainerExampleState createState() =>
      _VariableSizeContainerExampleState();
}

class _VariableSizeContainerExampleState extends State<Description>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  bool opened = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _controller,
      curve: Curves.fastLinearToSlowEaseIn,
    );
  }

  @override
  dispose() {
    _controller.dispose(); // you need this
    super.dispose();
  }

  _onTapDown(TapDownDetails details) {
    var x = details.globalPosition.dx;
    var y = details.globalPosition.dy;
    // or user the local position method to get the offset
    //print(details.localPosition);
    //print("tap down " + x.toString() + ", " + y.toString());
    final RenderBox renderBox =
        keyText.currentContext?.findRenderObject() as RenderBox;

    final Size size = renderBox.size;
    if (_animation.status != AnimationStatus.completed) {
      _controller.forward();
      setState(() {
        opened = true;
      });
      widget.callback(widget.index, (y + size.height).toString(), widget.type);
      logEvent("review_question_explanation_click",
          {"question_id": widget.questionid});
    } else {
      _controller.animateBack(0, duration: Duration(milliseconds: 300));
      Future.delayed(const Duration(milliseconds: 300), () {
        setState(() {
          opened = false;
        });
      });
    }
  }

  _toggleContainer() {
    if (_animation.status != AnimationStatus.completed) {
      _controller.forward();
      setState(() {
        opened = true;
      });
      logEvent("review_question_explanation_click",
          {"question_id": widget.questionid});
    } else {
      _controller.animateBack(0, duration: Duration(milliseconds: 300));
      Future.delayed(const Duration(milliseconds: 300), () {
        setState(() {
          opened = false;
        });
      });
    }
  }

  final keyText = GlobalKey();
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizeTransition(
          sizeFactor: _animation,
          axis: Axis.vertical,
          child: Container(
              key: keyText,
              child: Column(
                children: [
                  SizedBox(height: 8.0),
                  Html(
                    data: Helper().parseHtmlString(widget.description),
                    style: {
                      "body": Style(
                          margin: Margins.zero,
                          padding: HtmlPaddings.zero,
                          fontWeight: FontWeight.w500,
                          lineHeight: LineHeight.number(1.5))
                    },
                  ),
                ],
              )),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            if (widget.wrong != 0) ...{
              Text(
                AppLocalizations.of(context)
                    .numberWrong
                    .replaceAll("{e}", widget.wrong.toString()),
                style: AppStyles.questionText,
              )
            } else ...{
              Container()
            },
            GestureDetector(
              onTapDown: (TapDownDetails details) => _onTapDown(details),
              child: /* TextButton(
                style: ButtonStyle(
                    alignment: Alignment.centerRight,
                    overlayColor: MaterialStateProperty.all(Colors.transparent),
                    padding: MaterialStateProperty.all<EdgeInsets>(
                        EdgeInsets.all(3))),
                onPressed: () {
                  //widget.deleteMode ? null : {_toggleContainer()};
                }, */
                  /* child: */ opened == false
                      ? Padding(
                          padding: const EdgeInsets.symmetric(vertical: 15.0),
                          child: Text(
                            AppLocalizations.of(context).viewExplanation,
                            style: AppStyles.bodyBold.copyWith(
                                color: widget.deleteMode
                                    ? Color(0xffB3B3B3)
                                    : AppColors.greenPrimary,
                                fontSize: 14),
                          ),
                        )
                      : Padding(
                          padding: const EdgeInsets.symmetric(vertical: 15.0),
                          child: Text(
                            AppLocalizations.of(context).hide,
                            style: AppStyles.bodyBold.copyWith(
                                color: widget.deleteMode
                                    ? Color(0xffB3B3B3)
                                    : AppColors.greenPrimary,
                                fontSize: 14),
                          ),
                        ),
            ),
            /* ), */
          ],
        )
      ],
    );
  }
}
