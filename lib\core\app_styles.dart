import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:google_fonts/google_fonts.dart';

import 'app_colors.dart';

String noto = 'roboto';

class AppStyles {
  static final TextStyle title = GoogleFonts.roboto(
    color: AppColors.white,
    fontSize: 20,
    fontWeight: FontWeight.w400,
  );

  static final TextStyle titleBold = GoogleFonts.roboto(
    color: AppColors.white,
    fontSize: 20,
    fontWeight: FontWeight.w600,
  );

  static final TextStyle heading = GoogleFonts.roboto(
    color: AppColors.black,
    fontSize: 18,
    fontWeight: FontWeight.w600,
  );

  static final TextStyle heading40 = GoogleFonts.roboto(
    color: AppColors.black,
    fontSize: 40,
    fontWeight: FontWeight.w600,
  );

  static final TextStyle heading40White = GoogleFonts.roboto(
    color: AppColors.white,
    fontSize: 40,
    fontWeight: FontWeight.w600,
  );

  static final TextStyle heading15 = GoogleFonts.roboto(
    color: AppColors.black,
    fontSize: 15,
    fontWeight: FontWeight.w600,
  );

  static final TextStyle body = GoogleFonts.roboto(
    color: AppColors.grey,
    fontSize: 13,
    fontWeight: FontWeight.normal,
  );

  static final TextStyle bodyWhite = GoogleFonts.roboto(
    color: AppColors.white,
    fontSize: 13,
    fontWeight: FontWeight.normal,
  );

  static final TextStyle bodyBold = GoogleFonts.roboto(
    color: AppColors.grey,
    fontSize: 13,
    fontWeight: FontWeight.bold,
  );

  static final TextStyle bodylightGrey = GoogleFonts.roboto(
    color: AppColors.lightGreen,
    fontSize: 13,
    fontWeight: FontWeight.normal,
  );
  static final TextStyle bodyDarkGreen = GoogleFonts.roboto(
    color: AppColors.darkGreen,
    fontSize: 13,
    fontWeight: FontWeight.w500,
  );

  static final TextStyle bodyDarkRed = GoogleFonts.roboto(
    color: AppColors.darkRed,
    fontSize: 13,
    fontWeight: FontWeight.w500,
  );

  static final TextStyle body20 = GoogleFonts.roboto(
    color: AppColors.grey,
    fontSize: 20,
    fontWeight: FontWeight.normal,
  );
  static final TextStyle body18 = GoogleFonts.roboto(
    color: AppColors.grey,
    fontSize: 18,
    fontWeight: FontWeight.normal,
  );
  static final TextStyle bodyLightGrey20 = GoogleFonts.roboto(
    color: AppColors.lightGrey,
    fontSize: 20,
    fontWeight: FontWeight.normal,
  );

  static final TextStyle bodyWhite20 = GoogleFonts.roboto(
    color: AppColors.white,
    fontSize: 20,
    fontWeight: FontWeight.normal,
  );
  static final TextStyle body11 = GoogleFonts.roboto(
    color: AppColors.grey,
    fontSize: 11,
    fontWeight: FontWeight.normal,
  );

  //font test

  static final TextStyle examHeading12 = GoogleFonts.roboto(
    color: AppColors.blackText,
    fontSize: 12,
    fontWeight: FontWeight.w600,
  );

  static final TextStyle examBodyBold = GoogleFonts.roboto(
    color: AppColors.grey,
    fontSize: 13,
    fontWeight: FontWeight.bold,
  );
  static final TextStyle examBody = GoogleFonts.roboto(
    color: AppColors.grey,
    fontSize: 13,
    fontWeight: FontWeight.normal,
  );

  //Home page
  static final TextStyle homeHeading = GoogleFonts.roboto(
    color: AppColors.homeHeading,
    fontSize: 24,
    fontWeight: FontWeight.w500,
  );

  static final TextStyle homeAppOverviewTitle = GoogleFonts.roboto(
    color: AppColors.blackText,
    fontSize: 18,
    fontWeight: FontWeight.bold,
    height: 1.5,
  );

  static final TextStyle homeAppOverviewContent = GoogleFonts.roboto(
    color: AppColors.lightGreyText,
    fontSize: 14,
    fontWeight: FontWeight.w500,
  );

  static final TextStyle homeCardTitle = GoogleFonts.roboto(
    color: AppColors.darkBlueText,
    fontSize: 16,
    fontWeight: FontWeight.w600,
  );

  static final TextStyle homeScoreCardTitle = GoogleFonts.roboto(
    color: AppColors.white,
    fontSize: 18,
    fontWeight: FontWeight.w700,
  );

  static final TextStyle homeScoreCardText = GoogleFonts.roboto(
    color: AppColors.scoreCardText,
    fontSize: 12,
    fontWeight: FontWeight.w500,
  );

  static final TextStyle appBarTitle = GoogleFonts.roboto(
    color: AppColors.blackText,
    fontSize: 18,
    fontWeight: FontWeight.w700,
  );

  static final TextStyle listQuizTitle = GoogleFonts.roboto(
    color: AppColors.blackText,
    fontSize: 16,
    fontWeight: FontWeight.w700,
  );

  static final TextStyle listQuizInfo = GoogleFonts.roboto(
    color: AppColors.lightGreyText,
    fontSize: 13,
    fontWeight: FontWeight.w500,
  );

  static final TextStyle listPass = GoogleFonts.roboto(
    color: AppColors.greenText,
    fontSize: 12,
    fontWeight: FontWeight.w700,
  );

  static final TextStyle listFail = GoogleFonts.roboto(
    color: AppColors.redText,
    fontSize: 12,
    fontWeight: FontWeight.w700,
  );

  static final TextStyle secondaryButton = GoogleFonts.roboto(
    color: AppColors.greenPrimary,
    fontSize: 16,
    fontWeight: FontWeight.w600,
  );

  static final TextStyle primaryButton = GoogleFonts.roboto(
    color: AppColors.white,
    fontSize: 16,
    fontWeight: FontWeight.w600,
  );

  static final TextStyle questionText = GoogleFonts.roboto(
    color: AppColors.blackText,
    fontSize: 14,
    height: 1.57,
  );

  static final TextStyle correctAnswerText = GoogleFonts.roboto(
    color: AppColors.greenText,
    fontSize: 14,
    fontWeight: FontWeight.w500,
    height: 1.57,
  );

  static final TextStyle incorrectAnswerText = GoogleFonts.roboto(
    color: AppColors.redAnswerText,
    fontSize: 14,
    fontWeight: FontWeight.w500,
    height: 1.57,
  );

  static final TextStyle titleBody16 = GoogleFonts.roboto(
    color: AppColors.lightGreyText,
    fontSize: 16,
    fontWeight: FontWeight.w500,
  );

  static final TextStyle titleScoreCardResult = GoogleFonts.roboto(
    color: AppColors.homeHeading,
    fontSize: 18,
    fontWeight: FontWeight.w700,
  );

  static final TextStyle textScoreCardResult = GoogleFonts.roboto(
    color: AppColors.blackText,
    fontSize: 16,
    fontWeight: FontWeight.w600,
  );

  static final TextStyle reportDialogTitle = GoogleFonts.roboto(
    color: AppColors.blackText,
    fontSize: 16,
    fontWeight: FontWeight.w700,
  );

  static final TextStyle reportDialogText = GoogleFonts.roboto(
    color: AppColors.blackText,
    fontSize: 14,
    fontWeight: FontWeight.w500,
  );

  static final TextStyle dialogText = GoogleFonts.roboto(
    color: AppColors.blackText,
    fontSize: 16,
    fontWeight: FontWeight.w500,
  );

  static final TextStyle textWeight500 = GoogleFonts.roboto(
    color: AppColors.blackText,
    fontWeight: FontWeight.w500,
  );

  static final TextStyle textWeight700 = GoogleFonts.roboto(
    color: AppColors.blackText,
    fontWeight: FontWeight.w700,
  );
}

Map<String, Style> optionHtmlStyle(BuildContext context, Color color) {
  return {
    "body": Style(
        fontSize: FontSize(14),
        fontFamily: 'Inter',
        color: color,
        margin: Margins.zero,
        padding: HtmlPaddings.zero,
        lineHeight: const LineHeight(1.5)),
    "img": Style(width: Width(MediaQuery.of(context).size.width * 0.5, Unit.px))
  };
}
