import 'package:flutter/material.dart';
import 'package:psm_app/core/core.dart';
import 'package:psm_app/localization.dart';

class SuccessDialog extends StatelessWidget {
  final String text;
  const SuccessDialog({Key? key, required this.text}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: EdgeInsets.symmetric(horizontal: 16),
      child: SingleChildScrollView(
        child: Container(
          width: double.infinity,
          padding: EdgeInsets.all(16),
          child: Column(children: [
            Padding(
              padding: const EdgeInsets.only(top: 30),
              child: Container(
                width: 196,
                height: 139,
                decoration: BoxDecoration(
                    image: DecorationImage(
                  image: AssetImage("assets/images/dialog_success.png"),
                  fit: BoxFit.fill,
                )),
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: Container(
                child: Text(
                  text,
                  textAlign: TextAlign.center,
                  style: AppStyles.dialogText,
                ),
              ),
            ),
            Container(
              margin: const EdgeInsets.only(left: 0, right: 0, bottom: 8),
              width: MediaQuery.of(context).size.width,
              child: Padding(
                padding: const EdgeInsets.only(left: 0),
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 30),
                    child: Text(
                      "OK",
                      style: AppStyles.primaryButton,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    foregroundColor: AppColors.white, backgroundColor: AppColors.greenPrimary,
                    shadowColor: Color.fromARGB(92, 0, 166, 144),
                    elevation: 0,
                    minimumSize: Size(20, 44),
                    side: BorderSide(
                        color: AppColors.greenPrimary,
                        width: 1.0,
                        style: BorderStyle.solid),
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(4.0)),
                  ),
                ),
              ),
            )
          ]),
        ),
      ),
    );
  }
}
