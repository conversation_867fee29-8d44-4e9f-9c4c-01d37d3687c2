import 'package:psm_app/core/core.dart';
/* import 'package:dev_quiz/home/<USER>/chart/chart_widget.dart'; */
import 'package:flutter/material.dart';
import 'package:psm_app/localization.dart';

class ScoreCardWidget extends StatelessWidget {
  final double percent;
  const ScoreCardWidget({Key? key, required this.percent}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 16, right: 16),
      child: Container(
        height: 136,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
              child: Padding(
                padding: const EdgeInsets.only(),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(AppLocalizations.of(context).appTitle,
                        style: AppStyles.homeAppOverviewTitle),
                    Padding(
                      padding: const EdgeInsets.only(top: 5),
                      child: Text(AppLocalizations.of(context).appDescription,
                          style: AppStyles.homeAppOverviewContent),
                    ),
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
