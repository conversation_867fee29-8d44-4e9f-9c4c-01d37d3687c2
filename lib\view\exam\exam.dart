import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter_countdown_timer/countdown_timer_controller.dart';
import 'package:flutter_countdown_timer/current_remaining_time.dart';
import 'package:flutter_countdown_timer/flutter_countdown_timer.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:intl/intl.dart';
import 'package:psm_app/core/app_colors.dart';
import 'package:psm_app/core/app_styles.dart';
import 'package:psm_app/data_sources/api_servies.dart';
import 'package:psm_app/globals.dart';
import 'package:psm_app/localization.dart';
import 'package:psm_app/models/question_api_model.dart';
import 'package:psm_app/models/result_model.dart';
import 'package:psm_app/view/exam/exam_result.dart';
import 'package:psm_app/view/exam/new_quiz_list.dart';
import 'package:psm_app/view/result/new_result.dart';
import 'package:psm_app/view/widgets/error_dialog.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:collection/collection.dart';
import 'package:psm_app/helper/helper.dart';
import 'package:http/http.dart' as http;

import '../home.dart';

class Exam extends StatefulWidget {
  final String idQuiz;
  final String quizName;
  final String questionSelection;
  final String exam_quiz;
  final int passPercent;
  final int duration; // minutes
  final bool resume;
  const Exam(
      {Key? key,
      required this.idQuiz,
      required this.exam_quiz,
      this.questionSelection = "1",
      required this.quizName,
      required this.passPercent,
      required this.duration,
      this.resume = false})
      : super(key: key);

  @override
  _ExamState createState() => _ExamState();
}

class _ExamState extends State<Exam> with WidgetsBindingObserver {
  int _currentIndex = 0;
  var _answers = Map<int, dynamic>();
  var _answersText = Map<int, dynamic>();
  var _bookmarks = Map<int, dynamic>();
  List<int> bookmarksIndex = [];
  List<Question> question = [];
  late ScrollController _scrollControllerOptions;
  late ScrollController _scrollControllerQuestion;
  var _multiSelect = Map<int, dynamic>();
  late Future<String> futureLocalData;
  var bookmarkList;
  int remainTime = 0; // seconds
  int timeDoQuiz = 0; // seconds
  double valueProgress = 0;
  Timer? _timer;
  bool internet = false;
  Map _answersId = {};
  String endQuizTime = '';
  final List alphabet = [
    'A. ',
    'B. ',
    'C. ',
    'D. ',
    'E. ',
    'F. ',
    'G. ',
    'H. ',
    'I.',
    'K. '
  ];
  List<Question> incorrectQuestion = [];
  List<Question> bookmarkQuestion = [];
  int endTime = 0;
  int startTime = 0;
  late CountdownTimerController controller;
  bool showModal = false;

  void _initMultiSelect() {
    var indexQuestion = 0;
    for (var element in question) {
      if (element.type == "multi") {
        Map<String, bool> temp = {};
        for (var index = 0; index < element.options!.length; index++) {
          temp[element.options?[index].oid ?? ""] = false;
        }
        _answers[indexQuestion] = temp;
        _answersText[indexQuestion] = [];
        _answersId[indexQuestion] = [];
      } else {
        _answers[indexQuestion] = "";
        _answersText[indexQuestion] = "";
        _answersId[indexQuestion] = [];
      }
      indexQuestion++;
    }
  }

  void _saveResult(String name, status, percentage, time, correctList) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    // Fetch and decode data
    final String resultString = prefs.getString('result') ?? "";
    Map data = {
      'name': name,
      'status': status,
      'percentage': percentage,
      'quizId': widget.idQuiz,
      'score_individual': correctList,
      'environment': environment.text
    };
    _sendResult(data);
    //print(user_list);
    //print(list);
    if (resultString != "") {
      final listResult = Result.decode(resultString);
      listResult.add(Result(
          name: name,
          status: status,
          percentage: percentage,
          exam_quiz: widget.exam_quiz,
          time: time,
          questions: question,
          quizType: widget.questionSelection.toString(),
          answers: _answers.values.toList(),
          answersText: _answersText.values.toList(),
          passPercent: widget.passPercent,
          quizId: widget.idQuiz,
          bookmark: _bookmarks.values.toList(),
          quizDuration: widget.duration,
          environment: environment.text,
          shuffleOptions: true,
          timeDoQuiz: timeDoQuiz));
      if (listResult.isNotEmpty) {
        await prefs.setString('result', Result.encode(listResult));
      }
    } else {
      final String encodedData = Result.encode([
        Result(
          name: name,
          status: status,
          percentage: percentage,
          time: time,
          questions: question,
          exam_quiz: widget.exam_quiz,
          quizType: widget.questionSelection.toString(),
          answers: _answers.values.toList(),
          answersText: _answersText.values.toList(),
          passPercent: widget.passPercent,
          quizId: widget.idQuiz,
          environment: environment.text,
          bookmark: _bookmarks.values.toList(),
          quizDuration: widget.duration,
          timeDoQuiz: timeDoQuiz,
          shuffleOptions: true,
        )
      ]);
      if (encodedData.isNotEmpty) {
        await prefs.setString('result', encodedData);
      }
    }
  }

  Future<void> _sendResult(data) async {
    List scoreIndividual = [];
    List questionId = [];
    var _correctList = data['score_individual'].values.toList();
    int correct = 0;
    _correctList.forEach((element) {
      if (element == true) {
        scoreIndividual.add(1);
        correct++;
      } else {
        scoreIndividual.add(2);
      }
    });
    question.forEach((element) {
      questionId.add(element.id);
    });
    data['username'] = Common.username;
    data['appid'] = Common.appid;
    data['app_version'] = Common.version;
    data['score_individual'] = scoreIndividual.join(',');
    data['start_time'] = startTime;
    data['end_time'] = (DateTime.now().millisecondsSinceEpoch / 1000).toInt();
    data['r_qids'] = questionId;
    data['score_obtained'] = correct;
    data['total_time'] = timeDoQuiz;
    data['answers_id'] = _answersId.values.toList();
    data['app_username'] = Common.username;
    data['bookmark_index'] = bookmarksIndex.join(",");
    data['device_id'] = Common.deviceId;
    Common.premium ? data['premium'] = "1" : data['premium'] = "0";
    var sendResult = await ApiServices().sendResult(jsonEncode(data));
    if (sendResult == false) {
      _saveDataToSendLater(jsonEncode(data));
    }
  }

  void _saveDataToSendLater(data) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    List<String> sendLater = prefs.getStringList('sendLater') ?? [];
    sendLater.add(data);
    prefs.setStringList('sendLater', sendLater);
    print('save data to send later');
  }

  void _handleNext() {
    if (_bookmarks[_currentIndex] == null) {
      _bookmarks[_currentIndex] = 0;
    }
    if (_answers[_currentIndex] == '') {
      _showAlertDialog();
      return;
    } else if (question[_currentIndex].type == 'multi') {
      if (_answers[_currentIndex].values.toList().contains(true) == false) {
        _showAlertDialog();
        return;
      }
    }
    if (_currentIndex < (question.length - 1)) {
      setState(() {
        _saveLocal();
        _currentIndex++;
      });
    } else {
      _showConfirmDialog();
    }
    _scrollControllerOptions
        .jumpTo(_scrollControllerOptions.position.minScrollExtent);
    _scrollControllerQuestion
        .jumpTo(_scrollControllerQuestion.position.minScrollExtent);
  }

  void _endQuiz(bool force) {
    int correct = 0;
    double percent = 0;
    var correctQuestion;
    late Map correctList = {};
    if (force) {
      timeDoQuiz = timeDoQuiz + 1;
    }
    _answers.forEach((index, value) {
      if (question[index].type == "single") {
        correctQuestion = question[index].correct;
        if (correctQuestion == value) {
          correctList[index] = true;
          correct++;
        } else {
          correctList[index] = false;
          question[index].quizId = widget.idQuiz;
          question[index].environment = environment.text;
          incorrectQuestion.add(question[index]);
        }
      } else if (question[index].type == "multi") {
        correctList[index] = true;
        int selectedCount = 0;
        if (_answers[index] is Map) {
          final arr = _answers[index] as Map;
          arr.forEach((key, value) {
            if (value) selectedCount++;
          });
        }
        // for (var val in _answers[index]) {
        //   if (val) selectedCount++;
        // }
        var correctIds = question[index].correct?.split(',');
        for (int i = 0; i < (correctIds?.length ?? 0); i++) {
          if (_answers[index][correctIds?[i]] == false ||
              correctIds?.length != selectedCount) {
            correctList[index] = false;
            question[index].quizId = widget.idQuiz;
            question[index].environment = environment.text;
            incorrectQuestion.add(question[index]);
            break;
          }
        }
        if (correctList[index] == true) correct++;
      }
      if (_bookmarks[index] == 1) {
        question[index].quizId = widget.idQuiz;
        question[index].environment = environment.text;
        bookmarkQuestion.add(question[index]);
        bookmarksIndex.add(index);
      }
    });
    saveBookmarkQuestion();
    saveIncorrectQuestion();
    percent = (correct / question.length) * 100;
    String status = percent >= widget.passPercent ? "Pass" : "Fail";
    logEvent("exam_take_finish_click", {
      "test_name": widget.quizName,
      "test_result": status,
      "test_percent": percent.toString() + "%"
    });
    DateTime now = DateTime.now();
    String formattedDate = DateFormat('dd/MM/yyyy').format(now);
    endQuizTime = formattedDate;
    _saveResult(widget.quizName, status, percent.toString(), formattedDate,
        correctList);
    _clearLocal();
  }

  saveIncorrectQuestion() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    final localIncorrectString = prefs.getString('incorrectQuestion') ?? "";
    final localIncorrect =
        localIncorrectString != "" ? jsonDecode(localIncorrectString) : [];
    List newListQuestion = [...localIncorrect, ...incorrectQuestion];
    prefs.setString('incorrectQuestion', jsonEncode(newListQuestion));
  }

  saveBookmarkQuestion() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    final localBookmarkString = prefs.getString('bookmarkQuestion') ?? "";
    final localBookmark =
        localBookmarkString != "" ? jsonDecode(localBookmarkString) : [];
    List newListBookmark = [...localBookmark, ...bookmarkQuestion];
    prefs.setString('bookmarkQuestion', jsonEncode(newListBookmark));
  }

  void _showAlertDialog() {
    showDialog<String>(
      context: context,
      builder: (BuildContext context) => Dialog(
        // contentPadding: EdgeInsets.fromLTRB(16, 30, 16, 24),
        insetPadding: const EdgeInsets.symmetric(horizontal: 16),
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.fromLTRB(16, 30, 16, 24),
            child: Column(
              children: [
                Container(
                  width: 196,
                  height: 139,
                  decoration: const BoxDecoration(
                      image: DecorationImage(
                    image: AssetImage("assets/images/info_dialog.png"),
                    fit: BoxFit.fill,
                  )),
                ),
                Text(
                  AppLocalizations.of(context).mustSelectAnswer,
                  style: AppStyles.dialogText,
                  textAlign: TextAlign.center,
                ),
                Padding(
                  padding: const EdgeInsets.only(
                    top: 24,
                  ),
                  child: Container(
                    width: MediaQuery.of(context).size.width,
                    child: Row(
                      children: [
                        Expanded(
                            child: ElevatedButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                          },
                          child: Text(
                            "OK",
                            style: AppStyles.primaryButton,
                          ),
                          style: ElevatedButton.styleFrom(
                            foregroundColor: AppColors.white,
                            backgroundColor: AppColors.greenPrimary,
                            shadowColor: const Color.fromARGB(92, 0, 166, 144),
                            elevation: 0,
                            minimumSize: const Size(20, 44),
                            side: BorderSide(
                                color: AppColors.greenPrimary,
                                width: 1.0,
                                style: BorderStyle.solid),
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(4.0)),
                          ),
                        ))
                      ],
                    ),
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showConfirmDialog() {
    showDialog<String>(
      context: context,
      builder: (BuildContext context) => Dialog(
        // contentPadding: EdgeInsets.fromLTRB(16, 30, 16, 24),
        insetPadding: const EdgeInsets.symmetric(horizontal: 16),
        child: SingleChildScrollView(
          child: ConstrainedBox(
            constraints: new BoxConstraints(
              maxWidth: 600.0,
            ),
            child: Padding(
              padding: const EdgeInsets.fromLTRB(16, 30, 16, 24),
              child: Column(
                children: [
                  Container(
                    width: 196,
                    height: 139,
                    decoration: const BoxDecoration(
                        image: DecorationImage(
                      image: AssetImage("assets/images/info_dialog.png"),
                      fit: BoxFit.fill,
                    )),
                  ),
                  Text(AppLocalizations.of(context).confirmEndQuiz,
                      style: AppStyles.dialogText, textAlign: TextAlign.center),
                  Padding(
                    padding: const EdgeInsets.only(top: 24),
                    child: Container(
                      width: MediaQuery.of(context).size.width,
                      child: Row(
                        children: [
                          Expanded(
                              child: Padding(
                            padding: const EdgeInsets.only(right: 6),
                            child: ElevatedButton(
                              onPressed: () {
                                Navigator.pop(context);
                              },
                              child: Text(
                                AppLocalizations.of(context).no,
                                style: AppStyles.secondaryButton,
                                textAlign: TextAlign.center,
                              ),
                              style: ElevatedButton.styleFrom(
                                foregroundColor: AppColors.greenPrimary,
                                backgroundColor: Colors
                                    .white, //specify the color of the button's text and icons as well as the overlay colors used to indicate the hover, focus, and pressed states
                                shadowColor: Colors
                                    .white, //specify the button's elevation color
                                elevation: 0, //buttons Material shadow
                                // padding: const EdgeInsets.only(top: 4.0, bottom: 4.0, right: 8.0, left: 8.0), //specify the button's Padding
                                minimumSize: const Size(20,
                                    44), //specify the button's first: width and second: height
                                side: BorderSide(
                                    color: AppColors.greenPrimary,
                                    width: 1.0,
                                    style: BorderStyle
                                        .solid), //set border for the button
                                shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(4.0)),
                              ),
                            ),
                          )),
                          Expanded(
                              child: Padding(
                            padding: const EdgeInsets.only(left: 6),
                            child: ElevatedButton(
                              onPressed: () {
                                _endQuiz(false);
                                Navigator.of(context).pop();
                                if (showModal) Navigator.pop(context);
                                Navigator.of(context).pushReplacement(
                                  MaterialPageRoute(
                                    builder: (context) => NewExamResult(
                                      answers: _answers,
                                      questions: question,
                                      answersText: _answersText,
                                      quizId: widget.idQuiz,
                                      exam_quiz: widget.exam_quiz,
                                      quizType:
                                          widget.questionSelection.toString(),
                                      quizName: widget.quizName,
                                      bookmark: _bookmarks,
                                      passPercent: widget.passPercent,
                                      time: endQuizTime,
                                      quizDuration: widget.duration,
                                      timeDoQuiz: timeDoQuiz,
                                      fromExam: true,
                                      shuffleOptions: true,
                                    ),
                                  ),
                                );
                              },
                              child: Text(
                                "OK",
                                style: AppStyles.primaryButton,
                              ),
                              style: ElevatedButton.styleFrom(
                                foregroundColor: AppColors.white,
                                backgroundColor: AppColors.greenPrimary,
                                shadowColor:
                                    const Color.fromARGB(92, 0, 166, 144),
                                elevation: 0,
                                minimumSize: const Size(20, 44),
                                side: BorderSide(
                                    color: AppColors.greenPrimary,
                                    width: 1.0,
                                    style: BorderStyle.solid),
                                shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(4.0)),
                              ),
                            ),
                          ))
                        ],
                      ),
                    ),
                  )
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _showSubmitDialog() {
    final text =
        _currentIndex == (question.length - 1) ? "submit" : "unaswered";
    showDialog<String>(
      context: context,
      builder: (BuildContext context) => Dialog(
        // contentPadding: EdgeInsets.fromLTRB(16, 30, 16, 24),
        insetPadding: const EdgeInsets.symmetric(horizontal: 16),
        child: SingleChildScrollView(
          child: ConstrainedBox(
            constraints: const BoxConstraints(
              maxWidth: 600.0,
            ),
            child: Padding(
              padding: const EdgeInsets.fromLTRB(16, 30, 16, 24),
              child: Column(
                children: [
                  Container(
                    width: 196,
                    height: 139,
                    decoration: const BoxDecoration(
                        image: DecorationImage(
                      image: AssetImage("assets/images/info_dialog.png"),
                      fit: BoxFit.fill,
                    )),
                  ),
                  Text(text,
                      style: AppStyles.dialogText, textAlign: TextAlign.center),
                  Padding(
                    padding: const EdgeInsets.only(top: 24),
                    child: SizedBox(
                      width: MediaQuery.of(context).size.width,
                      child: Row(
                        children: [
                          Expanded(
                              child: Padding(
                            padding: const EdgeInsets.only(right: 6),
                            child: ElevatedButton(
                              onPressed: () {
                                Navigator.pop(context);
                              },
                              style: ElevatedButton.styleFrom(
                                foregroundColor: AppColors.greenPrimary,
                                backgroundColor: Colors
                                    .white, //specify the color of the button's text and icons as well as the overlay colors used to indicate the hover, focus, and pressed states
                                shadowColor: Colors
                                    .white, //specify the button's elevation color
                                elevation: 0, //buttons Material shadow
                                // padding: const EdgeInsets.only(top: 4.0, bottom: 4.0, right: 8.0, left: 8.0), //specify the button's Padding
                                minimumSize: const Size(20,
                                    44), //specify the button's first: width and second: height
                                side: BorderSide(
                                    color: AppColors.greenPrimary,
                                    width: 1.0,
                                    style: BorderStyle
                                        .solid), //set border for the button
                                shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(4.0)),
                              ),
                              child: Text(
                                AppLocalizations.of(context).no,
                                style: AppStyles.secondaryButton,
                                textAlign: TextAlign.center,
                              ),
                            ),
                          )),
                          Expanded(
                              child: Padding(
                            padding: const EdgeInsets.only(left: 6),
                            child: ElevatedButton(
                              onPressed: () {
                                _endQuiz(true);
                                Navigator.of(context).pop();
                                if (showModal) Navigator.pop(context);
                                Navigator.of(context).pushReplacement(
                                  MaterialPageRoute(
                                    builder: (context) => NewExamResult(
                                      answers: _answers,
                                      questions: question,
                                      answersText: _answersText,
                                      quizId: widget.idQuiz,
                                      exam_quiz: widget.exam_quiz,
                                      quizType:
                                          widget.questionSelection.toString(),
                                      quizName: widget.quizName,
                                      bookmark: _bookmarks,
                                      passPercent: widget.passPercent,
                                      time: endQuizTime,
                                      quizDuration: widget.duration,
                                      timeDoQuiz: timeDoQuiz,
                                      fromExam: true,
                                      shuffleOptions: true,
                                    ),
                                  ),
                                );
                              },
                              style: ElevatedButton.styleFrom(
                                foregroundColor: AppColors.white,
                                backgroundColor: AppColors.greenPrimary,
                                shadowColor:
                                    const Color.fromARGB(92, 0, 166, 144),
                                elevation: 0,
                                minimumSize: const Size(20, 44),
                                side: BorderSide(
                                    color: AppColors.greenPrimary,
                                    width: 1.0,
                                    style: BorderStyle.solid),
                                shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(4.0)),
                              ),
                              child: Text(
                                AppLocalizations.of(context).yes,
                                style: AppStyles.primaryButton,
                              ),
                            ),
                          ))
                        ],
                      ),
                    ),
                  )
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _bookmark() {
    if (_bookmarks[_currentIndex] == 1) {
      _bookmarks[_currentIndex] = 0;
    } else {
      _bookmarks[_currentIndex] = 1;
    }
    _saveLocal();
    //print(_bookmarks);
  }

  Future<String> getContinue() async {
    String question = '';
    SharedPreferences prefs = await SharedPreferences.getInstance();
    if (prefs.getString('question') != null) {
      question = prefs.getString('question')!;
    }
    return question;
  }

  bool hasOptionAll(List<Options> options) {
    bool hasOptionAll = false;
    // Nếu có option chứa 1 trong các text này sẽ ko thực hiện trộn
    List containText = [
      'all of the above',
      'all of the answers listed',
      'all of the above options',
      'all of the answers are correct',
      'all of the answers are valid',
      'all provided answers are correct',
      'all answers are true',
      'all answers are false',
      'all of these',
      'all of these listed options',
      'all stated statements above',
      'all the above',
      'all of the answers',
      'all answers apply',
      'all of above',
      'all of the above are true',
      'all of above is true'
    ];
    for (var element in options) {
      final option = element.qOption.toLowerCase();
      hasOptionAll = containText.any((element) => option.contains(element));
      if (hasOptionAll) return true;
    }
    return false;
  }

  Future<String> _getLocal() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    var answerList;
    var answerTextList;
    var status;
    bool hasGroupQuestion = false;
    if (!widget.resume) {
      question = await ApiServices().fetchQuestion(widget.idQuiz);
      for (Question val in question) {
        if (val.hasGroupQuestion) hasGroupQuestion = true;
        break;
      }
      if (hasGroupQuestion == false) {
        question.shuffle();
      }
      if (Common.shuffleApp.contains(Common.appid)) {
        for (var val in question) {
          final cantShuffle = hasOptionAll(val.options ?? []);
          if (!cantShuffle) {
            val.options?.shuffle();
          }
        }
      }
      // prefs.setString('question', jsonEncode(question));
    } else {
      String questionString = prefs.getString('questionResume') ?? '';
      const JsonDecoder _decoder = JsonDecoder();
      final useListContainer = _decoder.convert(questionString);
      final List questionList = useListContainer;
      question = questionList.map((e) => Question.fromJson(e)).toList();
    }
    _initMultiSelect();
    if (prefs.getString('answered_' + widget.idQuiz) != null) {
      answerList =
          jsonDecode(prefs.getString('answered_' + widget.idQuiz) ?? '');
      answerTextList =
          jsonDecode(prefs.getString('answeredText_' + widget.idQuiz) ?? '');
    }
    if (prefs.getString('bookmarked_' + widget.idQuiz) != null) {
      bookmarkList =
          jsonDecode(prefs.getString('bookmarked_' + widget.idQuiz) ?? '');
    }
    if (prefs.getInt('timeDoQuiz_' + widget.idQuiz) != null) {
      timeDoQuiz = prefs.getInt('timeDoQuiz_' + widget.idQuiz) ?? 0;
      remainTime = widget.duration * 60 - timeDoQuiz;
    } else {
      remainTime = widget.duration * 60;
    }
    valueProgress = (1 / (widget.duration * 60)) * timeDoQuiz;
    if (answerList != null) {
      for (var index = 0; index < answerList.length; index++) {
        _answers[index] = answerList[index];
        _answersText[index] = answerTextList[index];
      }
    }
    if (bookmarkList != null) {
      for (var index = 0; index < bookmarkList.length; index++) {
        _bookmarks[index] = bookmarkList[index];
      }
    }
    if (prefs.getInt('timeStartQuiz_' + widget.idQuiz) != null) {
      startTime = prefs.getInt('timeStartQuiz_' + widget.idQuiz) ?? 0;
      print(startTime);
    } else {
      startTime = (DateTime.now().millisecondsSinceEpoch / 1000).toInt();
      prefs.setInt('timeStartQuiz_' + widget.idQuiz, startTime);
      print('save time start quiz ${startTime}');
    }
    if (prefs.getString('answeredId_' + widget.idQuiz) != null) {
      var listAnswersId =
          jsonDecode(prefs.getString('answeredId_' + widget.idQuiz) ?? '');
      for (int i = 0; i < listAnswersId.length; i++) {
        _answersId[i] = listAnswersId[i];
      }
    }
    status = 'done';
    return status;
  }

  void _saveLocal() async {
    //print(_answers);
    var data = _answers.values.toList();
    var data2 = _bookmarks.values.toList();
    var textData = _answersText.values.toList();
    var idData = _answersId.values.toList();
    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.setString('answered_' + widget.idQuiz, jsonEncode(data));
    prefs.setString('bookmarked_' + widget.idQuiz, jsonEncode(data2));
    prefs.setString('answeredText_' + widget.idQuiz, jsonEncode(textData));
    prefs.setString('answeredId_' + widget.idQuiz, jsonEncode(idData));
    _saveTimeDoQuiz();
    _addResumeQuiz();
    print('saved to local');
  }

  void _clearLocal() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.remove('answered_' + widget.idQuiz);
    prefs.remove('bookmarked_' + widget.idQuiz);
    prefs.remove('answeredText_' + widget.idQuiz);
    prefs.remove('timeDoQuiz_' + widget.idQuiz);
    prefs.remove('timeStartQuiz_' + widget.idQuiz);
    prefs.remove('answeredId_' + widget.idQuiz);
    prefs.remove('resumeQuiz');
    prefs.remove('questionResume');
    print('local clear');
  }

  void _selectCheckbox(index, id) {
    if (_answers[_currentIndex][id] == false) {
      _answers[_currentIndex][id] = true;
    } else {
      _answers[_currentIndex][id] = false;
    }
    if (_answersId[_currentIndex].contains(id)) {
      _answersId[_currentIndex].remove(id);
    } else {
      _answersId[_currentIndex].add(id);
    }
  }

  void _multiText(text) {
    if (_answersText[_currentIndex].contains(text)) {
      _answersText[_currentIndex].remove(text);
    } else {
      _answersText[_currentIndex].add(text);
    }
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (t) {
      setState(() {
        if (timeDoQuiz == widget.duration * 60 && widget.duration != 0) {
          t.cancel();
        } else {
          timeDoQuiz += 1;
          valueProgress += (1 / (widget.duration * 60));
          // print(valueProgress);
          // print('time do quiz: ${timeDoQuiz}');
          // print('remain time: ${remainTime - timeDoQuiz}');
        }
      });
    });
  }

  void _saveTimeDoQuiz() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.setInt('timeDoQuiz_' + widget.idQuiz, timeDoQuiz);
    print('save time do quiz');
  }

  void _addResumeQuiz() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.setString('resumeQuiz', widget.idQuiz);
    prefs.setString('questionResume', jsonEncode(question));
  }

  Future<bool> _timeUpDiaglog() async {
    _endQuiz(true);
    return (await showDialog(
          context: context,
          builder: (BuildContext context) => WillPopScope(
            child: Dialog(
              // contentPadding: EdgeInsets.fromLTRB(16, 30, 16, 24),
              insetPadding: const EdgeInsets.symmetric(horizontal: 16),
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(16, 30, 16, 24),
                  child: Column(
                    children: [
                      Container(
                        width: 196,
                        height: 139,
                        decoration: const BoxDecoration(
                            image: DecorationImage(
                          image: AssetImage("assets/images/info_dialog.png"),
                          fit: BoxFit.fill,
                        )),
                      ),
                      Text(
                        AppLocalizations.of(context).timeUpDialog,
                        style: AppStyles.dialogText,
                        textAlign: TextAlign.center,
                      ),
                      Padding(
                        padding: const EdgeInsets.only(top: 24),
                        child: Container(
                          width: MediaQuery.of(context).size.width * 0.7,
                          child: Row(
                            children: [
                              Expanded(
                                  child: ElevatedButton(
                                onPressed: () {
                                  Navigator.of(context).pop();
                                  if (showModal) Navigator.pop(context);
                                  Navigator.of(context)
                                      .pushReplacement(
                                        MaterialPageRoute(
                                          builder: (context) => NewExamResult(
                                            answers: _answers,
                                            questions: question,
                                            answersText: _answersText,
                                            quizId: widget.idQuiz,
                                            exam_quiz: widget.exam_quiz,
                                            quizName: widget.quizName,
                                            bookmark: _bookmarks,
                                            passPercent: widget.passPercent,
                                            time: endQuizTime,
                                            quizDuration: widget.duration,
                                            timeDoQuiz: timeDoQuiz,
                                            fromExam: true,
                                            shuffleOptions: true,
                                          ),
                                        ),
                                      )
                                      .then((_) => setState(() {}));
                                },
                                child: Text(
                                  "OK",
                                  style: AppStyles.primaryButton,
                                ),
                                style: ElevatedButton.styleFrom(
                                  foregroundColor: AppColors.white,
                                  backgroundColor: AppColors.greenPrimary,
                                  shadowColor:
                                      const Color.fromARGB(92, 0, 166, 144),
                                  elevation: 0,
                                  minimumSize: const Size(20, 44),
                                  side: BorderSide(
                                      color: AppColors.greenPrimary,
                                      width: 1.0,
                                      style: BorderStyle.solid),
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(4.0)),
                                ),
                              ))
                            ],
                          ),
                        ),
                      )
                    ],
                  ),
                ),
              ),
            ),
            onWillPop: () async {
              return false;
            },
          ),
        )) ??
        false;
  }

  Future<bool> _onBackPressed() async {
    return (await showDialog(
          context: context,
          builder: (BuildContext context) => Dialog(
            // contentPadding: EdgeInsets.fromLTRB(16, 30, 16, 24),
            insetPadding: const EdgeInsets.symmetric(horizontal: 16),
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.fromLTRB(16, 30, 16, 24),
                child: Column(
                  children: [
                    Container(
                      width: 196,
                      height: 139,
                      decoration: const BoxDecoration(
                          image: DecorationImage(
                        image: AssetImage("assets/images/info_dialog.png"),
                        fit: BoxFit.fill,
                      )),
                    ),
                    Text(
                      AppLocalizations.of(context).quitQuizDialog,
                      style: AppStyles.dialogText,
                      textAlign: TextAlign.center,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(top: 24),
                      child: Container(
                        width: MediaQuery.of(context).size.width,
                        child: Row(
                          children: [
                            Expanded(
                                child: Padding(
                              padding: const EdgeInsets.only(right: 6),
                              child: ElevatedButton(
                                onPressed: () {
                                  Navigator.pop(context);
                                },
                                child: Text(
                                  AppLocalizations.of(context).no,
                                  style: AppStyles.secondaryButton,
                                  textAlign: TextAlign.center,
                                ),
                                style: ElevatedButton.styleFrom(
                                  foregroundColor: AppColors.greenPrimary,
                                  backgroundColor: Colors
                                      .white, //specify the color of the button's text and icons as well as the overlay colors used to indicate the hover, focus, and pressed states
                                  shadowColor: Colors
                                      .white, //specify the button's elevation color
                                  elevation: 0, //buttons Material shadow
                                  // padding: const EdgeInsets.only(top: 4.0, bottom: 4.0, right: 8.0, left: 8.0), //specify the button's Padding
                                  minimumSize: const Size(20,
                                      44), //specify the button's first: width and second: height
                                  side: BorderSide(
                                      color: AppColors.greenPrimary,
                                      width: 1.0,
                                      style: BorderStyle
                                          .solid), //set border for the button
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(4.0)),
                                ),
                              ),
                            )),
                            Expanded(
                                child: Padding(
                              padding: const EdgeInsets.only(left: 6),
                              child: ElevatedButton(
                                onPressed: () {
                                  _saveTimeDoQuiz();
                                  _addResumeQuiz();
                                  Navigator.of(context).pop(false);
                                  //Navigator.of(context).pop(context);
                                  Navigator.pushReplacement(context,
                                      MaterialPageRoute(
                                          builder: (BuildContext context) {
                                    return Home();
                                  }));
                                },
                                child: Text(
                                  "OK",
                                  style: AppStyles.primaryButton,
                                ),
                                style: ElevatedButton.styleFrom(
                                  foregroundColor: AppColors.white,
                                  backgroundColor: AppColors.greenPrimary,
                                  shadowColor:
                                      const Color.fromARGB(92, 0, 166, 144),
                                  elevation: 0,
                                  minimumSize: const Size(20, 44),
                                  side: BorderSide(
                                      color: AppColors.greenPrimary,
                                      width: 1.0,
                                      style: BorderStyle.solid),
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(4.0)),
                                ),
                              ),
                            ))
                          ],
                        ),
                      ),
                    )
                  ],
                ),
              ),
            ),
          ),
        )) ??
        false;
  }

  _showFullModalQuestionList(context) {
    showModal = true;
    ScrollController _scrollController = ScrollController();
    int bookmarkCount = 0;
    String mode = 'all';
    for (int i = 0; i < _bookmarks.length; i++) {
      if (_bookmarks[i] != null && _bookmarks[i] == 1) {
        bookmarkCount++;
      }
    }
    showGeneralDialog(
      context: context,
      barrierDismissible:
          false, // should dialog be dismissed when tapped outside
      barrierLabel: "Modal", // label for barrier
      transitionDuration: const Duration(
          milliseconds:
              300), // how long it takes to popup dialog after button click
      pageBuilder: (_, __, ___) {
        // your widget implementation
        return StatefulBuilder(
            builder: (BuildContext context, StateSetter setState) {
          return Scaffold(
            appBar: AppBar(
                backgroundColor: Colors.white,
                centerTitle: true,
                leading: IconButton(
                    icon: const Icon(
                      Icons.close,
                      color: Colors.black,
                    ),
                    onPressed: () {
                      showModal = false;
                      Navigator.pop(context);
                    }),
                title: Text(
                  AppLocalizations.of(context).questionList,
                  style: AppStyles.body20.copyWith(color: AppColors.darkGrey),
                ),
                elevation: 0.0),
            body: Container(
              padding: const EdgeInsets.fromLTRB(20, 10, 20, 10),
              decoration: const BoxDecoration(
                border: Border(
                  top: BorderSide(
                    color: Color(0xfff8f8f8),
                    width: 1,
                  ),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(top: 5, bottom: 5),
                    child: Row(
                      children: [
                        InkWell(
                          child: Text(AppLocalizations.of(context).allQuestion,
                              style: mode == 'all'
                                  ? AppStyles.bodyBold.copyWith(
                                      fontSize: 15, color: AppColors.darkGrey)
                                  : AppStyles.bodyBold.copyWith(
                                      fontSize: 15,
                                      color: AppColors.lightGrey2)),
                          onTap: () {
                            setState(() {
                              mode = 'all';
                              _scrollController.animateTo(0,
                                  duration: const Duration(milliseconds: 500),
                                  curve: Curves.fastOutSlowIn);
                            });
                          },
                        ),
                        Padding(
                          padding: const EdgeInsets.only(left: 15, right: 15),
                          child: Text('|',
                              style: TextStyle(
                                  color: AppColors.lightGrey, fontSize: 15)),
                        ),
                        InkWell(
                          child: Row(
                            children: [
                              Icon(
                                Icons.bookmark,
                                color: mode == 'all'
                                    ? AppColors.lightGrey2
                                    : AppColors.darkGrey,
                              ),
                              Text(
                                  '${AppLocalizations.of(context).bookmarked} (${bookmarkCount})',
                                  style: mode == 'all'
                                      ? AppStyles.bodyBold.copyWith(
                                          fontSize: 15,
                                          color: AppColors.lightGrey2)
                                      : AppStyles.bodyBold.copyWith(
                                          fontSize: 15,
                                          color: AppColors.darkGrey)),
                            ],
                          ),
                          onTap: () {
                            setState(() {
                              mode = 'bookmark';
                              _scrollController.animateTo(0,
                                  duration: const Duration(milliseconds: 500),
                                  curve: Curves.fastOutSlowIn);
                            });
                          },
                        )
                      ],
                    ),
                  ),
                  if (mode == 'all') ...{
                    Expanded(
                      child: ListView.separated(
                          controller: _scrollController,
                          itemBuilder: (context, index) {
                            return ListTile(
                              minLeadingWidth: 20,
                              title: Row(
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.only(right: 20),
                                    child: Text(
                                        '${AppLocalizations.of(context).question} ${index + 1}',
                                        style: _answersText[index].isNotEmpty ||
                                                index == _currentIndex
                                            ? AppStyles.bodyBold.copyWith(
                                                color: AppColors.darkGrey)
                                            : AppStyles.bodyBold.copyWith(
                                                color: AppColors.lightGrey2)),
                                  ),
                                  if (_answersText[index].isNotEmpty) ...{
                                    Text(AppLocalizations.of(context).answered,
                                        style: AppStyles.body.copyWith(
                                            color: AppColors.darkGrey))
                                  } else if (_answersText[index].isEmpty &&
                                      index == _currentIndex) ...{
                                    Text(
                                        AppLocalizations.of(context)
                                            .notAnswered,
                                        style: AppStyles.body.copyWith(
                                            color: AppColors.darkGrey))
                                  }
                                ],
                              ),
                              leading: _bookmarks[index] != null &&
                                      _bookmarks[index] == 1
                                  ? Icon(Icons.bookmark, color: AppColors.black)
                                  : const Icon(Icons.bookmark,
                                      color: Colors.transparent),
                              onTap: () {
                                if (_answersText[index].isNotEmpty ||
                                    index == _currentIndex) {
                                  logEvent("exam_take_question_list_item_click",
                                      {"test_name": widget.quizName});
                                  setState(() {
                                    _currentIndex = index;
                                    Navigator.pop(context);
                                  });
                                }
                              },
                            );
                          },
                          separatorBuilder: (context, index) {
                            return const Divider();
                          },
                          itemCount: _answers.length),
                    )
                  } else ...{
                    Expanded(
                      child: ListView.separated(
                          controller: _scrollController,
                          itemBuilder: (context, index) {
                            if (_bookmarks[index] != null &&
                                _bookmarks[index] == 1) {
                              return ListTile(
                                minLeadingWidth: 20,
                                title: Row(
                                  children: [
                                    Padding(
                                      padding: const EdgeInsets.only(right: 20),
                                      child: Text(
                                          '${AppLocalizations.of(context).question} ${index + 1}',
                                          style: AppStyles.bodyBold.copyWith(
                                              color: AppColors.darkGrey)),
                                    ),
                                    if (_answersText[index].isNotEmpty) ...{
                                      Text(
                                          AppLocalizations.of(context).answered,
                                          style: AppStyles.body.copyWith(
                                              color: AppColors.darkGrey))
                                    } else if (_answersText[index].isEmpty &&
                                        index == _currentIndex) ...{
                                      Text(
                                          AppLocalizations.of(context)
                                              .notAnswered,
                                          style: AppStyles.body.copyWith(
                                              color: AppColors.darkGrey))
                                    }
                                  ],
                                ),
                                leading: Icon(Icons.bookmark,
                                    color: AppColors.black),
                                onTap: () {
                                  setState(() {
                                    _currentIndex = index;
                                    Navigator.pop(context);
                                  });
                                },
                              );
                            } else {
                              return Container(
                                height: 0,
                                width: 0,
                              );
                            }
                          },
                          separatorBuilder: (context, index) {
                            if (_bookmarks[index] != null &&
                                _bookmarks[index] == 1) {
                              return const Divider();
                            } else {
                              return Container(
                                height: 0,
                                width: 0,
                              );
                            }
                          },
                          itemCount: _bookmarks.length),
                    )
                  }
                ],
              ),
            ),
          );
        });
      },
    );
  }

  void _checkInternet() async {
    bool connect = await InternetConnectionChecker().hasConnection;
    if (connect) internet = true;
  }

  _goBack(BuildContext context) {
    logPage("Main");
    logEvent("exam_take_back_click", {"test_name": widget.quizName});
    Navigator.pop(context);
  }

  @override
  void initState() {
    if (!widget.resume) {
      _clearLocal();
    }
    WidgetsBinding.instance.addObserver(this);
    _checkInternet();
    inspect(widget.questionSelection);
    futureLocalData = _getLocal();
    _scrollControllerOptions = ScrollController();
    _scrollControllerQuestion = ScrollController();
    _getLastQ();
    logPage("Take Exam");
    super.initState();
  }

  Future<void> _getLastQ() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    var answerList;
    int all = 0;
    if (prefs.getString('answered_' + widget.idQuiz) != null) {
      answerList =
          jsonDecode(prefs.getString('answered_' + widget.idQuiz) ?? '');
      all = answerList.length;
    }

    int count = 0;
    for (int i = 0; i < all; i++) {
      if (answerList[i] is String && answerList[i].length == 0) {
        break;
      }
      if (answerList[i] is Map &&
          answerList[i].values.contains(true) == false) {
        break;
      }
      count++;
      //print(_answers[i]);
    }
    if (count == question.length && count != 0) {
      _currentIndex = count - 1;
    } else {
      _currentIndex = count;
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.inactive) {
      controller.disposeTimer();
      _timer?.cancel();
      _timer = null;
      print('pause time');
    } else if (state == AppLifecycleState.resumed) {
      print('start time');
      _startTimer();
      remainTime = widget.duration * 60 - timeDoQuiz;
      endTime = DateTime.now().millisecondsSinceEpoch +
          Duration(seconds: remainTime).inMilliseconds;
      controller =
          CountdownTimerController(endTime: endTime, onEnd: _timeUpDiaglog);
    }
    _saveLocal();
    print('state = $state');
  }

  @override
  void dispose() {
    WidgetsBinding.instance?.removeObserver(this);
    controller.dispose();
    _timer?.cancel();
    _timer = null;
    super.dispose();
    print('dispose');
    /* if (_currentIndex < (question.length - 1)) {
      _saveLocal();
    } */
  }

  @override
  void setState(fn) {
    if (mounted) {
      super.setState(fn);
    }
  }

  String getQuestionText() {
    if (question.isEmpty) return "";
    String questionText = question[_currentIndex].question;
    if (RegExp(r'^<p><br />.*').hasMatch(questionText)) {
      questionText = questionText.substring(9, questionText.length - 4);
    } else if (RegExp(r'<p>.*?</p>').hasMatch(questionText)) {
      questionText = questionText.substring(3, questionText.length - 4);
    } else if (RegExp(r'^<br>.*').hasMatch(questionText)) {
      questionText = questionText.substring(4);
    }
    return questionText
        .replaceAll('../../../', Common.apiDomain)
        .replaceAll('../../', Common.apiDomain);
  }

  @override
  Widget build(BuildContext context) {
    final questionText = getQuestionText();
    return FutureBuilder<String>(
        future: futureLocalData,
        builder: (context, snapshot) {
          if (snapshot.hasData) {
            if (endTime == 0) {
              _startTimer();
              if (widget.duration == 0) {
                endTime = 9999999999;
              } else {
                endTime = DateTime.now().millisecondsSinceEpoch +
                    Duration(seconds: remainTime).inMilliseconds;
              }
              controller = CountdownTimerController(
                  endTime: endTime, onEnd: _timeUpDiaglog);
            }
            return WillPopScope(
              onWillPop: _onBackPressed,
              child: Scaffold(
                appBar: AppBar(
                  elevation: 1,
                  centerTitle: true,
                  iconTheme: const IconThemeData(
                    color: Colors.black,
                  ),
                  title: Text(
                    widget.quizName,
                    style: AppStyles.title.copyWith(
                        color: AppColors.blackText,
                        fontSize: 18,
                        fontWeight: FontWeight.w700,
                        height: 1.444),
                  ),
                  backgroundColor: Colors.white,
                  actions: [
                    TextButton(
                        onPressed: _showSubmitDialog,
                        child: Text(
                          AppLocalizations.of(context).submit,
                          style: AppStyles.primaryButton.copyWith(
                              fontWeight: FontWeight.w700,
                              color: const Color(0xFF00A690)),
                        )),
                  ],
                ),
                body: Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(left: 16, top: 10),
                      child: Row(
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(right: 5),
                            child: Icon(
                              Icons.access_time,
                              color: AppColors.grey,
                              size: 16,
                            ),
                          ),
                          Text('${AppLocalizations.of(context).remainTime}: ',
                              style: AppStyles.examBody.copyWith(
                                  fontSize: 12, fontWeight: FontWeight.w500)),
                          widget.duration == 0
                              ? Text(AppLocalizations.of(context).noLimit,
                                  style: AppStyles.examBody.copyWith(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w500))
                              : CountdownTimer(
                                  controller: controller,
                                  endTime: endTime,
                                  onEnd: _timeUpDiaglog,
                                  widgetBuilder:
                                      (_, CurrentRemainingTime? time) {
                                    if (time == null) {
                                      return Text(
                                          AppLocalizations.of(context).timeUp);
                                    }
                                    Object? hours =
                                        time.hours == null ? '0' : time.hours;
                                    Object? min =
                                        time.min == null ? '0' : time.min;
                                    return Text(
                                        '${hours.toString().padLeft(2, '0')}:${min.toString().padLeft(2, '0')}:${time.sec.toString().padLeft(2, '0')}',
                                        style: AppStyles.examBody.copyWith(
                                            fontSize: 12,
                                            fontWeight: FontWeight.w500));
                                  },
                                ),
                          const Spacer(),
                          SizedBox(
                            width: 20,
                            height: 20,
                            child: IconButton(
                              padding: EdgeInsets.zero,
                              iconSize: 20,
                              icon: Icon(
                                Icons.menu,
                                color: AppColors.blackText,
                              ),
                              onPressed: () => {
                                _showFullModalQuestionList(context),
                                logEvent("exam_take_question_list_click",
                                    {"test_name": widget.quizName})
                              },
                            ),
                          ),
                          const SizedBox(width: 16),
                        ],
                      ),
                    ),
                    if (widget.duration != 0) ...{
                      Container(
                        margin:
                            const EdgeInsets.only(left: 16, right: 16, top: 10),
                        child: LinearProgressIndicator(
                          backgroundColor: const Color(0xFFD9E2E8),
                          color: AppColors.homeHeading,
                          minHeight: 4,
                          value: valueProgress,
                        ),
                      )
                    },
                    Expanded(
                      child: Container(
                        // padding: const EdgeInsets.all(16.0),
                        margin:
                            const EdgeInsets.only(top: 10, left: 16, right: 16),
                        decoration: BoxDecoration(
                            border: Border.all(color: const Color(0xFFE8E8E8)),
                            color: const Color(0xFFFAFAFA),
                            borderRadius: const BorderRadius.only(
                                topLeft: Radius.circular(4),
                                topRight: Radius.circular(4))),
                        child: Row(
                          children: [
                            Flexible(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  GestureDetector(
                                      child: Container(
                                        padding: const EdgeInsets.all(12.0),
                                        decoration: const BoxDecoration(
                                          color: Color(0xFFEDEFF1),
                                        ),
                                        child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Text(
                                                '${AppLocalizations.of(context).question} ${_currentIndex + 1} ${AppLocalizations.of(context).questionOf} ${question.length}'
                                                    .toUpperCase(),
                                                style: AppStyles.examHeading12
                                                    .copyWith(),
                                              ),
                                              InkWell(
                                                  splashColor:
                                                      Colors.transparent,
                                                  highlightColor:
                                                      Colors.transparent,
                                                  onTap: () {
                                                    setState(() {
                                                      _bookmark();
                                                    });
                                                  },
                                                  child: SizedBox(
                                                    width: 24,
                                                    height: 24,
                                                    child: _bookmarks[
                                                                _currentIndex] ==
                                                            1
                                                        ? Icon(
                                                            Icons.bookmark,
                                                            color:
                                                                AppColors.black,
                                                            size: 24,
                                                          )
                                                        : Icon(
                                                            Icons
                                                                .bookmark_border,
                                                            color:
                                                                AppColors.black,
                                                            size: 24,
                                                          ),
                                                  )),
                                            ]),
                                      ),
                                      onTap: () {
                                        logEvent("exam_take_bookmark_click",
                                            {"test_name": widget.quizName});
                                        setState(() {
                                          _bookmark();
                                        });
                                      }),
                                  GestureDetector(
                                    onTap: () {
                                      String imagePath =
                                          extractImagePathFromHtml(
                                              questionText);
                                      if (imagePath != "") {
                                        showImagePopup(context, imagePath);
                                      }
                                    },
                                    child: Padding(
                                      padding: const EdgeInsets.only(
                                          top: 10,
                                          left: 10,
                                          right: 10,
                                          bottom: 10),
                                      child: Container(
                                        constraints: BoxConstraints(
                                            maxHeight: MediaQuery.of(context)
                                                    .size
                                                    .height *
                                                0.3),
                                        child: SingleChildScrollView(
                                          controller: _scrollControllerQuestion,
                                          child: Html(
                                            data: questionText,
                                            style: {
                                              "body": Style(
                                                  fontSize: FontSize(
                                                      double.parse(
                                                          CommonFont.size)),
                                                  fontFamily: 'Inter',
                                                  color: AppColors.blackText,
                                                  margin: Margins.zero,
                                                  padding: HtmlPaddings.zero,
                                                  lineHeight:
                                                      const LineHeight(1.5)),
                                              "img": Style(
                                                  width: Width(
                                                      MediaQuery.of(context)
                                                              .size
                                                              .width -
                                                          50,
                                                      Unit.px))
                                            },
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                  Expanded(
                                    child: Container(
                                      decoration: const BoxDecoration(
                                          border: Border(
                                              top: BorderSide(
                                                  color: Color(0xFFE8E8E8)))),
                                      child: SingleChildScrollView(
                                        controller: _scrollControllerOptions,
                                        child: Column(
                                          children: [
                                            ...question[_currentIndex]
                                                .options!
                                                .mapIndexed((index, option) {
                                              if (question[_currentIndex]
                                                      .type ==
                                                  "single") {
                                                return InkWell(
                                                  onTap: () {
                                                    setState(() {
                                                      _answers[_currentIndex] =
                                                          option.oid;
                                                      _answersText[
                                                              _currentIndex] =
                                                          option.qOption;
                                                      _answersId[
                                                          _currentIndex] = [
                                                        option.oid
                                                      ];
                                                      _saveLocal();
                                                      //print(_answersText);
                                                    });
                                                  },
                                                  child: Padding(
                                                    padding: const EdgeInsets
                                                        .symmetric(vertical: 7),
                                                    child: Row(
                                                      children: [
                                                        Padding(
                                                          padding:
                                                              const EdgeInsets
                                                                  .only(
                                                                  left: 15,
                                                                  right: 11),
                                                          child: SizedBox(
                                                            width: 18,
                                                            height: 18,
                                                            child: Radio(
                                                                visualDensity: const VisualDensity(
                                                                    horizontal:
                                                                        VisualDensity
                                                                            .minimumDensity,
                                                                    vertical:
                                                                        VisualDensity
                                                                            .minimumDensity),
                                                                materialTapTargetSize:
                                                                    MaterialTapTargetSize
                                                                        .shrinkWrap,
                                                                value:
                                                                    option.oid,
                                                                groupValue:
                                                                    _answers[
                                                                        _currentIndex],
                                                                onChanged:
                                                                    (value) {
                                                                  setState(() {
                                                                    _answers[
                                                                            _currentIndex] =
                                                                        option
                                                                            .oid;
                                                                    _answersText[
                                                                            _currentIndex] =
                                                                        option
                                                                            .qOption;
                                                                    _answersId[
                                                                        _currentIndex] = [
                                                                      option.oid
                                                                    ];
                                                                    _saveLocal();
                                                                    //print(_answersText);
                                                                  });
                                                                }),
                                                          ),
                                                        ),
                                                        buildOptionsExam(index,
                                                            option.qOption)
                                                      ],
                                                    ),
                                                  ),
                                                );
                                              } else {
                                                return InkWell(
                                                  onTap: () {
                                                    setState(() {
                                                      _multiText(
                                                          option.qOption);
                                                      _selectCheckbox(
                                                          index, option.oid);
                                                      _saveLocal();
                                                    });
                                                  },
                                                  child: Padding(
                                                    padding: const EdgeInsets
                                                        .symmetric(vertical: 7),
                                                    child: Row(
                                                      children: [
                                                        Padding(
                                                          padding:
                                                              const EdgeInsets
                                                                  .only(
                                                                  left: 15,
                                                                  right: 11),
                                                          child: Checkbox(
                                                              visualDensity: const VisualDensity(
                                                                  horizontal:
                                                                      VisualDensity
                                                                          .minimumDensity,
                                                                  vertical:
                                                                      VisualDensity
                                                                          .minimumDensity),
                                                              materialTapTargetSize:
                                                                  MaterialTapTargetSize
                                                                      .shrinkWrap,
                                                              value: _answers[
                                                                      _currentIndex]
                                                                  [option.oid],
                                                              onChanged:
                                                                  (value) {
                                                                setState(() {
                                                                  _multiText(option
                                                                      .qOption);
                                                                  _selectCheckbox(
                                                                      index,
                                                                      option
                                                                          .oid);
                                                                  _saveLocal();
                                                                });
                                                              }),
                                                        ),
                                                        buildOptionsExam(index,
                                                            option.qOption)
                                                      ],
                                                    ),
                                                  ),
                                                );
                                                // CheckboxListTile(
                                                //     controlAffinity:
                                                //         ListTileControlAffinity
                                                //             .leading,
                                                //     title: RichText(
                                                //       text: TextSpan(
                                                //         text: alphabet[index],
                                                //         style: AppStyles
                                                //             .bodyBold
                                                //             .copyWith(
                                                //                 color: AppColors
                                                //                     .black,
                                                //                 fontSize: double.parse(
                                                //                         CommonFont
                                                //                             .size) -
                                                //                     5),
                                                //         children: <TextSpan>[
                                                //           TextSpan(
                                                //               text: Helper()
                                                //                   .parseHtmlString(option
                                                //                       .qOption
                                                //                       .replaceAll(
                                                //                           '\n',
                                                //                           ' ')),
                                                //               style: AppStyles.body.copyWith(
                                                //                   height: 1.2,
                                                //                   color:
                                                //                       AppColors
                                                //                           .black,
                                                //                   fontSize:
                                                //                       double.parse(
                                                //                               CommonFont.size) -
                                                //                           5)),
                                                //         ],
                                                //       ),
                                                //     ),
                                                //     value:
                                                //         _answers[_currentIndex]
                                                //             [option.oid],
                                                //     onChanged: (value) {
                                                //       setState(() {
                                                //         _multiText(
                                                //             option.qOption);
                                                //         _selectCheckbox(
                                                //             index, option.oid);
                                                //         _saveLocal();
                                                //       });
                                                //     });
                                              }
                                            })
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(
                      height: 12,
                    ),
                    Container(
                      decoration:
                          BoxDecoration(color: Colors.white, boxShadow: [
                        BoxShadow(
                          color: const Color(0xff8a8a8a).withOpacity(0.25),
                          spreadRadius: 0,
                          blurRadius: 16,
                          offset: const Offset(4, -4),
                        ),
                      ]),
                      child: Row(
                        children: [
                          Expanded(
                              child: Padding(
                            padding: const EdgeInsets.only(
                                top: 12, right: 6, bottom: 38, left: 16),
                            child: ElevatedButton(
                              onPressed: () {
                                _currentIndex != 0
                                    ? setState(() {
                                        _currentIndex--;
                                        _scrollControllerOptions.jumpTo(
                                            _scrollControllerOptions
                                                .position.minScrollExtent);
                                        _scrollControllerQuestion.jumpTo(
                                            _scrollControllerQuestion
                                                .position.minScrollExtent);
                                      })
                                    : null;
                              },
                              child: Text(
                                AppLocalizations.of(context).previous,
                                style: AppStyles.secondaryButton
                                    .copyWith(color: const Color(0xFF6A6A6A)),
                                textAlign: TextAlign.center,
                              ),
                              style: ElevatedButton.styleFrom(
                                foregroundColor: const Color(0xFF6A6A6A),
                                backgroundColor: const Color(0xFFE8E8E8),
                                shadowColor: Colors.white,
                                elevation: 0,
                                minimumSize: const Size(20, 48),
                                side: const BorderSide(
                                    color: Color(0xFFE8E8E8),
                                    width: 1.0,
                                    style: BorderStyle.solid),
                                shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(4.0)),
                              ),
                            ),
                          )),
                          Expanded(
                              child: Padding(
                            padding: const EdgeInsets.only(
                                top: 12, left: 6, bottom: 38, right: 16),
                            child: ElevatedButton(
                              onPressed: _handleNext,
                              child: Text(
                                _currentIndex < (question.length - 1)
                                    ? AppLocalizations.of(context).next
                                    : AppLocalizations.of(context).finish,
                                style: AppStyles.primaryButton,
                              ),
                              style: ElevatedButton.styleFrom(
                                foregroundColor: AppColors.white,
                                backgroundColor: AppColors.greenPrimary,
                                shadowColor:
                                    const Color.fromARGB(92, 0, 166, 144),
                                elevation: 0,
                                minimumSize: const Size(20, 48),
                                side: BorderSide(
                                    color: AppColors.greenPrimary,
                                    width: 1.0,
                                    style: BorderStyle.solid),
                                shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(4.0)),
                              ),
                            ),
                          )),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          } else if (snapshot.hasError) {
            if (internet == false) {
              return Scaffold(
                  appBar: AppBar(
                    leading: IconButton(
                      icon: const Icon(Icons.arrow_back),
                      iconSize: 20.0,
                      onPressed: () {
                        _goBack(context);
                      },
                    ),
                    title: Text(
                      AppLocalizations.of(context).quizList,
                      style: AppStyles.title,
                    ),
                  ),
                  body: Center(
                      child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Image.asset('assets/images/no-internet.png'),
                      Padding(
                        padding: const EdgeInsets.only(top: 20),
                        child: Text(AppLocalizations.of(context).noInternet,
                            style: AppStyles.body),
                      ),
                    ],
                  )));
            } else {
              return ErrorDialog(error: snapshot.error.toString());
            }
          } else {
            return const Scaffold(
                body: Center(child: CircularProgressIndicator()));
          }
        });
  }

  Widget buildOptionsExam(int index, String option) {
    if (option.contains("<img")) {
      return Expanded(
        child: Row(
          children: [
            Text(
              alphabet[index],
              style: AppStyles.bodyBold.copyWith(
                  color: AppColors.black,
                  fontSize: double.parse(CommonFont.size) - 5),
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 5),
                child: Html(
                  data: replaceLinkImg(option),
                  shrinkWrap: true,
                  style: {
                    "body": Style(
                        fontSize: FontSize(double.parse(CommonFont.size) - 5),
                        fontFamily: 'Inter',
                        color: AppColors.blackText,
                        margin: Margins.zero,
                        padding: HtmlPaddings.zero,
                        lineHeight: const LineHeight(1.5)),
                    "img": Style(
                        width: Width(
                            MediaQuery.of(context).size.width - 150, Unit.px))
                  },
                ),
              ),
            ),
          ],
        ),
      );
    } else {
      return Expanded(
        child: RichText(
          text: TextSpan(
            text: alphabet[index],
            style: AppStyles.bodyBold.copyWith(
                color: AppColors.black,
                fontSize: double.parse(CommonFont.size) - 5),
            children: <TextSpan>[
              TextSpan(
                  text: Helper().parseHtmlString(option.replaceAll('\n', ' ')),
                  style: AppStyles.body.copyWith(
                      height: 1.2,
                      color: AppColors.black,
                      fontSize: double.parse(CommonFont.size) - 5)),
            ],
          ),
        ),
      );
    }
  }
}
