import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_svg/svg.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:intl/intl.dart';
import 'package:psm_app/helper/helper.dart';
import 'package:psm_app/localization.dart';
import 'package:psm_app/view/detail_result/detail_result.dart';
import 'dart:math' as math;
import 'package:psm_app/view/home/<USER>/score_card/score_card_widget.dart';
/* import 'package:dev_quiz/shared/models/user_model.dart'; */
import 'package:flutter/material.dart';
import 'package:psm_app/view/widgets/error_dialog.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../../core/core.dart';
import '../../../../globals.dart';
import '../../../home.dart';
import '../../../widgets/info_dialog.dart';

Future<String> getUserName() async {
  final SharedPreferences prefs = await SharedPreferences.getInstance();
  final String name = await prefs.getString('userName') ?? "Error";
  return name;
}

class AppBarWidget extends PreferredSize {
  String username = '';
  final VoidCallback onTap;
  BuildContext context;
  AppBarWidget({required this.onTap, required this.context})
      : super(
          preferredSize: const Size.fromHeight(220),
          child: Container(
            height: 220,
            child: Stack(
              children: [
                if (Common.premium) ...{
                  Positioned(
                    top: 45,
                    left: 16,
                    child: Text("Premium",
                        style: AppStyles.body.copyWith(
                            color: const Color(0xFF116C78),
                            fontWeight: FontWeight.w600,
                            fontSize: 12)),
                  )
                },
                Container(
                  height: 150,
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  width: double.maxFinite,
                  // decoration: BoxDecoration(
                  //   gradient: AppGradients.linear,
                  //   border: Border.fromBorderSide(
                  //       BorderSide(color: AppColors.border)),
                  // ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Row(children: <Widget>[
                        Text(
                          AppLocalizations.of(context).hello + ", ",
                          style: AppStyles.homeHeading,
                        ),
                        FutureBuilder<String>(
                          future: getUserName(), // async work
                          builder: (BuildContext context,
                              AsyncSnapshot<String> snapshot) {
                            switch (snapshot.connectionState) {
                              case ConnectionState.waiting:
                                return Text(
                                  Common.username + "!",
                                  style: AppStyles.homeHeading
                                      .copyWith(fontWeight: FontWeight.w700),
                                );
                              default:
                                return Text(
                                  snapshot.data! + "!",
                                  style: AppStyles.homeHeading
                                      .copyWith(fontWeight: FontWeight.w700),
                                );
                            }
                          },
                        ),
                      ]),
                      Row(
                        children: [
                          GestureDetector(
                              onTap: () {
                                logEvent("main_exam_tips_click", {});
                                showDialog(
                                  context: context,
                                  builder: (_) => const FunkyOverlay(),
                                );
                              },
                              child: getDeviceType() == 'tablet'
                                  ? SvgPicture.asset(
                                      'assets/images/tipsIcon.svg',
                                      fit: BoxFit.contain,
                                      height: 35,
                                    )
                                  : SvgPicture.asset(
                                      'assets/images/tipsIcon.svg',
                                      fit: BoxFit.contain,
                                    )),
                          const SizedBox(
                            width: 10,
                          ),
                          Stack(
                            clipBehavior: Clip.none,
                            children: [
                              InkWell(
                                splashColor: Colors.transparent,
                                highlightColor: Colors.transparent,
                                onTap: onTap,
                                child: getDeviceType() == 'tablet'
                                    ? SizedBox(
                                        width: 30,
                                        height: 30,
                                        child: Image.asset(
                                            'assets/images/setting_icon.png',
                                            fit: BoxFit.fill),
                                      )
                                    : SizedBox(
                                        width: 25,
                                        height: 25,
                                        child: Image.asset(
                                            'assets/images/setting_icon.png',
                                            fit: BoxFit.fill),
                                      ),
                              ),
                              if (Common.premium) ...{
                                Positioned(
                                    top: -6,
                                    right: -5,
                                    child: SvgPicture.asset(
                                      'assets/images/star.svg',
                                      width: 16,
                                      height: 16,
                                      fit: BoxFit.contain,
                                    ))
                              }
                            ],
                          ),
                        ],
                      ),
                      /* Container(
                        width: 58,
                        height: 58,
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            image: DecorationImage(
                                image: NetworkImage(user.photoUrl))),
                      ) */
                    ],
                  ),
                ),
                const Align(
                    alignment: Alignment(0.0, 1.0),
                    child: ScoreCardWidget(
                      percent: 10 / 100,
                    ))
              ],
            ),
          ),
        );
}

class FunkyOverlay extends StatefulWidget {
  const FunkyOverlay({Key? key}) : super(key: key);
  @override
  State<StatefulWidget> createState() => FunkyOverlayState();
}

class FunkyOverlayState extends State<FunkyOverlay>
    with SingleTickerProviderStateMixin {
  late AnimationController controller;
  late Animation<double> scaleAnimation;
  int index = 0;
  bool status = false;

  Future<List> getTipsLocal() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    index = preferences.getInt('tipsIndex') ?? 0;
    var checkTipsStatus = preferences.getString('last_tips');
    var checkTips = preferences.getString('tips');
    var today = DateFormat('dd/MM/yyyy').format(DateTime.now());
    if (checkTipsStatus == null ||
        checkTipsStatus == "" ||
        checkTips == "" ||
        checkTips == null) {
      return await getTips();
    } else {
      if (today.compareTo(checkTipsStatus) <= 0 && status == false) {
        status = true;
        return await getTips();
      } else {
        var tipsList = preferences.getString('tips');
        return jsonDecode(tipsList!);
      }
    }
  }

  Future<List> getTips() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    bool loadRemoteDatatSucceed = false;
    bool connect = await InternetConnectionChecker().hasConnection;
    var sevenDaysFromNow = DateFormat('dd/MM/yyyy')
        .format(DateTime.now().add(const Duration(days: 7)));
    if (connect) {
      var token = preferences.getString('token');
      String info = await GetDeviceInfo() + "-Get Tips";
      Map data = {'appid': Common.appid};
      String tips_api = getTipsApi().url_tips;
      try {
        final response = await dio.post(tips_api,
            data: FormData.fromMap({
              "key": getUserInfor().appkey,
              "token": token,
              "info": info,
              "data": jsonEncode(data),
              "debugId": Common.debugId
            }));
        if (response.statusCode == 200) {
          loadRemoteDatatSucceed = true;
          preferences.setString('tips', response.data);
          preferences.setString('last_tips', sevenDaysFromNow);
          return response.data != "" ? jsonDecode(response.data) : [];
        }
      } catch (e) {
        if (loadRemoteDatatSucceed == false) {
          return [];
        }
      }
      return [];
    } else {
      return [];
    }
  }

  @override
  void initState() {
    super.initState();

    controller = AnimationController(
        vsync: this, duration: const Duration(milliseconds: 450));
    scaleAnimation =
        CurvedAnimation(parent: controller, curve: Curves.elasticInOut);

    controller.addListener(() {
      setState(() {});
    });

    controller.forward();
  }

  void saveTipsIndex() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.setInt('tipsIndex', index);
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: getTipsLocal(),
        builder: (context, snapshot) {
          if (snapshot.hasError) {
            return ErrorDialog(error: snapshot.error.toString());
          }
          if (snapshot.hasData) {
            final tips = snapshot.data as List;
            if (tips.isEmpty) {
              return InfoDialog(
                title: AppLocalizations.of(context).emptyExamTips,
              );
            } else {
              return Scaffold(
                backgroundColor: Colors.transparent,
                body: Center(
                  child: SingleChildScrollView(
                    //color: Colors.transparent,
                    child: ScaleTransition(
                      scale: scaleAnimation,
                      child: Padding(
                        padding: const EdgeInsets.only(left: 16.0, right: 16),
                        child: Container(
                          //height: 600,
                          //height: 520,
                          decoration: ShapeDecoration(
                              gradient: const LinearGradient(
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                                colors: [
                                  Color(0xFFE3F8FF),
                                  Color(0xFFFFFFFF),
                                ],
                              ),
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(15.0))),
                          child: ConstrainedBox(
                            constraints: const BoxConstraints(
                              maxWidth: 600.0,
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(0.0),
                              child: Column(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Column(
                                    children: [
                                      Align(
                                          alignment: Alignment.topRight,
                                          child: Padding(
                                            padding: const EdgeInsets.fromLTRB(
                                                0, 10, 10, 10),
                                            child: GestureDetector(
                                              onTap: () {
                                                Navigator.pop(context);
                                              },
                                              child: const Icon(
                                                Icons.close,
                                                size: 30,
                                              ),
                                            ),
                                          )),
                                      Text(
                                        AppLocalizations.of(context)
                                            .someTips4U
                                            .toUpperCase(),
                                        textAlign: TextAlign.center,
                                        style: AppStyles.titleBold.copyWith(
                                            color: const Color(0xff0D4B93)),
                                      ),
                                      const SizedBox(
                                        height: 24,
                                      ),
                                      Text(
                                        "#${index + 1}",
                                        style: AppStyles.titleBold.copyWith(
                                            color: const Color(0xff384655),
                                            fontSize: 30),
                                      ),
                                      const SizedBox(
                                        height: 24,
                                      ),
                                      Align(
                                          alignment: Alignment.topLeft,
                                          child: Padding(
                                            padding: const EdgeInsets.only(
                                                left: 24.0),
                                            child: Transform.rotate(
                                                angle: 180 * math.pi / 180,
                                                child: const Icon(
                                                    Icons.format_quote_sharp)),
                                          )),
                                      Padding(
                                        padding: const EdgeInsets.fromLTRB(
                                            40, 20, 40, 20),
                                        child: Localizations.localeOf(context)
                                                    .toString() ==
                                                "vi"
                                            ? Html(
                                                data: Helper().parseHtmlString(
                                                    tips[index]["message_vn"]
                                                        .toString()),
                                                style: {
                                                  "body": Style(
                                                    textAlign: TextAlign.center,
                                                    fontWeight: FontWeight.bold,
                                                    color:
                                                        const Color(0xff384655),
                                                    fontSize: FontSize(14),
                                                  )
                                                },
                                              )
                                            : Html(
                                                data: Helper().parseHtmlString(
                                                    tips[index]["message"]
                                                        .toString()),
                                                style: {
                                                  "body": Style(
                                                    textAlign: TextAlign.center,
                                                    fontWeight: FontWeight.bold,
                                                    color:
                                                        const Color(0xff384655),
                                                    fontSize: FontSize(14),
                                                  )
                                                },
                                              ),
                                      ),
                                      const Align(
                                          alignment: Alignment.topRight,
                                          child: Padding(
                                            padding:
                                                EdgeInsets.only(right: 24.0),
                                            child:
                                                Icon(Icons.format_quote_sharp),
                                          )),
                                    ],
                                  ),
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Padding(
                                        padding: const EdgeInsets.only(
                                            top: 24,
                                            bottom: 12,
                                            left: 0,
                                            right: 16),
                                        child: Container(
                                          width:
                                              MediaQuery.of(context).size.width,
                                          child: Row(
                                            children: [
                                              index != 0
                                                  ? Expanded(
                                                      child: Padding(
                                                      padding:
                                                          const EdgeInsets.only(
                                                              bottom: 16,
                                                              left: 16),
                                                      child: ElevatedButton(
                                                        onPressed: () {
                                                          setState(() {
                                                            if (index != 0) {
                                                              index--;
                                                              saveTipsIndex();
                                                            }
                                                          });
                                                        },
                                                        child: Text(
                                                          AppLocalizations.of(
                                                                  context)
                                                              .back,
                                                          style: AppStyles
                                                              .secondaryButton,
                                                          textAlign:
                                                              TextAlign.center,
                                                        ),
                                                        style: ElevatedButton
                                                            .styleFrom(
                                                          foregroundColor:
                                                              AppColors
                                                                  .greenPrimary,
                                                          backgroundColor:
                                                              Colors.white,
                                                          shadowColor:
                                                              Colors.white,
                                                          elevation: 0,
                                                          minimumSize:
                                                              const Size(
                                                                  20, 44),
                                                          side: BorderSide(
                                                              color: AppColors
                                                                  .greenPrimary,
                                                              width: 1.0,
                                                              style: BorderStyle
                                                                  .solid),
                                                          shape: RoundedRectangleBorder(
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(
                                                                          4.0)),
                                                        ),
                                                      ),
                                                    ))
                                                  : Container(),
                                              index != tips.length - 1
                                                  ? const SizedBox(
                                                      width: 16,
                                                    )
                                                  : Container(),
                                              index != tips.length - 1
                                                  ? Expanded(
                                                      child: Padding(
                                                      padding:
                                                          const EdgeInsets.only(
                                                              bottom: 16),
                                                      child: ElevatedButton(
                                                        onPressed: () {
                                                          setState(() {
                                                            if (index !=
                                                                tips.length -
                                                                    1) {
                                                              index++;
                                                              saveTipsIndex();
                                                            }
                                                          });
                                                        },
                                                        child: Text(
                                                          AppLocalizations.of(
                                                                  context)
                                                              .next,
                                                          style: AppStyles
                                                              .primaryButton,
                                                        ),
                                                        style: ElevatedButton
                                                            .styleFrom(
                                                          foregroundColor:
                                                              AppColors.white,
                                                          backgroundColor:
                                                              AppColors
                                                                  .greenPrimary,
                                                          shadowColor:
                                                              const Color
                                                                  .fromARGB(92,
                                                                  0, 166, 144),
                                                          elevation: 0,
                                                          minimumSize:
                                                              const Size(
                                                                  20, 44),
                                                          side: BorderSide(
                                                              color: AppColors
                                                                  .greenPrimary,
                                                              width: 1.0,
                                                              style: BorderStyle
                                                                  .solid),
                                                          shape: RoundedRectangleBorder(
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(
                                                                          4.0)),
                                                        ),
                                                      ),
                                                    ))
                                                  : Container()
                                            ],
                                          ),
                                        ),
                                      )
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              );
            }
          }
          return const Center(
            child: CircularProgressIndicator(),
          );
        });
  }
}
