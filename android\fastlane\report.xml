<?xml version="1.0" encoding="UTF-8"?>
<testsuites>
  <testsuite name="fastlane.lanes">
    
    
    
      
      <testcase classname="fastlane.lanes" name="0: default_platform" time="0.001257">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="1: Switch to android build_apk lane" time="0.000314">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="2: Switch to android sh_on_root lane" time="0.000176">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="3: cd /Users/<USER>/Work/Project/exam_simulator &amp;&amp; flutter build apk" time="189.9518">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="4: firebase_app_distribution" time="12.32836">
        
      </testcase>
    
  </testsuite>
</testsuites>
