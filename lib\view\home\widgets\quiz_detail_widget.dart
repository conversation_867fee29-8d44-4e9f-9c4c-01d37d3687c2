/* import 'package:dev_quiz/core/app_gradients.dart';
import 'package:dev_quiz/core/app_text_styles.dart'; */
/* import 'package:dev_quiz/shared/models/user_model.dart'; */
import 'package:flutter/material.dart';
import 'package:psm_app/view/home/<USER>/score_card/score_card_widget2.dart';

class DetailWidget extends PreferredSize {
  // Không thêm const để khi back tính lại số
  DetailWidget({Key? key})
      : super(
          key: key,
          preferredSize: const Size.fromHeight(250),
          child: ScoreCardWidget2(),
        );
}
