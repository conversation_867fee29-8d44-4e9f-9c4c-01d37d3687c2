import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:psm_app/core/core.dart';
import 'package:psm_app/localization.dart';
import 'package:psm_app/view/home.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../globals.dart';

class RedeemCodeDialog extends StatelessWidget {
  const RedeemCodeDialog({Key? key}) : super(key: key);

  void redirectStore(code) async {
    String link;
    if (Common.platform == "iOS") {
      link =
          'https://apps.apple.com/redeem?ctx=offercodes&id=${Common.appleAppId}&code=$code';
    } else {
      link = 'https://play.google.com/redeem?code=$code';
    }
    if (await canLaunch(link)) {
      await launch(link);
    } else {
      throw 'Could not launch $link';
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Navigator.pop(
          context,
        );
      },
      child: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        color: Colors.transparent,
        child: Dialog(
          insetPadding: EdgeInsets.symmetric(horizontal: 20),
          child: SingleChildScrollView(
            child: ConstrainedBox(
              constraints: new BoxConstraints(
                maxWidth: 600.0,
              ),
              child: Container(
                width: double.infinity,
                padding: EdgeInsets.all(16),
                child: Stack(
                  children: [
                    Positioned(
                        top: 0,
                        right: 0,
                        child: Icon(
                          Icons.close_outlined,
                          size: 30,
                        )),
                    Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SvgPicture.asset(
                                'assets/images/redeem_code.svg',
                              ),
                            ],
                          ),
                          Padding(
                            padding: const EdgeInsets.only(bottom: 12),
                            child: Container(
                              margin: EdgeInsets.only(top: 10),
                              child: Column(
                                children: [
                                  Text(
                                    AppLocalizations.of(context)
                                        .enterRedeemCode,
                                    textAlign: TextAlign.center,
                                    style: AppStyles.dialogText.copyWith(
                                        fontSize: 18,
                                        fontWeight: FontWeight.w700),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          TextField(
                            textInputAction: TextInputAction.go,
                            onSubmitted: (value) {
                              redirectStore(value);
                              Navigator.pop(context);
                            },
                            decoration: InputDecoration(
                              hintText: AppLocalizations.of(context).yourCode,
                              hintStyle: AppStyles.dialogText
                                  .copyWith(color: const Color(0xFFB3B3B3)),
                              contentPadding: const EdgeInsets.symmetric(
                                  vertical: 4, horizontal: 10),
                              border: OutlineInputBorder(
                                  borderSide: const BorderSide(
                                      width: 1, color: Color(0xFFACB7C5)),
                                  borderRadius: BorderRadius.circular(4)),
                            ),
                          ),
                        ]),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
