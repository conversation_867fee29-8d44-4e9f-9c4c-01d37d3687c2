import 'package:flutter/material.dart';
import 'package:psm_app/core/core.dart';
import 'package:psm_app/localization.dart';

class ConfirmDialog extends StatelessWidget {
  final String text;
  const ConfirmDialog({Key? key, required this.text}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: SingleChildScrollView(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          padding: EdgeInsets.all(16),
          child: Column(children: [
            Padding(
              padding: const EdgeInsets.only(top: 30),
              child: Container(
                width: 196,
                height: 139,
                decoration: BoxDecoration(
                    image: DecorationImage(
                  image: AssetImage("assets/images/dialog_success.png"),
                  fit: BoxFit.fill,
                )),
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Container(
                child: Text(
                  text,
                  textAlign: TextAlign.center,
                  style: AppStyles.dialogText,
                ),
              ),
            ),
          ]),
        ),
      ),
    );
  }
}
