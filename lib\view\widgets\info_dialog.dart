import 'package:flutter/material.dart';

import '../../core/core.dart';

class InfoDialog extends StatelessWidget {
  const InfoDialog({Key? key, required this.title}) : super(key: key);

  final String title;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      // contentPadding: EdgeInsets.fromLTRB(16, 30, 16, 24),
      insetPadding: const EdgeInsets.symmetric(horizontal: 16),
      child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.fromLTRB(16, 30, 16, 24),
          child: Column(
            children: [
              Container(
                width: 196,
                height: 139,
                decoration: const BoxDecoration(
                    image: DecorationImage(
                  image: AssetImage("assets/images/info_dialog.png"),
                  fit: BoxFit.fill,
                )),
              ),
              Text(
                title,
                style: AppStyles.dialogText,
                textAlign: TextAlign.center,
              ),
              Padding(
                padding: const EdgeInsets.only(
                  top: 24,
                ),
                child: SizedBox(
                  width: MediaQuery.of(context).size.width,
                  child: Row(
                    children: [
                      Expanded(
                          child: ElevatedButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                        style: ElevatedButton.styleFrom(
                          foregroundColor: AppColors.white,
                          backgroundColor: AppColors.greenPrimary,
                          shadowColor: const Color.fromARGB(92, 0, 166, 144),
                          elevation: 0,
                          minimumSize: const Size(20, 44),
                          side: BorderSide(
                              color: AppColors.greenPrimary,
                              width: 1.0,
                              style: BorderStyle.solid),
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(4.0)),
                        ),
                        child: Text(
                          "OK",
                          style: AppStyles.primaryButton,
                        ),
                      ))
                    ],
                  ),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
