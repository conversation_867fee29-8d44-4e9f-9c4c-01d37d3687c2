import 'dart:io';

import 'package:flutter/material.dart';
import 'package:launch_review/launch_review.dart';
import 'package:psm_app/core/app_colors.dart';
import 'package:psm_app/core/app_styles.dart';
import 'package:psm_app/localization.dart';
import 'package:psm_app/models/qlist_model.dart';
import 'package:psm_app/view/home.dart';
import 'package:rating_dialog/rating_dialog.dart';
import 'package:url_launcher/url_launcher.dart';

class AboutUs extends StatefulWidget {
  @override
  _MyAppState createState() => _MyAppState();
}

_goBack(BuildContext context) {
  Navigator.pop(context);
}

class _MyAppState extends State<AboutUs> {
  Map vi = {
    'about_us_content':
        'ScrumPass là một tổ chức chuyên nghiệp chuyên cung cấp dịch vụ cố vấn và luyện thi các chứng chỉ Agile nh<PERSON>M, PSPO, PMI-ACP. Với các kh<PERSON>a học được cam kết cao, chúng tôi đã giúp hàng trăm học viên vượt qua các kỳ thi mỗi năm. Bên cạnh đó, các dịch vụ tư vấn, mô phỏng bài thi, công cụ dành cho Scrum Master của chúng tôi đã được nhiều công ty và cá nhân tin tưởng.',
    'description':
        'Chào mừng bạn đến với ứng dụng PSM Exam Simulator. Với PSM Exam Simulator bạn có thể ôn tập, kiểm tra lại kiến thức và chuẩn bị cho chứng chỉ Scrum Master chuyên nghiệp của bạn. Các bài kiểm tra của chúng tôi chắc chắn là một nguồn tham khảo giá trị giúp bạn vượt qua bài kiểm tra chứng chỉ PSM của Scrum.org. \n\nChứng chỉ Scrum Master chuyên nghiệp (PSM) là một trong những chứng chỉ giá trị dành cho vị trị Scrum Master hoặc bất kỳ ai đã, đang và sẽ áp dụng khung làm việc Scrum cho đội nhóm của mình. Chứng chỉ PSM sẽ giúp bạn chứng minh sự hiểu biết của mình về khung làm việc Scrum cũng như sẽ giúp bạn tăng thu nhập, lương của mình. \n\nPSM Exam Simulator bao gồm nhiều bộ câu hỏi đã được chúng tôi lựa chọn cẩn thận để giúp bạn làm quen với đề thi cũng như tăng cao khả năng vượt qua bài thi chứng chỉ PSM ngay ở lần thi đầu tiên. \n\nPSM Exam Simulator là một sản phẩm của ScrumPass.'
  };

  Map en = {
    'about_us_content':
        'ScrumPass is a professional organization specializing in providing mentoring and exam preparation services for Agile certifications such as PSM, PSPO, PMI-ACP. With highly committed courses, we have helped hundreds of students pass the exams every year. Besides, our consulting services, exam simulator, Scrum tools have been trusted by many companies and individuals.',
    'description':
        'Welcome to PSM Exam Simulator application. You can improve and validate your basic knowledge of the Professional Scrum Master Exam. With PSM Exam Simulator you can improve your Agile & Scrum knowledge and help you to pass on your first try scrum.org or PMI certifications. \n\nAll the test content on the app are curated by ScrumPass experts. As you take the exams, you will be able to track a summary of your progress directly on the app. You will know which areas you need to improve on to improve your Scrum Knowledge and to become a Scrum professional. \n\nTake the scrum examinations in different areas \nTrack your result and performance summary \nDetailed explanations on each question and answers \nReal exam style full mock exam with timed interface \nContains large number of questions that covers all syllabus area. \n\nWe regularly update the question set with the real exam content. If you take our Scrum exams frequently and aim at achieving at least 85% on all the exams you take, you will easily pass the real Scrum examination. Download PSM Exam Simulator and get yourself familiarize with the Scrum exam. You can use our application from anywhere and at any time.'
  };
  final _dialog = RatingDialog(
    initialRating: 1.0,
    // your app's name?
    title: Text(
      'Rating Dialog',
      textAlign: TextAlign.center,
      style: const TextStyle(
        fontSize: 25,
        fontWeight: FontWeight.bold,
      ),
    ),
    // encourage your user to leave a high rating?
    message: Text(
      'Tap a star to set your rating. Add more description here if you want.',
      textAlign: TextAlign.center,
      style: const TextStyle(fontSize: 15),
    ),
    // your app's logo?
    image: const FlutterLogo(size: 100),
    submitButtonText: 'Submit',
    commentHint: 'Set your custom comment hint',
    onCancelled: () => print('cancelled'),
    onSubmitted: (response) {
      print('rating: ${response.rating}, comment: ${response.comment}');

      // TODO: add your own logic
      if (response.rating < 3.0) {
        // send their comments to your email or anywhere you wish
        // ask the user to contact you instead of leaving a bad review
      } else {
        //_rateAndReviewApp();
        LaunchReview.launch(
            androidAppId: "com.scrumpass.rangoli", iOSAppId: "585027354");
      }
    },
  );
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var language = Localizations.localeOf(context).toString();
    return MaterialApp(
      title: AppLocalizations.of(context).aboutUs,
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primaryColor: Colors.lightBlueAccent,
      ),
      home: Scaffold(
        appBar: AppBar(
          toolbarHeight: 140.10, //set your height
          flexibleSpace: SafeArea(
            child: Container(
              color: Colors.blue, // set your color
              child: Column(
                children: [
                  Row(
                    children: [
                      IconButton(
                        icon: Icon(Icons.arrow_back),
                        iconSize: 20.0,
                        color: Colors.white,
                        onPressed: () {
                          _goBack(context);
                        },
                      ),
                      Text(
                        AppLocalizations.of(context).aboutUs,
                        style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.white),
                      )
                    ],
                  ),
                  SizedBox(
                    height: 10,
                  ),
                  Wrap(
                    alignment: WrapAlignment.spaceAround, // set your alignment
                    spacing: 10,
                    children: <Widget>[
                      CustomCardHeader(
                          Title: "95%",
                          Description: AppLocalizations.of(context)
                              .certificationPassRate),
                      CustomCardHeader(
                          Title: "92%",
                          Description: AppLocalizations.of(context)
                              .serviceSatisfactionRate),
                      CustomCardHeader(
                          Title: "95%",
                          Description:
                              AppLocalizations.of(context).averageExamResult),
                    ],
                  ),
                  SizedBox(
                    height: 10,
                  ),
                ],
              ),
            ),
          ),
        ),
        body: ListView(
          // This next line does the trick.
          scrollDirection: Axis.vertical,
          children: <Widget>[
            CustomCardText(
                Title: "PSM Exam Simulator App",
                Description:
                    language == 'vi' ? vi['description'] : en['description']),
            CustomCardText(
                Title: AppLocalizations.of(context).aboutUs,
                Description: language == 'vi'
                    ? vi['about_us_content']
                    : en['about_us_content']),
            CustomCardLink(
                TitleP: AppLocalizations.of(context).connectUs,
                DescriptionP: AppLocalizations.of(context).callUs,
                FacebookText: "Facebook",
                WebsiteText: "Website",
                DescriptionM: AppLocalizations.of(context).contact,
                Phone: "+84945694499",
                Mail: "<EMAIL>",
                Facebook: "https://www.facebook.com/scrumpassvn",
                Website: "https://scrumpass.com"),
            Padding(
              padding: const EdgeInsets.only(top: 5, bottom: 5),
              child: Card(
                shape: new RoundedRectangleBorder(
                    side: new BorderSide(color: AppColors.border, width: 1),
                    borderRadius: BorderRadius.circular(4.0)),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Text(
                        AppLocalizations.of(context).writeUsDirectly,
                        style: TextStyle(
                            color: Colors.black,
                            fontWeight: FontWeight.w500,
                            fontSize: 24.0),
                      ),
                      SizedBox(height: 10.0),
                      Row(
                        children: [
                          Icon(Icons.sms, color: Colors.blueAccent, size: 35.0),
                          SizedBox(width: 15.0),
                          new Flexible(
                            child: new TextField(
                              textInputAction: TextInputAction.send,
                              onSubmitted: (text) {
                                /* launch(
                                    'mailto:<EMAIL>?subject=ScrumApp-Feedback&body=$text'); */
                                sendMessage("0945694499", text);
                              },
                              decoration: InputDecoration(
                                  helperText:
                                      AppLocalizations.of(context).yourMessage),
                            ),
                          ),
                        ],
                      ),
                      if (Platform.isAndroid) ...[
                        SizedBox(
                          height: 10,
                        ),
                        Center(
                          child: ElevatedButton(
                            onPressed: () {
                              /* showDialog(
                              context: context,
                              barrierDismissible:
                                  true, // set to false if you want to force a rating
                              builder: (context) => _dialog,
                            ); */
                              LaunchReview.launch(
                                  androidAppId: "com.scrumpass.psm",
                                  iOSAppId: "1614034950");
                            },
                            child: Text(AppLocalizations.of(context).rateUs),
                          ),
                        ),
                      ]
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class CustomCardText extends StatelessWidget {
  //declare Required Vairables
  late String Title;
  late String Description;

  //constructo
  CustomCardText({Key? key, required this.Title, required this.Description})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 5, bottom: 5),
      child: Card(
        shape: new RoundedRectangleBorder(
            side: new BorderSide(color: AppColors.border, width: 1),
            borderRadius: BorderRadius.circular(4.0)),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Text(
                Title,
                style: TextStyle(
                    color: Colors.black,
                    fontWeight: FontWeight.w500,
                    fontSize: 24.0),
              ),
              SizedBox(height: 10.0),
              Text(
                Description,
                textAlign: TextAlign.justify,
                style: TextStyle(
                    color: Colors.black,
                    fontWeight: FontWeight.w400,
                    fontSize: 14.0),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class CustomCardLink extends StatelessWidget {
  //declare Required Vairables
  late String TitleP;
  late String DescriptionP;
  late String TitleM;
  late String DescriptionM;
  late String Phone;
  late String Mail;
  late String FacebookText;
  late String Facebook;
  late String WebsiteText;
  late String Website;

  //constructo
  CustomCardLink(
      {Key? key,
      required this.TitleP,
      required this.DescriptionP,
      required this.DescriptionM,
      required this.Phone,
      required this.Mail,
      required this.Facebook,
      required this.FacebookText,
      required this.Website,
      required this.WebsiteText})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 5, bottom: 5),
      child: Card(
        shape: new RoundedRectangleBorder(
            side: new BorderSide(color: AppColors.border, width: 1),
            borderRadius: BorderRadius.circular(4.0)),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Text(
                TitleP,
                style: TextStyle(
                    color: Colors.black,
                    fontWeight: FontWeight.w500,
                    fontSize: 24.0),
              ),
              SizedBox(height: 10.0),
              Row(
                children: [
                  Icon(Icons.facebook, color: Colors.blueAccent, size: 35.0),
                  SizedBox(width: 15.0),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        FacebookText,
                        style: TextStyle(
                            color: Colors.grey,
                            fontWeight: FontWeight.w400,
                            fontSize: 18.0),
                      ),
                      InkWell(
                          onTap: () {
                            launch(Facebook);
                          },
                          child: Text(
                            "ScrumPass",
                            style: TextStyle(
                                color: Colors.black,
                                fontWeight: FontWeight.bold,
                                fontSize: 18.0),
                          ))
                    ],
                  ),
                ],
              ),
              SizedBox(height: 10.0),
              Row(
                children: [
                  Icon(Icons.web, color: Colors.blueAccent, size: 35.0),
                  SizedBox(width: 15.0),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        WebsiteText,
                        style: TextStyle(
                            color: Colors.grey,
                            fontWeight: FontWeight.w400,
                            fontSize: 18.0),
                      ),
                      InkWell(
                          onTap: () {
                            launch(Website);
                          },
                          child: Text(
                            Website,
                            style: TextStyle(
                                color: Colors.black,
                                fontWeight: FontWeight.bold,
                                fontSize: 18.0),
                          ))
                    ],
                  ),
                ],
              ),
              SizedBox(height: 10.0),
              Row(
                children: [
                  Icon(Icons.call, color: Colors.blueAccent, size: 35.0),
                  SizedBox(width: 15.0),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        DescriptionP,
                        style: TextStyle(
                            color: Colors.grey,
                            fontWeight: FontWeight.w400,
                            fontSize: 18.0),
                      ),
                      InkWell(
                          onTap: () {
                            launch('tel://$Phone');
                          },
                          child: Text(
                            Phone,
                            style: TextStyle(
                                color: Colors.black,
                                fontWeight: FontWeight.bold,
                                fontSize: 18.0),
                          ))
                    ],
                  ),
                ],
              ),
              SizedBox(height: 10.0),
              Row(
                children: [
                  Icon(Icons.mail, color: Colors.blueAccent, size: 35.0),
                  SizedBox(width: 15.0),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        DescriptionM,
                        style: TextStyle(
                            color: Colors.grey,
                            fontWeight: FontWeight.w400,
                            fontSize: 18.0),
                      ),
                      InkWell(
                          onTap: () {
                            launch('mailto:$Mail');
                          },
                          child: Text(
                            Mail,
                            style: TextStyle(
                                color: Colors.black,
                                fontWeight: FontWeight.bold,
                                fontSize: 18.0),
                          ))
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class CustomCardHeader extends StatelessWidget {
  //declare Required Vairables
  late String Title;
  late String Description;

  //constructo
  CustomCardHeader({Key? key, required this.Title, required this.Description})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(children: [
      Text(
        Title,
        style: TextStyle(
            fontSize: 24, fontWeight: FontWeight.bold, color: Colors.white),
      ),
      Container(
        width: MediaQuery.of(context).size.width * 0.3,
        child: Text(
          Description,
          textAlign: TextAlign.center,
          style: TextStyle(fontSize: 14, color: Colors.white),
        ),
      ),
    ]);
  }
}

sendMessage(String phone, String body) async {
  if (Platform.isAndroid) {
    //FOR Android
    // url = 'sms:$phone?body=$body';
    await launch(url);
  } else if (Platform.isIOS) {
    //FOR IOS
    // url = 'sms:$phone&body=$body';
  }
}
