import 'dart:convert';

import 'package:psm_app/models/question_api_model.dart';

class Result {
  final String name, status, time, percentage;
  final List<Question> questions;
  final List<dynamic> answers;
  final List<dynamic> answersText;
  final int passPercent;
  final List<dynamic> bookmark;
  final String quizId;
  final String? quizType;
  final int quizDuration;
  final int timeDoQuiz;
  final String exam_quiz;
  final String? environment;
  final bool? shuffleOptions;
  Result(
      {required this.status,
      required this.time,
      this.quizType = "1",
      this.environment = "",
      required this.percentage,
      required this.name,
      required this.exam_quiz,
      required this.questions,
      required this.answers,
      required this.answersText,
      required this.passPercent,
      required this.bookmark,
      required this.quizId,
      required this.quizDuration,
      required this.timeDoQuiz,
      this.shuffleOptions = false,
      });
  @override
  String toString() {
    return "Question(name: $name, status: $status, time: $time, percentage: $percentage, questions: $questions, answers: $answers, answersText: $answersText, quizType: $answersText)";
  }

  factory Result.fromJson(Map<String, dynamic> jsonData) {
    return Result(
        status: jsonData['status'],
        time: jsonData['time'],
        percentage: jsonData['percentage'],
        name: jsonData['name'],
        quizType: jsonData['quizType'],
        environment: jsonData['environment'],
        exam_quiz: jsonData['exam_quiz'] ?? "0",
        questions: (jsonData['questions'] as List)
            .map((e) => Question.fromJson(e))
            .toList(),
        answers: (List<dynamic>.from(jsonData['answers'] ?? [])),
        answersText: (List<dynamic>.from(jsonData['answersText'] ?? [])),
        bookmark: (List<dynamic>.from(jsonData['bookmark'] ?? [])),
        passPercent: jsonData['passPercent'],
        quizId: jsonData['quizId'],
        quizDuration: jsonData['quizDuration'],
        timeDoQuiz: jsonData['timeDoQuiz'],
        shuffleOptions: jsonData['shuffleOptions'],
        );
  }

  static Map<String, dynamic> toMap(Result music) => {
        'name': music.name,
        'percentage': music.percentage,
        'status': music.status,
        'time': music.time,
        'quizType': music.quizType,
        'environment': music.environment,
        'exam_quiz': music.exam_quiz,
        'questions': music.questions,
        'answers': music.answers,
        'answersText': music.answersText,
        'bookmark': music.bookmark,
        'passPercent': music.passPercent,
        'quizId': music.quizId,
        'quizDuration': music.quizDuration,
        'timeDoQuiz': music.timeDoQuiz,
        'shuffleOptions': music.shuffleOptions,
      };

  static String encode(List<Result> musics) => json.encode(
        musics
            .map<Map<String, dynamic>>((music) => Result.toMap(music))
            .toList(),
      );

  static List<Result> decode(String musics) =>
      (json.decode(musics) as List<dynamic>)
          .map<Result>((item) => Result.fromJson(item))
          .toList();
}
