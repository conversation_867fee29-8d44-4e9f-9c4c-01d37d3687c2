// ignore_for_file: prefer_const_constructors, sized_box_for_whitespace, unnecessary_new, prefer_const_declarations, unused_local_variable

import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:psm_app/view/flashcard/flashcard.dart';
import 'package:psm_app/view/statistics.dart';
import 'package:psm_app/view/widgets/error_dialog.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:showcaseview/showcaseview.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'package:flutter_svg/flutter_svg.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:provider/provider.dart';
import 'package:psm_app/core/core.dart';
import 'package:psm_app/data_sources/api_servies.dart';
import 'package:psm_app/globals.dart';
import 'package:psm_app/localization.dart';
import 'package:psm_app/provider/locale_provider.dart';
import 'package:psm_app/view/exam/new_quiz_list.dart';
import 'package:psm_app/view/payment/payment.dart';
import 'package:psm_app/view/question_review.dart';
import 'package:psm_app/view/result/result.dart';
import 'package:psm_app/view/user_info.dart';
import 'package:psm_app/view/widgets/success_dialog.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:psm_app/view/home/<USER>/quiz_detail_widget.dart';
import 'package:psm_app/view/home/<USER>/appbar/app_bar_widget.dart';
import 'package:store_redirect/store_redirect.dart';
import 'package:firebase_performance/firebase_performance.dart';

import '../data_sources/dio.dart';
import '../helper/helper.dart';
import 'widgets/showcase_tooltip.dart';

String userName = '';
String get url => getUserInfor().url;
String get url_premium => getUserInfor().url_premium;
String appkey = getUserInfor().appkey;
bool avaliable = false;
List tips_list = [];

class TipsCard {
  String title;
  String description;
  TipsCard({
    required this.title,
    required this.description,
  });
}

class Home extends StatefulWidget {
  const Home({Key? key}) : super(key: key);

  @override
  _HomePageState createState() => _HomePageState();
}

var status = true;
final dio = NewDio().getDio();

Future<String> attempPost(String info) async {
  SharedPreferences preferences = await SharedPreferences.getInstance();
  var token = preferences.getString('token');
  token ??= "";
  try {
    final response = await dio.post(url,
        data: FormData.fromMap({
          "key": appkey,
          "token": token,
          "info": info,
          "debugId": Common.debugId
        }));

    if (response.statusCode == 200) {
      //print(response.body);
      return response.data;
    } else {
      status = false;
    }
  } catch (e) {
    print(e);
  }
  return "";
}

Future<void> getCategoryNumQuestion() async {
  SharedPreferences preferences = await SharedPreferences.getInstance();
  bool connect = await InternetConnectionChecker().hasConnection;
  bool loadRemoteDatatSucceed = false;
  if (Common.categoryQuestions == "") {
    if (connect) {
      var token = preferences.getString('token');
      Map data = {'appid': Common.appid};
      var test = jsonEncode(data);
      try {
        final response = await dio.post(
          getNumCategory().url_category_num,
          data: FormData.fromMap({
            'key': appkey,
            'token': token,
            'data': jsonEncode(data),
            "debugId": Common.debugId
          }),
        );
        if (response.statusCode == 200) {
          preferences.setString('numCategory', response.data);
          Common.categoryQuestions = response.data;
          loadRemoteDatatSucceed = true;
        }
      } catch (e) {}
    }
  }
}

Future<bool> runGetListQuiz() async {
  var newQuizListTime =
      DateFormat('dd/MM/yyyy').format(DateTime.now().add(Duration(days: 7)));
  SharedPreferences preferences = await SharedPreferences.getInstance();
  var token = preferences.getString('token');
  String info = await GetDeviceInfo() + "-Quiz List Request";
  Map data = {'appid': Common.appid};
  String getListUrl = '';
  if (Common.premium) {
    getListUrl = url_premium;
  } else {
    getListUrl = url;
  }
  final response = await dio.post(
    getListUrl,
    data: FormData.fromMap({
      'key': appkey,
      'token': token,
      'info': info,
      'data': jsonEncode(data),
      "debugId": Common.debugId
    }),
  );
  if (response.statusCode == 200) {
    preferences.setString('quizList', response.data);
    preferences.setString('quizListTimer', newQuizListTime);
    //loadRemoteDatatSucceed = true;
    return true;
  } else {
    return false;
  }
}

Future<void> getFlashcard() async {
  PackageInfo packageInfo = await PackageInfo.fromPlatform();
  //Common.appid = packageInfo.packageName;
  SharedPreferences preferences = await SharedPreferences.getInstance();
  final flashcard = preferences.getString('flashcard');
  bool loadRemoteDatatSucceed = false;
  bool connect = await InternetConnectionChecker().hasConnection;
  if (connect && (flashcard?.isEmpty ?? true) && Common.premium) {
    var token = preferences.getString('token');
    String info = await GetDeviceInfo() + "- Get Flashcard";
    String data = Common.flashcardCertId;
    String getFlashcardUrl = apiUrl + 'get_flashcard';

    var today = DateFormat('dd/MM/yyyy').format(DateTime.now());
    var flashcardTimerLocal = preferences.getString('flashcardTimerLocal');

    DateTime todayDate = new DateFormat("dd/MM/yyyy").parse(today);
    var newFlashcardTimer =
        DateFormat('dd/MM/yyyy').format(DateTime.now().add(Duration(days: 7)));
    DateTime flashcardTimer = DateFormat("dd/MM/yyyy").parse(newFlashcardTimer);

    if (flashcardTimerLocal != null) {
      flashcardTimer = DateFormat("dd/MM/yyyy").parse(flashcardTimerLocal);
    }

    try {
      var flashcardLocal = preferences.getString('flashcard');
      if (flashcardLocal == null || todayDate.isAfter(flashcardTimer)) {
        final response = await dio.post(
          getFlashcardUrl,
          data: FormData.fromMap({
            'key': appkey,
            'token': token,
            'info': info,
            'data': data,
            "debugId": Common.debugId
          }),
        );
        if (response.statusCode == 200) {
          preferences.setString('flashcard', response.data);
          preferences.setString('flashcardTimerLocal', newFlashcardTimer);
          loadRemoteDatatSucceed = true;
        }
      } else {
        loadRemoteDatatSucceed = true;
      }
    } catch (e) {
      if (loadRemoteDatatSucceed == false) print(e);
    }
  }
}

Future<void> getOtherApp() async {
  PackageInfo packageInfo = await PackageInfo.fromPlatform();
  //Common.appid = packageInfo.packageName;
  SharedPreferences preferences = await SharedPreferences.getInstance();
  bool loadRemoteDatatSucceed = false;
  bool connect = await InternetConnectionChecker().hasConnection;
  if (connect) {
    var token = preferences.getString('token');
    String info = await GetDeviceInfo() + "-Get Other App";
    Map data = {'app_id': Common.appid};
    String other_app_api = getOtherAppApi().url_other_app;
    var today = DateFormat('dd/MM/yyyy').format(DateTime.now());
    var otherAppTimer = preferences.getString('otherAppTimer');

    DateTime todayDate = new DateFormat("dd/MM/yyyy").parse(today);
    var newotherAppTimer =
        DateFormat('dd/MM/yyyy').format(DateTime.now().add(Duration(days: 7)));
    DateTime otherappDated = DateFormat("dd/MM/yyyy").parse(newotherAppTimer);

    if (otherAppTimer != null)
      otherappDated = DateFormat("dd/MM/yyyy").parse(otherAppTimer);

    try {
      var others = preferences.getString('otherApp');
      if (others == null || todayDate.isAfter(otherappDated)) {
        final response = await dio.post(other_app_api,
            data: FormData.fromMap({
              "key": appkey,
              "token": token,
              "info": info,
              "data": jsonEncode(data),
              "debugId": Common.debugId
            }));
        if (response.statusCode == 200) {
          preferences.setString('otherApp', response.data);
          preferences.setString('otherAppTimer', newotherAppTimer);
          loadRemoteDatatSucceed = true;
        }
      } else {
        loadRemoteDatatSucceed = true;
      }
    } catch (e) {
      // if (loadRemoteDatatSucceed == false) retryFuture(getOtherApp, 200);
    }
  }
}

Future<List> getTips() async {
  PackageInfo packageInfo = await PackageInfo.fromPlatform();
  SharedPreferences preferences = await SharedPreferences.getInstance();
  bool loadRemoteDatatSucceed = false;
  bool connect = await InternetConnectionChecker().hasConnection;
  if (connect) {
    var token = preferences.getString('token');
    String info = await GetDeviceInfo() + "-Get Tips";
    Map data = {'appid': Common.appid};
    String tips_api = getTipsApi().url_tips;
    try {
      final response = await dio.post(tips_api,
          data: FormData.fromMap({
            "key": appkey,
            "token": token,
            "info": info,
            "data": jsonEncode(data),
            "debugId": Common.debugId
          }));
      if (response.statusCode == 200) {
        loadRemoteDatatSucceed = true;
        return response.data != "" ? jsonDecode(response.data) : [];
      }
    } catch (e) {
      // if (loadRemoteDatatSucceed == false) retryFuture(getTips, 200);
    }
    return [];
  } else {
    return [];
  }
}

retryFuture(future, delay) {
  Future.delayed(Duration(milliseconds: delay), () {
    future();
  });
}

Future<String?> _getId() async {
  var deviceInfo = DeviceInfoPlugin();
  if (Platform.isIOS) {
    // import 'dart:io'
    var iosDeviceInfo = await deviceInfo.iosInfo;
    return iosDeviceInfo.identifierForVendor; // unique ID on iOS
  } else if (Platform.isAndroid) {
    var androidDeviceInfo = await deviceInfo.androidInfo;
    return androidDeviceInfo.id; // unique ID on Android
  }
}

String lang = '';
Future<void> getLang() async {
  SharedPreferences preferences = await SharedPreferences.getInstance();
  lang = preferences.getString('lang') ?? '';
  // if (lang == '') {
  //   final String defaultLocale = Platform.localeName;
  //   if (defaultLocale.contains('en')) {
  //     lang = 'en';
  //   } else {
  //     lang = 'vi';
  //   }
  //   preferences.setString('lang', lang);
  // }
}

Future getVersion() async {
  PackageInfo packageInfo = await PackageInfo.fromPlatform();
  Common.version = packageInfo.version;
  var version = await ApiServices().checkVersion();
  Common.update = version;
}

Future<void> get_description() async {
  SharedPreferences prefs = await SharedPreferences.getInstance();
  final String resultString = await prefs.getString('result') ?? "";
  final String questionReviewString =
      await prefs.getString('incorrectQuestion') ?? "";
  final String bookmarkReviewString =
      await prefs.getString('bookmarkQuestion') ?? "";
  final List questionReview =
      questionReviewString != "" ? jsonDecode(questionReviewString) : [];
  final List bookmarkReview =
      bookmarkReviewString != "" ? jsonDecode(bookmarkReviewString) : [];
  String url_premium = getDescription().url_depcription;
  List map = [];
  if (resultString != "") {
    map = jsonDecode(resultString);
  }
  var a = [];
  int i;
  int j;
  for (i = 0; i < map.length; i++) {
    for (j = 0; j < map[i]["questions"].length; j++) {
      a.add(map[i]["questions"][j]['id']);
    }
  }
  String jsonTags = jsonEncode(a);
  //print(jsonTags);

  PackageInfo packageInfo = await PackageInfo.fromPlatform();
  //Common.appid = packageInfo.packageName;
  bool loadRemoteDatatSucceed = false;
  bool connect = await InternetConnectionChecker().hasConnection;
  if (connect) {
    var token = prefs.getString('token');
    String info = await GetDeviceInfo() + "-Quiz List Request";
    try {
      final response = await dio.post(url_premium,
          data: FormData.fromMap({
            "key": appkey,
            "token": token,
            "info": info,
            "data": jsonTags,
            "debugId": Common.debugId
          }));
      if (response.statusCode == 200) {
        //preferences.setString('quizList', response.body);
        loadRemoteDatatSucceed = true;
        var result = jsonDecode(response.data);
        //print(resultString);
        var tutu = jsonDecode(resultString);
        //tutu[0]["test"] = "asdasd";
        //print(result.length);
        //inspect(result[0]["description"]);
        for (int i = 0; i < tutu.length; i++) {
          for (int j = 0; j < tutu[i]["questions"].length; j++) {
            for (int t = 0; t < result.length; t++) {
              if (result[t]["qid"] == tutu[i]["questions"][j]["id"]) {
                tutu[i]["questions"][j]["description"] =
                    result[t]['description'];
                break;
              }
            }
          }
        }
        if (questionReview.isNotEmpty) {
          for (int i = 0; i < questionReview.length; i++) {
            for (int t = 0; t < result.length; t++) {
              if (result[t]["qid"] == questionReview[i]["id"]) {
                questionReview[i]["description"] = result[t]["description"];
                break;
              }
            }
          }
          prefs.setString('incorrectQuestion', jsonEncode(questionReview));
        }
        if (bookmarkReview.isNotEmpty) {
          for (int i = 0; i < bookmarkReview.length; i++) {
            for (int t = 0; t < result.length; t++) {
              if (result[t]["qid"] == bookmarkReview[i]["id"]) {
                bookmarkReview[i]["description"] = result[t]["description"];
                break;
              }
            }
          }
          prefs.setString('bookmarkQuestion', jsonEncode(bookmarkReview));
        }
        final resultStr = jsonEncode(tutu);
        if (resultStr.isNotEmpty) {
          prefs.setString('result', resultStr);
        }
        print("Get Description Success");
      }
    } catch (e) {
      if (loadRemoteDatatSucceed == false) {} //retryFuture(getListQuiz, 200);
    }
  }
  //print(a);
}

class _HomePageState extends State<Home> {
  /* final controller = HomeController(); */
  final GlobalKey _showcase1 = GlobalKey();
  final GlobalKey _showcase2 = GlobalKey();
  final GlobalKey _showcase3 = GlobalKey();
  final GlobalKey _showcase4 = GlobalKey();
  final GlobalKey _showcase5 = GlobalKey();
  bool ignorePointer = true;
  String device_type = "";
  late double ratio;
  Future success() => showDialog(
      context: context,
      builder: (context) => SuccessDialog(
            text: "Thankyou for purchase",
          ));

  void connectError() => showDialog(
      context: context,
      builder: (context) => ErrorDialog(
            error: AppLocalizations.of(context).connectServerError,
          ));

  void _sendResultLater() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    List<String> sendLater = preferences.getStringList('sendLater') ?? [];
    if (sendLater.isNotEmpty) {
      for (int i = sendLater.length - 1; i >= 0; i--) {
        var sendResult = await ApiServices().sendResult(sendLater[i]);
        if (sendResult) {
          sendLater.removeAt(i);
        }
      }
      preferences.setStringList('sendLater', sendLater);
    }
  }

  void _showUpdateDialog(data) {
    showDialog<String>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) => WillPopScope(
        onWillPop: () async => false,
        child: Dialog(
          // contentPadding: EdgeInsets.fromLTRB(16, 30, 16, 24),
          insetPadding: EdgeInsets.symmetric(horizontal: 16),
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.fromLTRB(16, 30, 16, 24),
              child: Column(
                children: [
                  Container(
                    width: 196,
                    height: 139,
                    decoration: BoxDecoration(
                        image: DecorationImage(
                      image: AssetImage("assets/images/info_dialog.png"),
                      fit: BoxFit.fill,
                    )),
                  ),
                  Text(
                    data['force_update'] == 0
                        ? AppLocalizations.of(context).updateDialogContent
                        : AppLocalizations.of(context).forceUpdateDialogContent,
                    style: AppStyles.dialogText,
                    textAlign: TextAlign.center,
                  ),
                  Padding(
                    padding: const EdgeInsets.only(top: 24),
                    child: Container(
                      width: MediaQuery.of(context).size.width * 0.7,
                      child: Row(
                        children: [
                          if (data['force_update'] == 0) ...{
                            Expanded(
                                child: Padding(
                              padding: const EdgeInsets.only(right: 6),
                              child: ElevatedButton(
                                onPressed: () {
                                  Navigator.pop(context);
                                },
                                child: Text(
                                  AppLocalizations.of(context).no,
                                  style: AppStyles.secondaryButton,
                                  textAlign: TextAlign.center,
                                ),
                                style: ElevatedButton.styleFrom(
                                  foregroundColor: AppColors.greenPrimary,
                                  backgroundColor: Colors
                                      .white, //specify the color of the button's text and icons as well as the overlay colors used to indicate the hover, focus, and pressed states
                                  shadowColor: Colors
                                      .white, //specify the button's elevation color
                                  elevation: 0, //buttons Material shadow
                                  // padding: const EdgeInsets.only(top: 4.0, bottom: 4.0, right: 8.0, left: 8.0), //specify the button's Padding
                                  minimumSize: Size(20,
                                      44), //specify the button's first: width and second: height
                                  side: BorderSide(
                                      color: AppColors.greenPrimary,
                                      width: 1.0,
                                      style: BorderStyle
                                          .solid), //set border for the button
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(4.0)),
                                ),
                              ),
                            )),
                            Expanded(
                                child: Padding(
                              padding: const EdgeInsets.only(left: 6),
                              child: ElevatedButton(
                                onPressed: () {
                                  Navigator.pop(context);
                                  StoreRedirect.redirect(
                                      androidAppId: Common.appid,
                                      iOSAppId: Common.appleAppId);
                                },
                                child: Text(
                                  "OK",
                                  style: AppStyles.primaryButton,
                                ),
                                style: ElevatedButton.styleFrom(
                                  foregroundColor: AppColors.white,
                                  backgroundColor: AppColors.greenPrimary,
                                  shadowColor: Color.fromARGB(92, 0, 166, 144),
                                  elevation: 0,
                                  minimumSize: Size(20, 44),
                                  side: BorderSide(
                                      color: AppColors.greenPrimary,
                                      width: 1.0,
                                      style: BorderStyle.solid),
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(4.0)),
                                ),
                              ),
                            ))
                          } else ...{
                            Expanded(
                                child: ElevatedButton(
                              onPressed: () {
                                Navigator.pop(context);
                                StoreRedirect.redirect(
                                    androidAppId: "com.scrumpass.psm",
                                    iOSAppId: "1614034950");
                              },
                              child: Text(
                                "OK",
                                style: AppStyles.primaryButton,
                              ),
                              style: ElevatedButton.styleFrom(
                                foregroundColor: AppColors.white,
                                backgroundColor: AppColors.greenPrimary,
                                shadowColor: Color.fromARGB(92, 0, 166, 144),
                                elevation: 0,
                                minimumSize: Size(20, 44),
                                side: BorderSide(
                                    color: AppColors.greenPrimary,
                                    width: 1.0,
                                    style: BorderStyle.solid),
                                shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(4.0)),
                              ),
                            ))
                          }
                        ],
                      ),
                    ),
                  )
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _checkVersion() async {
    // var version = await ApiServices().checkVersion();
    // print(version);
    if (Common.update != null && Common.update['need_update'] == 1)
      _showUpdateDialog(Common.update);
  }

  String getDeviceType() {
    final data = MediaQueryData.fromWindow(WidgetsBinding.instance.window);
    return data.size.shortestSide < 550 ? 'phone' : 'tablet';
  }

  int getDeviceTypeGrid() {
    final data = MediaQueryData.fromWindow(WidgetsBinding.instance.window);
    return data.size.shortestSide < 550 ? 2 : 4;
  }

  Future<void> getDebugId() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    final id = preferences.getString('debugId');
    final env = preferences.getString('env');
    final server = env == "prodBackup" ? "DEU" : "SGP";
    if ((id?.isEmpty ?? true)) {
      final generateId = Helper.generateRandomString(10);
      final debugCode = "$generateId-$server";
      preferences.setString('debugId', debugCode);
      Common.debugId = debugCode;
    } else {
      if (id!.contains('-')) {
        final dashIndex = id.indexOf('-');
        final string = id.substring(0, dashIndex);
        final debugId = "$string-$server";
        Common.debugId = debugId;
        preferences.setString('debugId', debugId);
      } else {
        final debugId = "$id-$server";
        Common.debugId = debugId;
        preferences.setString('debugId', debugId);
      }
    }
  }

  BuildContext? myContext;

  void showShowcaseHome() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    final show = preferences.getString('showShowcaseHome');
    getVersion();
    if (show?.isEmpty ?? true) {
      preferences.setString('showShowcaseHome', '0');
      await Future.delayed(const Duration(seconds: 1));
      ShowCaseWidget.of(myContext!).startShowCase(
          [_showcase1, _showcase2, _showcase3, _showcase4, _showcase5]);
      Future.delayed(Duration(seconds: 2), () {
        setState(() {
          ignorePointer = false;
        });
      });
    } else {
      _checkVersion();
      setState(() {
        ignorePointer = false;
      });
    }
  }

  void getReportedQuestion() async {
    final result = await ApiServices().listReported();
  }

  Future<void> requestKey() async {
    await ApiServices().requestKey();
  }

  void getData() async {
    await getDebugId();
    SharedPreferences preferences = await SharedPreferences.getInstance();
    var env = preferences.getString('env');
    if ((env?.isNotEmpty ?? false) && environment != Environment.dev) {
      if (env == "prodBackup") {
        environment = Environment.prodBackup;
      }
    }
    final getKey = await ApiServices().getToken();
    if (getKey.isEmpty) {
      await ApiServices().changeEnvironment();
      final getKeyAgain = await ApiServices().getToken();
      if (getKeyAgain.isEmpty) {
        connectError();
      }
    }

    await sentryScope();
    sentEventEnvironment();
    await requestKey();
    getCategoryNumQuestion();
    getOtherApp();
    getFlashcard();
    _sendResultLater();
    getReportedQuestion();
    if (Common.premium) {
      get_description();
    }
  }

  Future<void> sentryScope() async {
    final user = {
      'id': Common.debugId,
      'premium': Common.premium,
    };
    await Sentry.configureScope((scope) {
      scope.setExtra('user_info', user);
      scope.setUser(SentryUser(id: Common.debugId));
    });
  }

  void sentEventEnvironment() {
    if (environment == Environment.prodBackup) {
      logEvent("environment_prodBackup", {});
    } else {
      logEvent("environment_${environment.text}", {});
    }
  }

  @override
  void initState() {
    super.initState();
    device_type = getDeviceType();
    if (device_type == 'phone') {
      ratio = 0.2;
    } else {
      ratio = 0.1;
    }
    getData();
    //Request_key();
    // getListQuiz(false);
    if (Common.appOpened) {
      logPage("Main");
      Common.appOpened = false;
    }
    //getUserName();
    getLang();
    Future.delayed(Duration.zero, () async {
      Common.deviceId = await _getId() ?? "";
      Provider.of<LocaleProvider>(context, listen: false)
          .setLocale(Locale(lang));
    });
    tz.initializeTimeZones();
    FirebasePerformance performance = FirebasePerformance.instance;
    performance.isPerformanceCollectionEnabled();
    showShowcaseHome();
    // SchedulerBinding.instance.addPostFrameCallback((_) => _checkVersion());
  }

  @override
  dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return IgnorePointer(
      ignoring: ignorePointer,
      child: AnnotatedRegion<SystemUiOverlayStyle>(
        value: SystemUiOverlayStyle(
            statusBarColor: Colors.transparent,
            statusBarBrightness: Brightness.light,
            statusBarIconBrightness: Brightness.dark), // dark status bar text
        child: WillPopScope(
            onWillPop: () async => false,
            child: Scaffold(
              appBar: PreferredSize(
                  preferredSize: const Size.fromHeight(1000),
                  child: AppBarWidget(
                    onTap: () {
                      Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (context) => UserInfo(
                                    userName: Common.username,
                                  ))).then((value) {
                        setState(() {});
                      });
                    },
                    context: context,
                  )),
              body: ShowCaseWidget(
                disableBarrierInteraction: true,
                disableMovingAnimation: true,
                builder: (context) {
                  myContext = context;
                  return Container(
                      decoration: BoxDecoration(
                        image: DecorationImage(
                          image:
                              AssetImage("assets/images/home_background.png"),
                          fit: BoxFit.fill,
                        ),
                      ),
                      child: Padding(
                          padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                          child: Container(
                            //color: Colors.black87,
                            //height: MediaQuery.of(context).size.aspectRatio,
                            child: Column(
                              children: [
                                GestureDetector(
                                    onTap: () {
                                      logEvent("main_dashboard_click", {});
                                      Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                              builder: (context) =>
                                                  Statistics()));
                                    },
                                    child: Showcase.withWidget(
                                        width: 300,
                                        height: 320,
                                        targetBorderRadius:
                                            BorderRadius.circular(8),
                                        container: Padding(
                                          padding:
                                              const EdgeInsets.only(top: 16),
                                          child: ShowcaseTooltip(
                                            showcaseContext: myContext!,
                                            posittionTop: -10,
                                            posittionLeft: 150,
                                            showBack: false,
                                            text: AppLocalizations.of(context)
                                                .homeShowcase1,
                                          ),
                                        ),
                                        key: _showcase1,
                                        child: DetailWidget())),
                                SizedBox(height: 16),
                                Expanded(
                                  child: SingleChildScrollView(
                                    child: ConstrainedBox(
                                      constraints: new BoxConstraints(
                                        minHeight:
                                            MediaQuery.of(context).size.height *
                                                0.3,
                                      ),
                                      child: Center(
                                        child: GridView.count(
                                          crossAxisSpacing: 16,
                                          mainAxisSpacing: 16,
                                          shrinkWrap: true,
                                          childAspectRatio: (1 / 1.1),
                                          crossAxisCount: getDeviceTypeGrid(),
                                          physics: ScrollPhysics(),
                                          // physics: NeverScrollableScrollPhysics(),
                                          children: <Widget>[
                                            Showcase.withWidget(
                                              width: 300,
                                              height: 320,
                                              targetBorderRadius:
                                                  BorderRadius.circular(8),
                                              container: Padding(
                                                padding: const EdgeInsets.only(
                                                    top: 16),
                                                child: ShowcaseTooltip(
                                                  showBack: true,
                                                  posittionTop: -10,
                                                  posittionLeft: 80,
                                                  showcaseContext: myContext!,
                                                  text: AppLocalizations.of(
                                                          context)
                                                      .homeShowcase2,
                                                ),
                                              ),
                                              key: _showcase2,
                                              child: InkWell(
                                                splashColor: Colors.transparent,
                                                child: Container(
                                                  padding: EdgeInsets.all(10),
                                                  decoration: BoxDecoration(
                                                    boxShadow: [
                                                      BoxShadow(
                                                        color: Colors.black
                                                            .withOpacity(
                                                                0.0275),
                                                        spreadRadius: 0,
                                                        blurRadius: 6.52,
                                                        offset: Offset(0,
                                                            8.15), // changes position of shadow
                                                      ),
                                                    ],
                                                    color: AppColors.white,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            8),
                                                  ),
                                                  child: Container(
                                                    // height: 9000,
                                                    child: Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .center,
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .center,
                                                      children: [
                                                        Container(
                                                          height: MediaQuery.of(
                                                                      context)
                                                                  .size
                                                                  .width *
                                                              ratio *
                                                              1.6,
                                                          width: MediaQuery.of(
                                                                      context)
                                                                  .size
                                                                  .width *
                                                              ratio *
                                                              1.6,
                                                          child: Image.asset(
                                                              'assets/images/take_exam_2.png',
                                                              fit: BoxFit.fill),
                                                        ),
                                                        Text(
                                                          AppLocalizations.of(
                                                                  context)
                                                              .examPractice,
                                                          style: AppStyles
                                                              .homeCardTitle,
                                                          textAlign:
                                                              TextAlign.center,
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                                onTap: () async {
                                                  logEvent(
                                                      "main_exam_click", {});
                                                  Navigator.push(
                                                          context,
                                                          MaterialPageRoute(
                                                              builder: (context) =>
                                                                  Widget_Qlist2(),
                                                              settings:
                                                                  RouteSettings(
                                                                      name:
                                                                          'ListQuiz')))
                                                      .then((value) {
                                                    setState(() {});
                                                  });
                                                  if (!status) {
                                                    print(
                                                        "Request Token Error");
                                                  }
                                                },
                                              ),
                                            ),
                                            Showcase.withWidget(
                                              width: 300,
                                              height: 320,
                                              targetBorderRadius:
                                                  BorderRadius.circular(8),
                                              container: Padding(
                                                padding: const EdgeInsets.only(
                                                    top: 16),
                                                child: ShowcaseTooltip(
                                                  showBack: true,
                                                  showcaseContext: myContext!,
                                                  posittionTop: -10,
                                                  posittionRight: 80,
                                                  text: AppLocalizations.of(
                                                          context)
                                                      .homeShowcase3,
                                                ),
                                              ),
                                              key: _showcase3,
                                              child: InkWell(
                                                splashColor: Colors.transparent,
                                                child: Container(
                                                  padding: EdgeInsets.all(10),
                                                  decoration: BoxDecoration(
                                                    boxShadow: [
                                                      BoxShadow(
                                                        color: Colors.black
                                                            .withOpacity(
                                                                0.0275),
                                                        spreadRadius: 0,
                                                        blurRadius: 6.52,
                                                        offset: Offset(0,
                                                            8.15), // changes position of shadow
                                                      ),
                                                    ],
                                                    color: AppColors.white,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            8),
                                                  ),
                                                  child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .center,
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .center,
                                                    children: [
                                                      Container(
                                                        height: MediaQuery.of(
                                                                    context)
                                                                .size
                                                                .width *
                                                            (ratio) *
                                                            1.6,
                                                        width: MediaQuery.of(
                                                                    context)
                                                                .size
                                                                .width *
                                                            (ratio) *
                                                            1.6,
                                                        child: Image.asset(
                                                            'assets/images/result_icon.png',
                                                            fit: BoxFit.fill),
                                                      ),
                                                      Text(
                                                        AppLocalizations.of(
                                                                context)
                                                            .result,
                                                        style: AppStyles
                                                            .homeCardTitle,
                                                        textAlign:
                                                            TextAlign.center,
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                                onTap: () {
                                                  logEvent(
                                                      "main_result_click", {});
                                                  Navigator.push(
                                                      context,
                                                      MaterialPageRoute(
                                                          builder: (context) =>
                                                              Widget_Result())).then(
                                                      (value) {
                                                    setState(() {});
                                                  });
                                                  //Navegra nas páginas
                                                },
                                              ),
                                            ),
                                            Showcase.withWidget(
                                              // tooltipPosition:
                                              //     TooltipPosition.top,
                                              width: 300,
                                              height: 320,
                                              targetBorderRadius:
                                                  BorderRadius.circular(8),
                                              container: ShowcaseTooltip(
                                                showBack: true,
                                                showcaseContext: myContext!,
                                                posittionBottom: -10,
                                                posittionLeft: 80,
                                                isArrowUp: false,
                                                text:
                                                    AppLocalizations.of(context)
                                                        .homeShowcase4,
                                              ),
                                              key: _showcase4,
                                              child: InkWell(
                                                splashColor: Colors.transparent,
                                                child: Stack(
                                                  children: [
                                                    Container(
                                                      width: double.infinity,
                                                      padding:
                                                          EdgeInsets.all(2),
                                                      decoration: BoxDecoration(
                                                        boxShadow: [
                                                          BoxShadow(
                                                            color: Colors.black
                                                                .withOpacity(
                                                                    0.0275),
                                                            spreadRadius: 0,
                                                            blurRadius: 6.52,
                                                            offset: Offset(0,
                                                                8.15), // changes position of shadow
                                                          ),
                                                        ],
                                                        color: AppColors.white,
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(8),
                                                      ),
                                                      child: Column(
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .center,
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .center,
                                                        children: [
                                                          Container(
                                                            height: MediaQuery.of(
                                                                        context)
                                                                    .size
                                                                    .width *
                                                                ratio *
                                                                1.6,
                                                            width: MediaQuery.of(
                                                                        context)
                                                                    .size
                                                                    .width *
                                                                ratio *
                                                                1.6,
                                                            child: Image.asset(
                                                                'assets/images/scrum_guide_2.png',
                                                                fit: BoxFit
                                                                    .contain),
                                                          ),
                                                          Text(
                                                            // AppLocalizations.of(context)
                                                            //     .scrumguide,
                                                            AppLocalizations.of(
                                                                    context)
                                                                .questionReview,
                                                            style: AppStyles
                                                                .homeCardTitle,
                                                            textAlign: TextAlign
                                                                .center,
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                    Positioned(
                                                        top: 10,
                                                        right: 10,
                                                        child: Container(
                                                          decoration:
                                                              BoxDecoration(
                                                            color: Color(
                                                                0xFFEDFBFF),
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        4),
                                                          ),
                                                          child: Padding(
                                                            padding:
                                                                const EdgeInsets
                                                                    .all(4.0),
                                                            child: Row(
                                                              children: [
                                                                SvgPicture
                                                                    .asset(
                                                                  'assets/images/star.svg',
                                                                  width: 10,
                                                                  height: 10,
                                                                  fit: BoxFit
                                                                      .contain,
                                                                ),
                                                                Padding(
                                                                  padding:
                                                                      const EdgeInsets
                                                                          .only(
                                                                          left:
                                                                              2),
                                                                  child: Text(
                                                                      "Premium",
                                                                      style: AppStyles.body.copyWith(
                                                                          color: Color(
                                                                              0xFF116C78),
                                                                          fontWeight: FontWeight
                                                                              .w600,
                                                                          fontSize:
                                                                              10)),
                                                                ),
                                                              ],
                                                            ),
                                                          ),
                                                        )),
                                                  ],
                                                ),
                                                onTap: () {
                                                  logEvent(
                                                      "main_review_click", {});

                                                  Navigator.push(
                                                      context,
                                                      MaterialPageRoute(
                                                          builder: (context) =>
                                                              QuestionReview()));
                                                },
                                              ),
                                            ),
                                            Showcase.withWidget(
                                              // tooltipPosition:
                                              //     TooltipPosition.top,
                                              width: 300,
                                              height: 320,
                                              targetBorderRadius:
                                                  BorderRadius.circular(8),
                                              container: ShowcaseTooltip(
                                                showBack: true,
                                                isArrowUp: false,
                                                posittionBottom: -10,
                                                posittionRight: 80,
                                                showcaseContext: myContext!,
                                                text:
                                                    AppLocalizations.of(context)
                                                        .homeShowcase5,
                                                lastItem: true,
                                              ),
                                              key: _showcase5,
                                              child: InkWell(
                                                splashColor: Colors.transparent,
                                                child: Stack(
                                                  children: [
                                                    Container(
                                                      width: double.infinity,
                                                      padding:
                                                          EdgeInsets.all(2),
                                                      decoration: BoxDecoration(
                                                        boxShadow: [
                                                          BoxShadow(
                                                            color: Colors.black
                                                                .withOpacity(
                                                                    0.0275),
                                                            spreadRadius: 0,
                                                            blurRadius: 6.52,
                                                            offset: Offset(0,
                                                                8.15), // changes position of shadow
                                                          ),
                                                        ],
                                                        color: AppColors.white,
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(8),
                                                      ),
                                                      child: Column(
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .center,
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .center,
                                                        children: [
                                                          Container(
                                                            height: MediaQuery.of(
                                                                        context)
                                                                    .size
                                                                    .width *
                                                                ratio *
                                                                1.6,
                                                            width: MediaQuery.of(
                                                                        context)
                                                                    .size
                                                                    .width *
                                                                ratio *
                                                                1.6,
                                                            child: Image.asset(
                                                                'assets/images/flashcard.png',
                                                                fit: BoxFit
                                                                    .contain),
                                                          ),
                                                          Text(
                                                            AppLocalizations.of(
                                                                    context)
                                                                .flashCard,
                                                            style: AppStyles
                                                                .homeCardTitle,
                                                            textAlign: TextAlign
                                                                .center,
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                    Positioned(
                                                        top: 10,
                                                        right: 10,
                                                        child: Container(
                                                          decoration:
                                                              BoxDecoration(
                                                            color: Color(
                                                                0xFFEDFBFF),
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        4),
                                                          ),
                                                          child: Padding(
                                                            padding:
                                                                const EdgeInsets
                                                                    .all(4.0),
                                                            child: Row(
                                                              children: [
                                                                SvgPicture
                                                                    .asset(
                                                                  'assets/images/star.svg',
                                                                  width: 10,
                                                                  height: 10,
                                                                  fit: BoxFit
                                                                      .contain,
                                                                ),
                                                                Padding(
                                                                  padding:
                                                                      const EdgeInsets
                                                                          .only(
                                                                          left:
                                                                              2),
                                                                  child: Text(
                                                                      "Premium",
                                                                      style: AppStyles.body.copyWith(
                                                                          color: Color(
                                                                              0xFF116C78),
                                                                          fontWeight: FontWeight
                                                                              .w600,
                                                                          fontSize:
                                                                              10)),
                                                                ),
                                                              ],
                                                            ),
                                                          ),
                                                        )),
                                                  ],
                                                ),
                                                onTap: () {
                                                  logEvent(
                                                      "main_flashcard_click",
                                                      {});
                                                  if (Common.premium) {
                                                    Navigator.push(
                                                        context,
                                                        MaterialPageRoute(
                                                            builder: (context) =>
                                                                FlashCard()));
                                                  } else {
                                                    Navigator.push(
                                                        context,
                                                        MaterialPageRoute(
                                                            builder: (context) =>
                                                                PaymentPage()));
                                                  }
                                                },
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          )));
                },
              ),
            )),
      ),
    );
  }
}
