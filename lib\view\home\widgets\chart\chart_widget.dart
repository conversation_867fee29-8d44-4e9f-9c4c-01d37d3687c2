import 'package:percent_indicator/percent_indicator.dart';
import 'package:psm_app/core/app_colors.dart';
import 'package:psm_app/core/core.dart';
import 'package:flutter/material.dart';

class ChartWidget extends StatefulWidget {
  final double percent;
  const ChartWidget({Key? key, required this.percent}) : super(key: key);

  @override
  _ChartWidgetState createState() => _ChartWidgetState();
}

class _ChartWidgetState extends State<ChartWidget>
    with SingleTickerProviderStateMixin {
  //animação
  late AnimationController _controller;
  late Animation<double> _animation;
  @override
  dispose() {
    _controller.dispose(); // you need this
    super.dispose();
  }

  void _initAnimation() {
    _controller = AnimationController(
        vsync: this,
        duration: Duration(seconds: widget.percent >= 0.5 ? 2 : 1));
    _animation =
        Tween<double>(begin: 0.0, end: widget.percent).animate(_controller);
    _controller.forward();
  }

  @override
  void initState() {
    _initAnimation();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, _) => CircularPercentIndicator(
        radius: 40, // width, height
        lineWidth: 4,
        percent: _animation.value,
        backgroundColor: AppColors.quizListBgChart,
        progressColor: AppColors.quizListProgessChart,
        circularStrokeCap: CircularStrokeCap.round,
        center: Text.rich(
          TextSpan(
              text: "${(_animation.value * 100).toInt()}",
              style: AppStyles.body.copyWith(
                  fontWeight: FontWeight.w600,
                  fontSize: 10,
                  color: const Color(0xff1F1F1F)),
              children: [
                TextSpan(
                    text: "%",
                    style: AppStyles.body.copyWith(
                        fontWeight: FontWeight.w600,
                        fontSize: 10,
                        color: const Color(0xff1F1F1F)))
              ]),
        ),
      ),
    );
  }
}
