import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:psm_app/view/question_review.dart';
import 'package:psm_app/view/widgets/error_dialog.dart';

import '../core/core.dart';
import '../data_sources/api_servies.dart';
import '../globals.dart';
import '../localization.dart';
import '../models/report_model.dart';
import 'widgets/success_dialog.dart';

class ReportDetail extends StatefulWidget {
  final Report report;

  const ReportDetail({Key? key, required this.report}) : super(key: key);

  @override
  State<ReportDetail> createState() => _ReportDetailState();
}

class _ReportDetailState extends State<ReportDetail> {
  late Future<List<ReportResponse>> response;
  bool isClosedReport = false;
  double heightScroll = 10;
  final ValueNotifier<double> notifier = ValueNotifier(0);
  final scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    isClosedReport = widget.report.reportStatus == ReportStatus.closed;
    response = getReportDetail();
  }

  void mesureQuestionWidget(context) {
    final TextPainter textPainter = TextPainter(
        text: TextSpan(
            text: widget.report.reportQuestion,
            style: AppStyles.body.copyWith(
                fontWeight: FontWeight.w500,
                fontSize: 14,
                height: 1.57,
                color: const Color(0xFF116C78))),
        textDirection: TextDirection.ltr)
      ..layout(minWidth: 0, maxWidth: MediaQuery.of(context).size.width - 64);
    heightScroll = textPainter.size.height + 60;
  }

  Future<List<ReportResponse>> getReportDetail() async {
    final result =
        await ApiServices().reportResponse({'id': widget.report.reportId});
    return result;
  }

  @override
  Widget build(BuildContext context) {
    mesureQuestionWidget(context);
    return LoaderOverlay(
      useDefaultLoading: false,
      overlayWidget: const Center(
        child: SpinKitRing(
          color: Colors.white,
          size: 50.0,
        ),
      ),
      overlayColor: Colors.black.withOpacity(0.8),
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: const Color(0xFFFAFAFA),
        appBar: AppBar(
          centerTitle: true,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back_ios_new),
            iconSize: 20.0,
            color: AppColors.blackText,
            onPressed: () {
              Navigator.pop(context);
            },
          ),
          title: Text(
            AppLocalizations.of(context).reportDetail,
            style: AppStyles.appBarTitle,
          ),
          backgroundColor: Colors.white,
          elevation: 1,
          shadowColor: const Color(0xFFD5D7DB),
        ),
        body: FutureBuilder<List<ReportResponse>>(
            future: response,
            builder: (context, snapshot) {
              if (snapshot.hasError) {
                return Center(
                    child: ErrorDialog(
                  error: snapshot.error.toString(),
                ));
              }
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Center(
                  child: CircularProgressIndicator(),
                );
              }
              if (snapshot.hasData) {
                return Stack(children: [
                  Column(
                    children: [
                      Expanded(
                          child: NotificationListener<ScrollNotification>(
                        onNotification: (n) {
                          if (n.metrics.pixels >= heightScroll) {
                            notifier.value = 1;
                          } else {
                            notifier.value = 0;
                          }
                          return false;
                        },
                        child: SingleChildScrollView(
                          controller: scrollController,
                          child: Padding(
                            padding: const EdgeInsets.only(bottom: 8),
                            child: Column(
                              children: [
                                QuestionBox(widget: widget),
                                _ReportItem(
                                  content: widget.report.reportDetail ?? '',
                                  time: widget.report.createdAt ?? '',
                                  isFirst: true,
                                ),
                                ..._responseList(snapshot.data ?? [])
                              ],
                            ),
                          ),
                        ),
                      )),
                      if (!isClosedReport) _bottomButton(context)
                    ],
                  ),
                  GestureDetector(
                    onTap: () {
                      scrollController.animateTo(0,
                          duration: const Duration(milliseconds: 500),
                          curve: Curves.easeOut);
                    },
                    child: HideableWidget(
                      height: heightScroll,
                      notifier: notifier,
                      text: widget.report.reportQuestion ?? '',
                    ),
                  ),
                ]);
              }
              return const Center(
                child: CircularProgressIndicator(),
              );
            }),
      ),
    );
  }

  Widget _bottomButton(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF8A8A8A).withOpacity(0.25),
            spreadRadius: 0,
            blurRadius: 16,
            offset: const Offset(4, -4),
          ),
        ],
      ),
      child: Padding(
        padding:
            const EdgeInsets.only(right: 16.0, left: 16, top: 12, bottom: 35),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
                child: Padding(
              padding: const EdgeInsets.only(right: 6),
              child: ElevatedButton(
                onPressed: () {
                  logEvent("close_report_click", {});
                  closeReport();
                },
                child: Text(
                  AppLocalizations.of(context).closeReport,
                  style: AppStyles.secondaryButton,
                  textAlign: TextAlign.center,
                ),
                style: ElevatedButton.styleFrom(
                  foregroundColor: AppColors.greenPrimary,
                  backgroundColor: Colors.white,
                  shadowColor: Colors.white,
                  elevation: 0,
                  minimumSize: const Size(20, 48),
                  side: BorderSide(
                      color: AppColors.greenPrimary,
                      width: 1.0,
                      style: BorderStyle.solid),
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(4.0)),
                ),
              ),
            )),
            Expanded(
                child: Padding(
              padding: const EdgeInsets.only(
                left: 6,
              ),
              child: ElevatedButton(
                onPressed: () {
                  logEvent("send_response_click", {});
                  _showModalSendResponse(widget.report.reportId)
                      .then((value) async {
                    if (value is String) {
                      context.loaderOverlay.show();
                      final result = await sendResponse(value);
                      context.loaderOverlay.hide();
                      if (result) {
                        showDialog(
                                context: context,
                                builder: (builder) => SuccessDialog(
                                    text: AppLocalizations.of(context)
                                        .sendResponseSuccess))
                            .then((value) => setState(() {
                                  print('=======================');
                                  response = getReportDetail();
                                }));
                      } else {
                        showDialog(
                            context: context,
                            builder: (builder) => const ErrorDialog());
                      }
                    }
                  });
                },
                style: ElevatedButton.styleFrom(
                  foregroundColor: AppColors.white,
                  backgroundColor: AppColors.greenPrimary,
                  shadowColor: const Color.fromARGB(92, 0, 166, 144),
                  elevation: 0,
                  minimumSize: const Size(20, 48),
                  side: BorderSide(
                      color: AppColors.greenPrimary,
                      width: 1.0,
                      style: BorderStyle.solid),
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(4.0)),
                ),
                child: Text(
                  AppLocalizations.of(context).sendResponse,
                  style: AppStyles.primaryButton,
                  textAlign: TextAlign.center,
                ),
              ),
            )),
          ],
        ),
      ),
    );
  }

  Future<bool> sendResponse(text) async {
    final data = {
      'report_id': widget.report.reportId,
      'response': text,
      'debug_id': Common.debugId
    };
    final result = await ApiServices().saveReportResponse(data);
    return result;
  }

  List<Widget> _responseList(List<ReportResponse> response) {
    List<Widget> listWidget = [];
    for (var element in response) {
      listWidget.add(_ReportItem(
        content: element.response ?? '',
        time: element.time ?? '',
        isAdmin: element.isAdmin ?? false,
      ));
    }
    return listWidget;
  }

  Future _showModalSendResponse(id) => showDialog(
      context: context,
      builder: (context) {
        return ResponseDialog(
          reportId: id,
        );
      });

  void closeReport() async {
    context.loaderOverlay.show();
    final result = await sendCloseReport();
    context.loaderOverlay.hide();
    if (!result) {
      showDialog(
          context: context,
          builder: (context) {
            return const ErrorDialog();
          });
    }
    // setState(() {});
    widget.report.reportStatus = ReportStatus.closed;
    Navigator.of(context).pop();
  }

  Future<bool> sendCloseReport() async {
    final data = {
      'id': widget.report.reportId,
    };
    final result = await ApiServices().closeReport(data);
    return result;
  }
}

class QuestionBox extends StatelessWidget {
  const QuestionBox({
    Key? key,
    required this.widget,
  }) : super(key: key);

  final ReportDetail widget;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 16, bottom: 8, left: 16, right: 16),
      child: DottedBorder(
        padding: EdgeInsets.zero,
        radius: const Radius.circular(6),
        dashPattern: const [7, 7],
        color: const Color(0xFF116C78),
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
              color: const Color(0xFFEDFBFF),
              borderRadius: BorderRadius.circular(6)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '${AppLocalizations.of(context).question}:',
                style: AppStyles.bodyBold.copyWith(
                    color: const Color(0xFF116C78), fontSize: 16, height: 1.5),
              ),
              Text(
                widget.report.reportQuestion ?? '',
                style: AppStyles.body.copyWith(
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                    height: 1.57,
                    color: const Color(0xFF116C78)),
              )
            ],
          ),
        ),
      ),
    );
  }
}

class HideableWidget extends StatelessWidget {
  final double height;
  final ValueNotifier<double> notifier;
  final String text;

  const HideableWidget(
      {Key? key,
      required this.height,
      required this.notifier,
      required this.text})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<double>(
      valueListenable: notifier,
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, value == 1 ? 0 : -height),
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
            height: 38,
            color: const Color(0xFFEDFBFF),
            child: Text(
              text,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: AppStyles.body.copyWith(
                  fontWeight: FontWeight.w400,
                  fontSize: 14,
                  height: 1.57,
                  color: const Color(0xFF116C78)),
            ),
          ),
        );
      },
    );
  }
}

class _ReportItem extends StatelessWidget {
  final String content;
  final String time;
  final bool isFirst;
  final bool isAdmin;
  const _ReportItem(
      {Key? key,
      required this.content,
      required this.time,
      this.isFirst = false,
      this.isAdmin = false})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 20,
            )
          ],
          borderRadius: BorderRadius.circular(6)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            isFirst
                ? '${AppLocalizations.of(context).reportContent}:'
                : isAdmin
                    ? '${AppLocalizations.of(context).scrumpassResponse}:'
                    : '${AppLocalizations.of(context).yourResponse}:',
            style: AppStyles.bodyBold.copyWith(
                fontSize: 16,
                height: 1.5,
                color:
                    isAdmin ? AppColors.greenPrimary : AppColors.lightBlueText),
          ),
          const SizedBox(
            height: 8,
          ),
          Text(
            content,
            style: AppStyles.body.copyWith(
                color: const Color(0xFF0B2520),
                fontWeight: FontWeight.w500,
                fontSize: 14,
                height: 1.57),
          ),
          const SizedBox(
            height: 8,
          ),
          Align(
            alignment: Alignment.centerRight,
            child: Text(
              time,
              style: AppStyles.body.copyWith(
                  color: AppColors.grey,
                  fontWeight: FontWeight.w500,
                  fontSize: 14,
                  height: 1.57),
            ),
          )
        ],
      ),
    );
  }
}

class ResponseDialog extends StatefulWidget {
  const ResponseDialog({Key? key, required this.reportId}) : super(key: key);
  final String reportId;
  @override
  _ResponseDialogState createState() => _ResponseDialogState();
}

class _ResponseDialogState extends State<ResponseDialog> {
  late TextEditingController controltext;
  @override
  void initState() {
    super.initState();
    controltext = TextEditingController();
  }

  @override
  void dispose() {
    super.dispose();
    controltext.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: const EdgeInsets.symmetric(horizontal: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Text(
                "${AppLocalizations.of(context).yourResponse}:",
                style: AppStyles.reportDialogTitle,
              ),
              const SizedBox(
                height: 4,
              ),
              TextField(
                onTap: () {
                  setState(() {
                    isEmpty = false;
                  });
                },
                decoration: InputDecoration(
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(
                      color: isEmpty ? Colors.red : const Color(0xFFACB7C5),
                    ),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(
                        color: isEmpty ? Colors.red : const Color(0xFFACB7C5),
                        width: 1.0),
                  ),
                ),
                autofocus: false,
                maxLines: 3,
                // expands: true,
                keyboardType: TextInputType.text,
                controller: controltext,
                textAlignVertical: TextAlignVertical.top,
              ),
              isEmpty == true
                  ? Padding(
                      padding: const EdgeInsets.only(top: 10.0),
                      child: Text(
                        AppLocalizations.of(context).emptyField,
                        style: AppStyles.reportDialogText
                            .copyWith(color: Colors.red),
                      ),
                    )
                  : Container(),
              const SizedBox(
                height: 16,
              ),
              Row(
                children: [
                  Expanded(
                      child: Padding(
                    padding: const EdgeInsets.only(right: 6),
                    child: ElevatedButton(
                      onPressed: close,
                      child: Text(
                        AppLocalizations.of(context).cancel,
                        style: AppStyles.secondaryButton,
                        textAlign: TextAlign.center,
                      ),
                      style: ElevatedButton.styleFrom(
                        foregroundColor: AppColors.greenPrimary,
                        backgroundColor: Colors.white,
                        shadowColor: Colors.white,
                        elevation: 0,
                        minimumSize: const Size(20, 44),
                        side: BorderSide(
                            color: AppColors.greenPrimary,
                            width: 1.0,
                            style: BorderStyle.solid),
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(4.0)),
                      ),
                    ),
                  )),
                  Expanded(
                      child: Padding(
                    padding: const EdgeInsets.only(left: 6),
                    child: ElevatedButton(
                      onPressed: submit,
                      child: Text(
                        AppLocalizations.of(context).send,
                        style: AppStyles.primaryButton,
                      ),
                      style: ElevatedButton.styleFrom(
                        foregroundColor: AppColors.white,
                        backgroundColor: AppColors.greenPrimary,
                        shadowColor: const Color.fromARGB(92, 0, 166, 144),
                        elevation: 0,
                        minimumSize: const Size(20, 44),
                        side: BorderSide(
                            color: AppColors.greenPrimary,
                            width: 1.0,
                            style: BorderStyle.solid),
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(4.0)),
                      ),
                    ),
                  ))
                ],
              )
            ],
          ),
        ),
      ),
    );
  }

  bool isEmpty = false;
  void submit() async {
    if (controltext.text.isEmpty) {
      setState(() {
        isEmpty = true;
      });
    } else {
      Navigator.of(context).pop(controltext.text);
    }
  }

  void close() async {
    Navigator.pop(context, false);
  }

  Future success() => showDialog(
      context: context,
      builder: (context) => const SuccessDialog(text: 'Send response success'));
  Future error() =>
      showDialog(context: context, builder: (context) => const ErrorDialog());
  DropdownMenuItem<int> buildMenuItem(int e) =>
      DropdownMenuItem(value: e, child: Text(e.toString()));
}
