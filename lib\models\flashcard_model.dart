
class Flashcard {
    String? fid;
    String? term;
    String? explanation;
    String? explanationVn;
    String? certificate;
    String? labels;
    String? status;
    String? createAt;

    Flashcard({this.fid, this.term, this.explanation, this.explanationVn, this.certificate, this.labels, this.status, this.createAt});

    Flashcard.fromJson(Map<String, dynamic> json) {
        fid = json["fid"];
        term = json["term"];
        explanation = json["explanation"];
        explanationVn = json["explanation_vn"];
        certificate = json["certificate"];
        labels = json["labels"];
        status = json["status"];
        createAt = json["create_at"];
    }

    Map<String, dynamic> toJson() {
        final Map<String, dynamic> _data = <String, dynamic>{};
        _data["fid"] = fid;
        _data["term"] = term;
        _data["explanation"] = explanation;
        _data["explanation_vn"] = explanationVn;
        _data["certificate"] = certificate;
        _data["labels"] = labels;
        _data["status"] = status;
        _data["create_at"] = createAt;
        return _data;
    }
}