import 'package:psm_app/core/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:psm_app/core/app_text_styles.dart';
import 'package:psm_app/core/core.dart';
import 'package:psm_app/localization.dart';

import 'package:psm_app/view/home/<USER>/chart/result_chart_widget.dart';
import 'package:psm_app/view/home/<USER>/chart/result_detail_chart_widget.dart';

class ScoreCardResultWidget extends StatelessWidget {
  final double percent;
  final int correct;
  final int incorrect;
  final int passPercent;
  final int num;
  final String time;
  final int timeDoQuiz;
  const ScoreCardResultWidget(
      {Key? key,
      required this.percent,
      required this.correct,
      required this.incorrect,
      required this.num,
      required this.time,
      required this.passPercent,
      required this.timeDoQuiz})
      : super(key: key);
  @override
  Widget build(BuildContext context) {
    int all = correct + incorrect;
    return Padding(
      padding: const EdgeInsets.only(left: 16, right: 16, top: 10),
      child: Container(
        height: 127,
        decoration: BoxDecoration(boxShadow: [
          BoxShadow(
            color: Color(0xFF8A8A8A).withOpacity(0.25),
            spreadRadius: 0,
            blurRadius: 16,
            offset: Offset(4, 4), // changes position of shadow
          ),
        ], color: AppColors.white, borderRadius: BorderRadius.circular(15)),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                flex: 1,
                child: ResultDetailChartWidget(
                    percent: percent,
                    status: percent >= passPercent / 100 ? "Pass" : "Fail"),
              ),
              Expanded(
                flex: 3,
                child: Padding(
                  padding: const EdgeInsets.only(left: 24),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "$correct/$all " +
                            AppLocalizations.of(context).rightAns,
                        style: AppStyles.titleScoreCardResult,
                      ),
                      Text(
                          "( " +
                              AppLocalizations.of(context)
                                  .passRequirement
                                  .replaceAll("{e}", passPercent.toString()) +
                              " )",
                          style: AppStyles.textScoreCardResult),
                      Padding(
                        padding: const EdgeInsets.only(top: 15),
                        child: Wrap(
                          direction: Axis.horizontal,
                          children: [
                            Wrap(
                              children: [
                                Padding(
                                  padding: const EdgeInsets.only(right: 4),
                                  child: Icon(
                                    Icons.access_time,
                                    color: AppColors.lightGreyText,
                                    size: 15,
                                  ),
                                ),
                                Text(
                                    (timeDoQuiz / 60).round().toString() +
                                        ' ' +
                                        AppLocalizations.of(context)
                                            .minutesShort +
                                        " • ",
                                    style: AppStyles.listQuizInfo)
                              ],
                            ),
                            Wrap(
                              children: [
                                Padding(
                                  padding: const EdgeInsets.only(right: 4),
                                  child: Icon(
                                    Icons.calendar_today_outlined,
                                    color: AppColors.lightGreyText,
                                    size: 15,
                                  ),
                                ),
                                Text(time, style: AppStyles.listQuizInfo),
                              ],
                            )
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
