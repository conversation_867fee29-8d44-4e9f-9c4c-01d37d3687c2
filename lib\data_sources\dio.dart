import 'dart:convert';
import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dio/dio.dart';
import 'package:firebase_performance_dio/firebase_performance_dio.dart';
import 'package:psm_app/globals.dart';
import 'package:sentry_dio/sentry_dio.dart';
import 'package:shared_preferences/shared_preferences.dart';


class NewDio {
  Dio getDio() {
    final dio = Dio();
    final performanceInterceptor = DioFirebasePerformanceInterceptor();
    dio.interceptors.add(performanceInterceptor);
    dio.interceptors.add(CustomInterceptors());
    dio.addSentry();
    return dio;
  }
}

class CustomInterceptors extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    log('REQUEST[${options.method}] => PATH: ${options.path}');
    // final request = options.data as FormData;
    // log(json.encode(Map.fromEntries(request.fields)));
    return super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    log('=============================================================');
    log('RESPONSE[${response.statusCode}] => PATH: ${response.requestOptions.path}');
    log(response.data);
    log('=============================================================');
    return super.onResponse(response, handler);
  }

  @override
  void onError(DioError err, ErrorInterceptorHandler handler) {
    FormData? request;
    String? requestJson;
    log('=============================================================');
    log('ERROR[${err.response?.statusCode}] => PATH: ${err.requestOptions.path}');
    if (err.requestOptions.data is FormData) {
      request = err.requestOptions.data as FormData;
      requestJson = json.encode(Map.fromEntries(request.fields));
      log(requestJson);
    }
    log(err.response?.data ?? err.type.name);
    log('=============================================================');
    // final data = {
    //   'type': 'API Error',
    //   'code': err.response?.statusCode,
    //   'path': err.requestOptions.path,
    //   'request': request?.fields.toString() ?? '',
    //   'response': err.response?.data
    // };
    // ApiServices().sendErrorReport(jsonEncode(data));
    // log to firebase
    final errorData = {
      "type": err.type.name,
      "code": err.response?.statusCode,
      "url": err.requestOptions.path,
      "request": requestJson ?? '',
      "response": err.response?.data,
      "version": Common.version,
      "id": Common.debugId,
      "time": DateTime.now().toString(),
      "app": Common.appName,
    };
    final db = FirebaseFirestore.instance;
    db.collection(Common.firestoreApilogCollection).add(errorData).then(
        (documentSnapshot) =>
            log("Added Data with ID: ${documentSnapshot.id}"));
    logEvent(
      "api_error",
      {
        "api_error": jsonEncode(errorData),
      },
    );
    if (err.type.name == "other" && environment == Environment.prodBackup) {
      environment = Environment.prod;
      removeEnvLocal();
      logEvent(
        "api_error_prodBackup",
        {},
      );
    }
    return super.onError(err, handler);
  }

  void removeEnvLocal() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    preferences.remove('env');
  }
}
