library my_prj.globals;

import 'dart:convert';

import 'package:dart_ipify/dart_ipify.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'dart:io' show Platform;

import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:psm_app/data_sources/api_servies.dart';

String generateMd5(String input) {
  Codec<String, String> stringToBase64 = utf8.fuse(base64);
  return stringToBase64.encode(input);
}

enum Environment { dev, prod, prodBackup, local }

extension EnvironmentExt on Environment {
  String get url {
    switch (this) {
      case Environment.dev:
        return "https://dev.scrumpass.com/api/";
      case Environment.prod:
        return "https://exam.scrumpass.com/api/";
      case Environment.prodBackup:
        return "https://exam-pass.com/api/";
      case Environment.local:
        return "http://localhost/scrumpass-exam/api/";
    }
  }

  String get urlPremium {
    switch (this) {
      case Environment.dev:
        return "https://dev.scrumpass.com/api_premium/";
      case Environment.prod:
        return "https://exam.scrumpass.com/api_premium/";
      case Environment.prodBackup:
        return "https://exam-pass.com/api_premium/";
      case Environment.local:
        return "http://localhost/scrumpass-exam/api_premium/";
    }
  }

  String get text {
    switch (this) {
      case Environment.dev:
        return "dev";
      case Environment.prod:
      case Environment.prodBackup:
        return "exam";
      case Environment.local:
        return "local";
    }
  }
}

/// Môi trường prod: "exam"
///
/// Môi trường dev: "dev"
Environment environment = Environment.prod;
String get apiUrl => environment.url;
String get apiUrlPremium => environment.urlPremium;

// String apiUrl = "https://" + environment + ".scrumpass.com/api/";
// String apiUrlPremium = "https://" + environment + ".scrumpass.com/api_premium/";
// String apiUrl = "http://localhost/scrumpass-exam/api/";
// String apiUrlPremium = "http://localhost/scrumpass-exam/api_premium/";

class getUserInfor {
  String get url => apiUrl + "quiz_list_api";
  String get url_premium => apiUrlPremium + "quiz_list_api";
  //String url = "http://***********/api/quiz_list_api";
  String appkey = generateMd5("bXlfYXBwbGljYXRpb25fc2VjcmV0" +
      DateTime.now().millisecondsSinceEpoch.toString());
}

class getDescription {
  String url_depcription = apiUrlPremium + "get_description";
}

class getOtherAppApi {
  String url_other_app = apiUrl + "get_other_app";
}

class getNumCategory {
  String url_category_num = apiUrl + "get_num_category";
}

class getTipsApi {
  String url_tips = apiUrl + "get_list_tips";
}

class url {
  // final String mainUrl = "http://***********/scrumpass-exam/api/";
  final String mainUrl = apiUrl;
}

class Common {
  static String apiDomain = "https://exam.scrumpass.com/";
  static String username = '';
  static String appName = 'PSM Exam Simulator';
  static String appid = 'com.scrumpass.psm';
  static var update;
  static var appOpened = true;
  static String platform = '';
  static String categoryQuestions = "";
  static String premiumType = '';
  static String revenuecatAndroid = 'goog_aPSXcVtCrloBuJlyvabRKODgTOJ';
  static String revenuecatiOS = 'appl_hcokrYGwjXQbLSOwxfxGbjxCXLQ';
  static bool premium = false;
  static bool trial = false;
  static String version = '';
  static String deviceId = '';
  static String trialEndDate = '';
  static String appleAppId = "1614034950";
  static List supportAnotherLanguage = ['de', 'fr', 'hi'];
  static String flashcardCertId = "2";
  static String certificate_name = 'PSM';
  static String debugId = '';
  static String firestoreApilogCollection = 'api-err-log';
  static String amplitudeKey = 'bd1c7694261b1a2ee6f3aa6fe8164866';

  /// những app sẽ shuffle câu trả lời
  static const List shuffleApp = [
    'com.scrumpass.psm',
    'com.scrumpass.psm2',
    'com.scrumpass.pspo',
    'com.scrumpass.pspo2'
  ];
}

Future<void> logEvent(String name, Map<String, dynamic> map) async {
  final Map<String, Object> parameters = {
    ...map,
    ...{
      "app_name": Common.appName,
      "subscription_type": Common.premium ? "Premium" : "Free",
      "platform": Common.platform,
      "device_id": Common.deviceId,
    },
  };
  await FirebaseAnalytics.instance.logEvent(name: name, parameters: parameters);

  if (environment == Environment.dev) {
    parameters['event_name'] = name;
    await ApiServices().saveEventLog(parameters);
  }
}

Future<void> logPage(String name) async {
  await FirebaseAnalytics.instance.setCurrentScreen(
      screenName: name, screenClassOverride: name.replaceAll(' ', ''));
}

class CommonFont {
  static String size = '';
}

Future<String> GetDeviceInfo() async {
  DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
  String ipv4 = "";
  try {
    ipv4 = await Ipify.ipv4().timeout(const Duration(seconds: 10));
  } catch (e) {
    print(e);
  }
  String brand = "";
  String model = "";
  //ios
  if (Platform.isIOS) {
    Common.platform = 'iOS';
    IosDeviceInfo androidInfo = await deviceInfoPlugin.iosInfo;
    if (androidInfo.systemName != "") {
      brand = androidInfo.name!;
    } else {
      brand = "";
    }
  }
  //end ios
  //android
  if (Platform.isAndroid) {
    Common.platform = 'Android';
    AndroidDeviceInfo androidInfo = await deviceInfoPlugin.androidInfo;
    if (androidInfo.brand != "") {
      brand = androidInfo.brand!;
    } else {
      brand = "";
    }
    if (androidInfo.model != "") {
      model = androidInfo.model!;
    } else {
      model = "";
    }
  }
  //end android
  String info = brand + "-" + model + "-" + ipv4;
  return info;
}

void showImagePopup(BuildContext context, String imageUrl) {
  showDialog(
    context: context,
    barrierDismissible: true,
    builder: (BuildContext context) {
      return Dialog(
        insetPadding: const EdgeInsets.all(10),
        child: ConstrainedBox(
          constraints: const BoxConstraints(minHeight: 300),
          child: InteractiveViewer(
            boundaryMargin: const EdgeInsets.all(10.0),
            minScale: 0.5,
            maxScale: 4.0,
            child: Image.network(
              imageUrl,
              width: MediaQuery.of(context).size.width,
              fit: BoxFit.contain,
            ),
          ),
        ),
      );
    },
  );
}

String extractImagePathFromHtml(String htmlString) {
  final RegExp regex = RegExp(r'<img src="([^"]+)"');

  final match = regex.firstMatch(htmlString);
  if (match != null) {
    return match.group(1) ?? '';
  } else {
    return '';
  }
}

String replaceSpecialCharacters(String input) {
  Map<String, String> replacements = {
    "&ndash;": "-",
  };

  replacements.forEach((key, value) {
    input = input.replaceAll(key, value);
  });

  return input;
}

String replaceLinkImg(String data) {
  String newStr = data
      .replaceAll('../../../', Common.apiDomain)
      .replaceAll('../../', Common.apiDomain)
      .replaceAll('\n', '<br>');
  return newStr;
}
