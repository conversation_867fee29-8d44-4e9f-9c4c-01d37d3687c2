class Question {
  String? id;
  String? type;
  late String question;
  List<Options>? options;
  String? correct;
  List<int>? correctIndex;
  String? quizId;
  String? description;
  String? environment;
  String? nextQuestion;

  Question(
      {this.id,
      this.type,
      required this.question,
      this.options,
      this.description,
      this.environment,
      this.quizId,
      this.correct,
      this.correctIndex,
      this.nextQuestion});

  bool get hasGroupQuestion => nextQuestion != null;

  Question.fromJson(Map<String, dynamic> json) {
    if (json["id"] is String) id = json["id"];
    if (json["quizId"] is String) quizId = json["quizId"];
    if (json["type"] is String) type = json["type"];
    if (json["question"] is String) question = json["question"];
    if (json["description"] is String) description = json["description"];
    if (json["environment"] is String) environment = json["environment"];
    if (json["options"] is List) {
      options = json["options"] == null
          ? null
          : (json["options"] as List).map((e) => Options.fromJson(e)).toList();
    }
    if (json["correct"] is String) correct = json["correct"];
    if (json["correct_index"] is List) {
      correctIndex = json["correct_index"] == null
          ? null
          : List<int>.from(json["correct_index"]);
    }

    if (json["next_qid"] is String) nextQuestion = json["next_qid"];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data["id"] = id;
    data["quizId"] = quizId;
    data["type"] = type;
    data["question"] = question;
    data["description"] = description;
    data["environment"] = environment;
    if (options != null) {
      data["options"] = options?.map((e) => e.toJson()).toList();
    }
    data["correct"] = correct;
    data["correct_index"] = correctIndex;

    return data;
  }
}

class Options {
  late String oid;
  String? qid;
  late String qOption;
  dynamic? qOptionMatch;
  String? qOption1;
  String? score;
  String? qOptionMatch1;

  Options(
      {required this.oid,
      this.qid,
      required this.qOption,
      this.qOptionMatch,
      this.qOption1,
      this.score,
      this.qOptionMatch1});

  Options.fromJson(Map<String, dynamic> json) {
    if (json["oid"] is String) oid = json["oid"];
    if (json["qid"] is String) qid = json["qid"];
    if (json["q_option"] is String) qOption = json["q_option"];
    qOptionMatch = json["q_option_match"];
    if (json["q_option1"] is String) qOption1 = json["q_option1"];
    if (json["score"] is String) score = json["score"];
    if (json["q_option_match1"] is String) {
      qOptionMatch1 = json["q_option_match1"];
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data["oid"] = oid;
    data["qid"] = qid;
    data["q_option"] = qOption;
    data["q_option_match"] = qOptionMatch;
    data["q_option1"] = qOption1;
    data["score"] = score;
    data["q_option_match1"] = qOptionMatch1;
    return data;
  }
}
