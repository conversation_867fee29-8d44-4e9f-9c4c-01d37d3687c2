import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:psm_app/core/app_colors.dart';
import 'package:psm_app/core/app_styles.dart';
import 'package:psm_app/globals.dart';
import 'package:psm_app/localization.dart';
import 'package:psm_app/models/qlist_model.dart';
import 'package:intl/intl.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:psm_app/view/detail_result/detail_result.dart';
import 'package:psm_app/view/exam/exam.dart';
import 'package:psm_app/view/widgets/error_dialog.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ExamDetail extends StatefulWidget {
  final Qlist user;
  ExamDetail({Key? key, required this.user}) : super(key: key);

  @override
  State<ExamDetail> createState() => _ExamDetailState();
}

class _ExamDetailState extends State<ExamDetail> {
  @override
  void initState() {
    super.initState();
    logPage("Exam Detail");
  }

  String convertTimeStampToHumanDate(int timeStamp) {
    var dateToTimeStamp = DateTime.fromMillisecondsSinceEpoch(timeStamp * 1000);
    return DateFormat('dd/MM/yyyy hh:mm:ss').format(dateToTimeStamp);
  }

  @override
  Widget build(BuildContext context) {
    final start = int.parse(widget.user.start_date); // timestamp in seconds
    final end = int.parse(widget.user.end_date);
    var timeStart = convertTimeStampToHumanDate(start);
    var timeEnd = convertTimeStampToHumanDate(end);
    String des = widget.user.description;
    if (des == '') {
      des = AppLocalizations.of(context).noInfo;
    }

    void _clearResumeQuiz() async {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String resumeQuiz = prefs.getString('resumeQuiz') ?? '';
      if (resumeQuiz != '') {
        prefs.remove('answered_' + resumeQuiz);
        prefs.remove('bookmarked_' + resumeQuiz);
        prefs.remove('answeredText_' + resumeQuiz);
        prefs.remove('timeDoQuiz_' + resumeQuiz);
        prefs.remove('timeStartQuiz_' + resumeQuiz);
        prefs.remove('answeredId_' + resumeQuiz);
        prefs.remove('resumeQuiz');
        prefs.remove('questionResume');
      }
    }

    void _showConfirmDialog() {
      showDialog<String>(
        context: context,
        builder: (BuildContext context) => Dialog(
          // contentPadding: EdgeInsets.fromLTRB(16, 30, 16, 24),
          insetPadding: const EdgeInsets.symmetric(horizontal: 16),
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.fromLTRB(16, 30, 16, 24),
              child: Column(
                children: [
                  Container(
                    width: 196,
                    height: 139,
                    decoration: const BoxDecoration(
                        image: DecorationImage(
                      image: AssetImage("assets/images/info_dialog.png"),
                      fit: BoxFit.fill,
                    )),
                  ),
                  Text(
                    AppLocalizations.of(context).confirmDoQuiz,
                    style: AppStyles.dialogText,
                    textAlign: TextAlign.center,
                  ),
                  Padding(
                    padding: const EdgeInsets.only(top: 24),
                    child: ConstrainedBox(
                      constraints: new BoxConstraints(
                        maxWidth: 600.0,
                      ),
                      child: Container(
                        width: MediaQuery.of(context).size.width,
                        child: Row(
                          children: [
                            Expanded(
                                child: Padding(
                              padding: const EdgeInsets.only(right: 6),
                              child: ElevatedButton(
                                onPressed: () {
                                  Navigator.pop(context);
                                },
                                child: Text(
                                  AppLocalizations.of(context).no,
                                  style: AppStyles.secondaryButton,
                                  textAlign: TextAlign.center,
                                ),
                                style: ElevatedButton.styleFrom(
                                  foregroundColor: AppColors.greenPrimary,
                                  backgroundColor: Colors
                                      .white, //specify the color of the button's text and icons as well as the overlay colors used to indicate the hover, focus, and pressed states
                                  shadowColor: Colors
                                      .white, //specify the button's elevation color
                                  elevation: 0, //buttons Material shadow
                                  // padding: const EdgeInsets.only(top: 4.0, bottom: 4.0, right: 8.0, left: 8.0), //specify the button's Padding
                                  minimumSize: const Size(20,
                                      44), //specify the button's first: width and second: height
                                  side: BorderSide(
                                      color: AppColors.greenPrimary,
                                      width: 1.0,
                                      style: BorderStyle
                                          .solid), //set border for the button
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(4.0)),
                                ),
                              ),
                            )),
                            Expanded(
                                child: Padding(
                              padding: const EdgeInsets.only(left: 6),
                              child: ElevatedButton(
                                onPressed: () {
                                  Navigator.pop(context);
                                  _clearResumeQuiz();
                                  Navigator.pushReplacement(
                                      context,
                                      MaterialPageRoute(
                                          builder: (context) => Exam(
                                              idQuiz: widget.user.quid,
                                              exam_quiz: widget.user.exam_quiz,
                                              quizName: widget.user.quiz_name,
                                              questionSelection: widget
                                                  .user.question_selection,
                                              passPercent: int.parse(
                                                  widget.user.pass_percentage),
                                              duration: int.parse(
                                                  widget.user.duration))));
                                },
                                child: Text(
                                  "OK",
                                  style: AppStyles.primaryButton,
                                ),
                                style: ElevatedButton.styleFrom(
                                  foregroundColor: AppColors.white,
                                  backgroundColor: AppColors.greenPrimary,
                                  shadowColor:
                                      const Color.fromARGB(92, 0, 166, 144),
                                  elevation: 0,
                                  minimumSize: const Size(20, 44),
                                  side: BorderSide(
                                      color: AppColors.greenPrimary,
                                      width: 1.0,
                                      style: BorderStyle.solid),
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(4.0)),
                                ),
                              ),
                            ))
                          ],
                        ),
                      ),
                    ),
                  )
                ],
              ),
            ),
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black),
          onPressed: () => Navigator.of(context).pop(),
        ),
        titleSpacing: 0,
        centerTitle: true,
        backgroundColor: /* Color(0xffE5E5E5) */ Colors.white,
        title: Text(
          widget.user.quiz_name,
          style: AppStyles.titleBold.copyWith(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.w700,
          ),
        ),
        elevation: 0,
      ),
      body: Container(
        color: /* Color(0xffE5E5E5) */ Colors.white,
        child: Column(
          children: [
            Container(
              margin: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                  border: Border.all(color: const Color(0xffdddddd)),
                  gradient: const LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Color(0xFF2F8FFF),
                      Color(0xFF0065DC),
                    ],
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xff000000).withOpacity(0.0275),
                      spreadRadius: 0,
                      blurRadius: 6.52,
                      offset: const Offset(0.0, 8.15),
                    ),
                    BoxShadow(
                      color: const Color(0xff000000).withOpacity(0.0531),
                      spreadRadius: 0,
                      blurRadius: 46.85,
                      offset: const Offset(0.0, 64.81),
                    ),
                  ],
                  borderRadius: BorderRadius.circular(8)),
              child: Stack(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                        vertical: 12, horizontal: 16),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Container(
                          width: MediaQuery.of(context).size.width * 0.5,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                AppLocalizations.of(context).quizDetail,
                                style: AppStyles.heading15.copyWith(
                                    fontWeight: FontWeight.w700,
                                    fontSize: 18,
                                    height: 1.44,
                                    color: Colors.white),
                              ),
                              RichText(
                                text: TextSpan(
                                  text: '  • ' +
                                      AppLocalizations.of(context)
                                          .numOfQuestion +
                                      ": ",
                                  style: AppStyles.body.copyWith(
                                      color: const Color(0xFFE1EFFF),
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                      height: 1.57),
                                  children: <TextSpan>[
                                    TextSpan(
                                        text: widget.user.noq,
                                        style: AppStyles.body.copyWith(
                                            color: const Color(0xFFE1EFFF),
                                            fontSize: 14,
                                            fontWeight: FontWeight.w500,
                                            height: 1.57))
                                  ],
                                ),
                              ),
                              RichText(
                                text: TextSpan(
                                  text: '  • ' +
                                      AppLocalizations.of(context).duration +
                                      ": ",
                                  style: AppStyles.body.copyWith(
                                      color: const Color(0xFFE1EFFF),
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                      height: 1.57),
                                  children: <TextSpan>[
                                    TextSpan(
                                      text: widget.user.duration +
                                          " " +
                                          AppLocalizations.of(context).minutes,
                                      style: AppStyles.body.copyWith(
                                          color: const Color(0xFFE1EFFF),
                                          fontSize: 14,
                                          fontWeight: FontWeight.w500,
                                          height: 1.57),
                                    ),
                                  ],
                                ),
                              ),
                              /* Padding(
                                padding: const EdgeInsets.only(top: 8),
                                child: RichText(
                                  text: TextSpan(
                                    text: '• ' +
                                        AppLocalizations.of(context).timeStart +
                                        ": ",
                                    style: AppStyles.body,
                                    children: <TextSpan>[
                                      TextSpan(
                                          text: timeStart,
                                          style: TextStyle(fontWeight: FontWeight.bold)),
                                    ],
                                  ),
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(top: 8),
                                child: RichText(
                                  text: TextSpan(
                                    text: '• ' +
                                        AppLocalizations.of(context).timeEnd +
                                        ": ",
                                    style: AppStyles.body,
                                    children: <TextSpan>[
                                      TextSpan(
                                          text: timeEnd,
                                          style: TextStyle(fontWeight: FontWeight.bold)),
                                    ],
                                  ),
                                ),
                              ), */
                              Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    '  • ',
                                    style: AppStyles.body.copyWith(
                                        color: const Color(0xFFE1EFFF),
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500,
                                        height: 1.57),
                                  ),
                                  Flexible(
                                      child: RichText(
                                    text: TextSpan(
                                      text: AppLocalizations.of(context)
                                              .minPercentage +
                                          ": ",
                                      style: AppStyles.body.copyWith(
                                          color: const Color(0xFFE1EFFF),
                                          fontSize: 14,
                                          fontWeight: FontWeight.w500,
                                          height: 1.57),
                                      children: <TextSpan>[
                                        TextSpan(
                                          text:
                                              widget.user.pass_percentage + "%",
                                          style: AppStyles.body.copyWith(
                                              color: const Color(0xFFE1EFFF),
                                              fontSize: 14,
                                              fontWeight: FontWeight.w500,
                                              height: 1.57),
                                        ),
                                      ],
                                    ),
                                  ))
                                ],
                              ),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    "  • ",
                                    style: AppStyles.body.copyWith(
                                        color: const Color(0xFFE1EFFF),
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500,
                                        height: 1.57),
                                  ),
                                  Flexible(
                                      child: Text(
                                    widget.user.question_selection.toString() ==
                                            "1"
                                        ? AppLocalizations.of(context)
                                            .randomRule
                                        : AppLocalizations.of(context)
                                            .questionrule,
                                    style: AppStyles.body.copyWith(
                                        color: const Color(0xFFE1EFFF),
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500,
                                        height: 1.57),
                                  ))
                                ],
                              )
                            ],
                          ),
                        ),
                        /* getDeviceType() == 'tablet'
                            ? Container(
                                width: MediaQuery.of(context).size.width * 0.18,
                                child: SvgPicture.asset(
                                    'assets/images/ExamDetail.svg',
                                    fit: BoxFit.fitWidth),
                              )
                            : Container(
                                width: MediaQuery.of(context).size.width * 0.4,
                                height: 150,
                                child: SvgPicture.asset(
                                    'assets/images/ExamDetail.svg',
                                    fit: BoxFit.contain),
                              ), */
                      ],
                    ),
                  ),
                  Positioned(
                      top: -20.0,
                      right: -20.0,
                      child: Container(
                        height: 160,
                        child: Image.asset('assets/images/Mask group.png',
                            fit: BoxFit.contain),
                      )),
                  Positioned(
                      top: 0,
                      right: 0,
                      child: getDeviceType() == 'tablet'
                          ? Container(
                              width: MediaQuery.of(context).size.width * 0.2,
                              height: 150,
                              child: SvgPicture.asset(
                                  'assets/images/ExamDetail.svg',
                                  fit: BoxFit.fitHeight),
                            )
                          : Container(
                              width: MediaQuery.of(context).size.height * 0.22,
                              height: 150,
                              child: SvgPicture.asset(
                                  'assets/images/ExamDetail.svg',
                                  fit: BoxFit.fitHeight))),
                ],
              ),
            ),
            Expanded(
              child: Container(
                alignment: Alignment.topLeft,
                padding: const EdgeInsets.all(4.0),
                margin: const EdgeInsets.only(left: 16, right: 16),
                decoration: BoxDecoration(
                    border: Border.all(color: const Color(0xffdddddd)),
                    color: const Color(0xfff9fafb),
                    borderRadius: BorderRadius.circular(8)),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(left: 8.0, top: 8),
                      child: Text(
                        AppLocalizations.of(context).intruction,
                        style: AppStyles.heading15.copyWith(
                            fontWeight: FontWeight.w800,
                            fontSize: 18,
                            color: const Color(0xff1F1F1F)),
                      ),
                    ),
                    Flexible(
                      child: Scrollbar(
                        // isAlwaysShown: true,
                        child: SingleChildScrollView(
                          scrollDirection: Axis.vertical,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Padding(
                                padding: const EdgeInsets.only(
                                    top: 0, bottom: 16, left: 0),
                                child: Html(
                                  data: des,
                                  style: {
                                    "body": Style(
                                        color: const Color(0xff1F1F1F),
                                        lineHeight: LineHeight.number(1.3)),
                                    'p': Style(
                                        fontFamily: 'Roboto',
                                        margin: Margins.zero,
                                        padding: HtmlPaddings.zero,
                                        fontSize: FontSize(14),
                                        color: const Color(0xff1F1F1F),
                                        fontWeight: FontWeight.w600)
                                  },
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Container(
              margin: const EdgeInsets.all(0.0),
              child: Row(
                children: [
                  /* Expanded(
                      child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      child: Text(
                        AppLocalizations.of(context).back,
                        style:
                            AppStyles.bodyBold.copyWith(color: AppColors.white),
                      ),
                      style: ElevatedButton.styleFrom(
                        primary: Colors.grey,
                      ),
                    ),
                  )), */
                  Expanded(
                      child: Container(
                    margin: const EdgeInsets.only(top: 12),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      boxShadow: [
                        BoxShadow(
                            color: const Color(0xff8a8a8a).withOpacity(0.25),
                            spreadRadius: 0,
                            blurRadius: 16,
                            offset: const Offset(4, -4)),
                      ],
                    ),
                    child: Padding(
                      padding: const EdgeInsets.only(
                          top: 12.0, bottom: 30, left: 16, right: 16),
                      child: ElevatedButton(
                        onPressed: () async {
                          bool connect = await InternetConnectionChecker().hasConnection;
                          if (connect) {
                            _showConfirmDialog();
                            logEvent("exam_detail_take_click",
                                {"test_name": widget.user.quiz_name});
                          } else {
                            showDialog(
                                context: context,
                                builder: (context) => const ErrorDialog());
                          }
                        },
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Text(
                            AppLocalizations.of(context).begin,
                            style: AppStyles.bodyBold
                                .copyWith(color: AppColors.white, fontSize: 16),
                          ),
                        ),
                        style: ElevatedButton.styleFrom(
                          minimumSize: const Size.fromHeight(40),
                          backgroundColor: AppColors.greenPrimary,
                        ),
                      ),
                    ),
                  ))
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
