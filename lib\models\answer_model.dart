import 'dart:convert';

class AnswerModel {
  final int question;
  final String value;

  AnswerModel({
    required this.question,
    required this.value,
  });

  Map<String, dynamic> toMap() {
    return {
      'question': question,
      'value': value,
    };
  }

  factory AnswerModel.fromMap(Map<int, dynamic> map) {
    return AnswerModel(
      question: map['question'],
      value: map['value'],
    );
  }

  String toJson() => json.encode(toMap());

  factory AnswerModel.fromJson(String source) =>
      AnswerModel.fromMap(json.decode(source));
}
