import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:psm_app/core/app_colors.dart';
import 'package:psm_app/core/app_styles.dart';
import 'package:psm_app/globals.dart';
import 'package:psm_app/localization.dart';
import 'package:psm_app/models/question_api_model.dart';
import 'package:psm_app/models/result_model.dart';
import 'package:psm_app/view/detail_result/detail_result.dart';
import 'package:psm_app/view/exam/exam.dart';
import 'package:psm_app/view/home.dart';
import 'package:psm_app/view/home/<USER>/chart/result_chart_new_widget.dart';
import 'package:psm_app/view/widgets/popup.dart';
import 'package:shared_preferences/shared_preferences.dart';

Future<int> getCountQuiz(String quizname, String time) async {
  final SharedPreferences prefs = await SharedPreferences.getInstance();
  // Fetch and decode data
  final String resultString = await prefs.getString('result') ?? "";
  final List<Result> musics = Result.decode(resultString);
  int count = 0;
  if (musics == "") {
    return count;
  } else {
    for (var i = 0; i < musics.length; i++) {
      if (musics[i].name == quizname) {
        count++;
      }
      if (musics[i].time == time) {
        break;
      }
    }
    return count;
  }
}

int test = 4;

class NewExamResult extends StatefulWidget {
  final Map<int, dynamic> answers;
  final Map<int, dynamic> answersText;
  final Map bookmark;
  final List<Question> questions;
  final String quizId;
  final String time;
  final String quizType;
  final String quizName;
  final String exam_quiz;
  double percent = 0;
  bool fromExam;
  late Map correctList = {};
  var correctQuestion;
  final int passPercent;
  final int quizDuration;
  final int timeDoQuiz; //seconds
  final bool shuffleOptions;
  NewExamResult({
    Key? key,
    required this.answers,
    required this.questions,
    this.quizType = "1",
    this.fromExam = false,
    required this.answersText,
    required this.quizId,
    required this.quizName,
    required this.exam_quiz,
    required this.time,
    required this.bookmark,
    required this.passPercent,
    required this.quizDuration,
    required this.timeDoQuiz,
    this.shuffleOptions = false,
  }) : super(key: key) {
    answers.forEach((index, value) {
      if (questions[index].type == "single") {
        correctQuestion = questions[index].correct;
        if (correctQuestion == value) {
          correctList[index] = true;
          correct++;
        } else {
          correctList[index] = false;
        }
      } else if (questions[index].type == "multi") {
        correctList[index] = true;
        int selectedCount = 0;
        if (shuffleOptions) {
          if (value is Map) {
            final arr = value;
            arr.forEach((key, value) {
              if (value) selectedCount++;
            });
          }
          var correctIds = questions[index].correct?.split(',');
          for (int i = 0; i < (correctIds?.length ?? 0); i++) {
            if (value[correctIds?[i]] == false ||
                questions[index].correctIndex?.length != selectedCount) {
              correctList[index] = false;
              continue;
            }
          }
        } else {
          for (var val in value) {
            if (val) selectedCount++;
          }
          questions[index].correctIndex!.forEach((element) {
            if (answers[index][element] == false ||
                questions[index].correctIndex?.length != selectedCount) {
              correctList[index] = false;
              return;
            }
          });
        }
        if (correctList[index] == true) correct++;
      }
    });
    percent = correct / questions.length;
  }

  int correct = 0;
  @override
  ExamRes createState() => ExamRes();
}

class ExamRes extends State<NewExamResult> {
  late List<int> arr;
  bool showPopup = false;
  String saveDate = '';
  formatedTime({required int timeInSecond}) {
    int sec = timeInSecond % 60;
    int min = (timeInSecond / 60).floor();
    String minute = min.toString().length <= 1 ? "$min" : "$min";
    String second = sec.toString().length <= 1 ? "$sec" : "$sec";
    if (second == "0") {
      return "$minute " + AppLocalizations.of(context).minutesShort;
    } else if (minute == "0") {
      return "$second " + AppLocalizations.of(context).secondShort;
    } else {
      return "$minute " +
          AppLocalizations.of(context).minutesShort +
          " $second " +
          AppLocalizations.of(context).secondShort;
    }
  }

  void getNumMarked() {
    int Marked = 0;
    int Correct = 0;
    int Wrong = 0;
    int UnAnswered = 0;
    int AllAns = widget.questions.length;

    for (var i = 0; i < widget.questions.length; i++) {
      if (widget.bookmark[i] == 1) {
        Marked++;
      }
      if (widget.correctList[i]) {
        Correct++;
      }
      if (widget.answersText[i] == '' ||
          widget.answersText[i].toString() == "[]") {
        UnAnswered++;
      }
    }
    Wrong = widget.questions.length - Correct;
    arr = [AllAns, Correct, Wrong, Marked, UnAnswered];
  }

  String device = 'phone';
  String getDeviceType() {
    final data = MediaQueryData.fromWindow(WidgetsBinding.instance!.window);
    return data.size.shortestSide < 550 ? 'phone' : 'tablet';
  }

  Future todayStatus() async {
    if (!Common.premium) {
      if (widget.percent < widget.passPercent / 100) {
        SharedPreferences preferences = await SharedPreferences.getInstance();
        saveDate = preferences.getString('savedDate') ?? '';
        var now = DateTime.now();
        var formatter = DateFormat('dd-MM-yyyy');
        String formattedDate = formatter.format(now);
        if (formattedDate != saveDate && widget.fromExam == true) {
          showPopup = true;
          preferences.setString('savedDate', formattedDate);
        }
      }
    }
  }

  @override
  void initState() {
    super.initState();
    getNumMarked();
    logPage("Exam Result");
    device = getDeviceType();
    todayStatus();
  }

  Future<bool> _onRetryPressed() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    final resumeQuiz = preferences.getString('resumeQuiz') ?? '';
    return (await showDialog(
          context: context,
          builder: (BuildContext context) => Dialog(
            // contentPadding: EdgeInsets.fromLTRB(16, 30, 16, 24),
            insetPadding: const EdgeInsets.symmetric(horizontal: 16),
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.fromLTRB(16, 30, 16, 24),
                child: Column(
                  children: [
                    Container(
                      width: 196,
                      height: 139,
                      decoration: const BoxDecoration(
                          image: DecorationImage(
                        image: AssetImage("assets/images/info_dialog.png"),
                        fit: BoxFit.fill,
                      )),
                    ),
                    Text(
                      AppLocalizations.of(context).confirmRetry,
                      style: AppStyles.dialogText,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(top: 24),
                      child: Container(
                        width: MediaQuery.of(context).size.width,
                        child: Row(
                          children: [
                            Expanded(
                                child: Padding(
                              padding: const EdgeInsets.only(right: 6),
                              child: ElevatedButton(
                                onPressed: () {
                                  Navigator.of(context).pop(false);
                                },
                                child: Text(
                                  AppLocalizations.of(context).no,
                                  style: AppStyles.secondaryButton,
                                  textAlign: TextAlign.center,
                                ),
                                style: ElevatedButton.styleFrom(
                                  foregroundColor: AppColors.greenPrimary,
                                  backgroundColor: Colors.white,
                                  shadowColor: Colors.white,
                                  elevation: 0,
                                  minimumSize: const Size(20, 44),
                                  side: BorderSide(
                                      color: AppColors.greenPrimary,
                                      width: 1.0,
                                      style: BorderStyle.solid),
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(4.0)),
                                ),
                              ),
                            )),
                            Expanded(
                                child: Padding(
                              padding: const EdgeInsets.only(left: 6),
                              child: ElevatedButton(
                                onPressed: () {
                                  Navigator.of(context).pop(false);
                                  Navigator.pushReplacement(
                                      context,
                                      MaterialPageRoute(
                                          builder: (context) => Exam(
                                                idQuiz: widget.quizId,
                                                exam_quiz: widget.exam_quiz,
                                                questionSelection:
                                                    widget.quizType,
                                                quizName: widget.quizName,
                                                passPercent: widget.passPercent,
                                                duration: widget.quizDuration,
                                                resume:
                                                    resumeQuiz == widget.quizId
                                                        ? true
                                                        : false,
                                              )));
                                },
                                child: Text(
                                  "OK",
                                  style: AppStyles.primaryButton,
                                ),
                                style: ElevatedButton.styleFrom(
                                  foregroundColor: AppColors.white,
                                  backgroundColor: AppColors.greenPrimary,
                                  shadowColor:
                                      const Color.fromARGB(92, 0, 166, 144),
                                  elevation: 0,
                                  minimumSize: const Size(20, 44),
                                  side: BorderSide(
                                      color: AppColors.greenPrimary,
                                      width: 1.0,
                                      style: BorderStyle.solid),
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(4.0)),
                                ),
                              ),
                            ))
                          ],
                        ),
                      ),
                    )
                  ],
                ),
              ),
            ),
          ),
        )) ??
        false;
  }

  void _moveToScreen2(BuildContext context) => Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => Home()),
      ).then((_) => setState(() {}));
  @override
  Widget build(BuildContext context) {
    int count = 0;
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarBrightness: Brightness.dark,
          statusBarIconBrightness: Brightness.light),
      child: FutureBuilder<int>(
          future: getCountQuiz(widget.quizName, widget.time),
          builder: (BuildContext context, AsyncSnapshot<int> snapshot) {
            if (snapshot.hasError) {
              count = 0;
            } else {
              count = snapshot.data ?? 0;
            }
            return Scaffold(
              backgroundColor: Colors.white,
              appBar: AppBar(
                title: Text(AppLocalizations.of(context).result,
                    style: AppStyles.appBarTitle),
                centerTitle: true,
                backgroundColor: Colors.white,
                elevation: 0,
                leading: IconButton(
                  icon: const Icon(Icons.arrow_back_ios_new),
                  iconSize: 20.0,
                  color: AppColors.blackText,
                  onPressed: () {
                    if (widget.exam_quiz == "1") {
                      Navigator.pushReplacement(context,
                          MaterialPageRoute(builder: (BuildContext context) {
                        return Home();
                      }));
                    } else {
                      Navigator.pop(context);
                    }
                  },
                ),
              ),
              body: WillPopScope(
                onWillPop: () async {
                  _moveToScreen2(
                    context,
                  );
                  return false;
                },
                child: Stack(
                  alignment: Alignment.bottomCenter,
                  children: [
                    Column(
                      children: [
                        Container(
                          color: Colors.white,
                          height: device == 'phone'
                              ? MediaQuery.of(context).size.height * 0.3682
                              : MediaQuery.of(context).size.height * 0.4,
                          child: Stack(
                            clipBehavior: Clip.none,
                            alignment: Alignment.center,
                            fit: StackFit.loose,
                            children: [
                              Positioned(
                                top: -10,
                                child:
                                    widget.percent >= widget.passPercent / 100
                                        ? SvgPicture.asset(
                                            'assets/images/Pass.svg',
                                            fit: BoxFit.fitHeight,
                                            height: device != "phone"
                                                ? MediaQuery.of(context)
                                                        .size
                                                        .height *
                                                    0.4
                                                : MediaQuery.of(context)
                                                        .size
                                                        .height *
                                                    0.3682,
                                          )
                                        : SvgPicture.asset(
                                            'assets/images/Fail.svg',
                                            fit: BoxFit.fitHeight,
                                            height: device != "phone"
                                                ? MediaQuery.of(context)
                                                        .size
                                                        .height *
                                                    0.4
                                                : MediaQuery.of(context)
                                                        .size
                                                        .height *
                                                    0.3682,
                                          ),
                              )
                            ],
                          ),
                        ),
                        Expanded(
                          child: SingleChildScrollView(
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Container(
                                width: device == "phone"
                                    ? MediaQuery.of(context).size.width
                                    : MediaQuery.of(context).size.height * 0.5,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(16),
                                  color: Colors.white,
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withOpacity(0.11),
                                      spreadRadius: 0,
                                      blurRadius: 16,
                                      offset: const Offset(4, 4),
                                    ),
                                  ],
                                ),
                                child: Column(children: [
                                  _result(context),
                                  const Padding(
                                    padding:
                                        EdgeInsets.only(top: 12, bottom: 8),
                                    child: Divider(
                                      thickness: 1,
                                      indent: 16,
                                      endIndent: 16,
                                      color: Color(0xffD9E2E8),
                                      height: 1,
                                    ),
                                  ),
                                  _timeAndDate(context),
                                  const Divider(
                                    thickness: 1,
                                    indent: 16,
                                    endIndent: 16,
                                    color: Color(0xffD9E2E8),
                                    height: 20,
                                  ),
                                  _chartAndStatistic(context),
                                  Padding(
                                    padding: const EdgeInsets.only(
                                        left: 16, bottom: 16, top: 5),
                                    child: Align(
                                      alignment: Alignment.centerLeft,
                                      child: Text(
                                        '*(${AppLocalizations.of(context).passRequirement.replaceAll("{e}", widget.passPercent.toString())})',
                                        style: AppStyles.textWeight500.copyWith(
                                            fontStyle: FontStyle.italic,
                                            color: const Color(0xff7A8694),
                                            fontSize: 12),
                                      ),
                                    ),
                                  )
                                ]),
                              ),
                            ),
                          ),
                        ),
                        _bottomButton(context),
                      ],
                    ),
                    showPopup == false
                        ? Container()
                        : Positioned(
                            bottom: 100,
                            child: PremiumPopup(
                              quiz_name: widget.quizName,
                              quiz_result: widget.percent >= widget.passPercent
                                  ? "Pass"
                                  : "Fail",
                            )),
                  ],
                ),
              ),
            );
          }),
    );
  }

  Widget _result(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 16, top: 16, bottom: 0, right: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          RichText(
            text: TextSpan(
                style: AppStyles.bodyBold.copyWith(
                    fontSize: 20, height: 1.5, color: AppColors.blackText),
                children: [
                  TextSpan(
                      text: AppLocalizations.of(context).resultText + ": "),
                  widget.percent >= widget.passPercent / 100
                      ? TextSpan(
                          text: "Pass",
                          style: TextStyle(color: AppColors.greenText))
                      : const TextSpan(
                          text: "Fail",
                          style: TextStyle(color: Color(0xFFFF040F)))
                ]),
          ),
          RichText(
              text: TextSpan(children: [
            TextSpan(
                text: (widget.percent * 100).round().toString() + "% ",
                style: AppStyles.bodyBold
                    .copyWith(color: const Color(0xff00326C), fontSize: 20)),
            TextSpan(
                text: AppLocalizations.of(context).correct,
                style: AppStyles.body
                    .copyWith(color: const Color(0xff7A8694), fontSize: 14))
          ]))
        ],
      ),
    );
  }

  Widget _timeAndDate(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 20.0, right: 20, bottom: 0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  AppLocalizations.of(context).time,
                  style: AppStyles.body.copyWith(
                      color: const Color(0xff7A8694),
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      height: 1.5),
                ),
                (widget.timeDoQuiz / 60).round() != 0
                    ? Container(
                        constraints: BoxConstraints(
                            maxWidth: MediaQuery.of(context).size.width * 0.5),
                        child: Text(
                            formatedTime(timeInSecond: widget.timeDoQuiz),
                            style: AppStyles.bodyBold.copyWith(
                                color: const Color(0xff00326C),
                                fontSize: 16,
                                height: 1.5,
                                fontWeight: FontWeight.w600)),
                      )
                    : Text(
                        (widget.timeDoQuiz).toString() +
                            " " +
                            AppLocalizations.of(context).secondShort,
                        style: AppStyles.bodyBold.copyWith(
                            color: const Color(0xff00326C),
                            fontSize: 16,
                            height: 1.5,
                            fontWeight: FontWeight.w600))
              ],
            ),
          ),
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 15),
            child: SizedBox(
                height: 13,
                child: VerticalDivider(
                    thickness: 1, width: 1, color: Color(0xffD9E2E8))),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  AppLocalizations.of(context).finishDate,
                  style: AppStyles.body.copyWith(
                      color: const Color(0xff7A8694),
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      height: 1.5),
                ),
                Text(
                    DateFormat('dd/MM/yy')
                        .format(DateFormat("dd/MM/yyyy").parse(widget.time)),
                    style: AppStyles.bodyBold.copyWith(
                        color: const Color(0xff00326C),
                        fontSize: 16,
                        height: 1.5,
                        fontWeight: FontWeight.w600))
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _chartAndStatistic(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          margin:
              EdgeInsets.only(right: MediaQuery.of(context).size.width * 0.05),
          child: Padding(
            padding: const EdgeInsets.only(left: 16),
            child: PieChart(
              right: arr[1],
              wrong: arr[2] - arr[4],
              unans: arr[4],
            ),
          ),
        ),
        Expanded(
          child: Padding(
            padding: const EdgeInsets.only(top: 8.0, bottom: 8, right: 20),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "${widget.questions.length.toString()} ${AppLocalizations.of(context).numberQuestion}",
                  style: AppStyles.bodyBold.copyWith(
                      color: AppColors.blackText,
                      fontWeight: FontWeight.w600,
                      height: 1.5,
                      fontSize: 16),
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 6),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(right: 6),
                            child: Container(
                              width: 14,
                              height: 14,
                              decoration: const BoxDecoration(
                                  color: Color(0xff00B9B9),
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(2))),
                            ),
                          ),
                          Text(
                            AppLocalizations.of(context).correct +
                                ": " +
                                arr[1].toString(),
                            style: AppStyles.textWeight500.copyWith(
                                color: AppColors.blackText,
                                height: 1.57,
                                fontSize: 14),
                          ),
                        ],
                      )
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 6),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(right: 6),
                            child: Container(
                              decoration: const BoxDecoration(
                                  color: Color(0xffF9705D),
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(2))),
                              width: 14,
                              height: 14,
                            ),
                          ),
                          Text(
                            AppLocalizations.of(context).wrong +
                                ": " +
                                (arr[2] - arr[4]).toString(),
                            style: AppStyles.textWeight500.copyWith(
                                color: AppColors.blackText,
                                height: 1.57,
                                fontSize: 14),
                          ),
                        ],
                      )
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 6),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(right: 6),
                            child: Container(
                              width: 14,
                              height: 14,
                              decoration: const BoxDecoration(
                                  color: Color(0xffB8CCD2),
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(2))),
                            ),
                          ),
                          Text(
                            AppLocalizations.of(context).unanswered +
                                ": " +
                                arr[4].toString(),
                            style: AppStyles.textWeight500.copyWith(
                                color: AppColors.blackText,
                                height: 1.57,
                                fontSize: 14),
                          ),
                        ],
                      ),
                    ],
                  ),
                )
              ],
            ),
          ),
        )
      ],
    );
  }

  Widget _bottomButton(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF8A8A8A).withOpacity(0.25),
            spreadRadius: 0,
            blurRadius: 16,
            offset: const Offset(4, -4),
          ),
        ],
      ),
      child: Padding(
        padding:
            const EdgeInsets.only(right: 16.0, left: 16, top: 12, bottom: 35),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
                child: Padding(
              padding: const EdgeInsets.only(right: 6),
              child: ElevatedButton(
                onPressed: () {
                  logEvent("exam_result_detail_click", {
                    "test_name": widget.quizName,
                    "test_result":
                        widget.percent >= widget.passPercent ? "Pass" : "Fail"
                  });
                  Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => DetailResult(
                          answers: widget.answers,
                          questions: widget.questions,
                          answersText: widget.answersText,
                          exam_quiz: widget.exam_quiz,
                          quizId: widget.quizId,
                          quizName: widget.quizName,
                          quizType: widget.quizType,
                          passPercent: widget.passPercent,
                          bookmark: widget.bookmark,
                          time: widget.time,
                          quizDuration: widget.quizDuration,
                          timeDoQuiz: widget.timeDoQuiz,
                          shuffleOptions: widget.shuffleOptions,
                        ),
                      ));
                },
                child: Text(
                  AppLocalizations.of(context).resultDetail,
                  style: AppStyles.secondaryButton,
                  textAlign: TextAlign.center,
                ),
                style: ElevatedButton.styleFrom(
                  foregroundColor: AppColors.greenPrimary,
                  backgroundColor: Colors.white,
                  shadowColor: Colors.white,
                  elevation: 0,
                  minimumSize: const Size(20, 48),
                  side: BorderSide(
                      color: AppColors.greenPrimary,
                      width: 1.0,
                      style: BorderStyle.solid),
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(4.0)),
                ),
              ),
            )),
            Expanded(
                child: Padding(
              padding: const EdgeInsets.only(
                left: 6,
              ),
              child: ElevatedButton(
                onPressed: () {
                  logEvent("exam_result_detail_retry_click", {
                    "test_name": widget.quizName,
                    "test_result":
                        widget.percent >= widget.passPercent ? "Pass" : "Fail"
                  });
                  /* Navigator.pushReplacement(
                                          context,
                                          MaterialPageRoute(
                                              builder: (context) => Home())); */
                  _onRetryPressed();
                },
                style: ElevatedButton.styleFrom(
                  foregroundColor: AppColors.white,
                  backgroundColor: AppColors.greenPrimary,
                  shadowColor: const Color.fromARGB(92, 0, 166, 144),
                  elevation: 0,
                  minimumSize: const Size(20, 48),
                  side: BorderSide(
                      color: AppColors.greenPrimary,
                      width: 1.0,
                      style: BorderStyle.solid),
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(4.0)),
                ),
                child: Text(
                  AppLocalizations.of(context).retry,
                  style: AppStyles.primaryButton,
                  textAlign: TextAlign.center,
                ),
              ),
            )),
          ],
        ),
      ),
    );
  }
}
