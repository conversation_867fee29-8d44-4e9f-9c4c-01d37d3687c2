import 'package:psm_app/core/app_colors.dart';
import 'package:psm_app/core/app_gradients.dart';
import 'package:psm_app/core/app_text_styles.dart';
import 'package:psm_app/localization.dart';
//
import 'package:psm_app/view/home/<USER>/score_card/score_card_widget.dart';
/* import 'package:dev_quiz/shared/models/user_model.dart'; */
import 'package:flutter/material.dart';
import 'package:psm_app/view/user_info.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:psm_app/view/user_info.dart';

import '../../../../core/core.dart';
import '../home/<USER>/score_card/score_card_result_widget.dart';

class ResultAppBarWidget extends PreferredSize {
  final double percent;
  final int correct;
  final int incorrect;
  final int passPercent;
  final int num;
  final String time;
  final int timeDoQuiz;
  final context;
  ResultAppBarWidget(
      {Key? key,
      required this.percent,
      required this.correct,
      required this.incorrect,
      required this.num,
      required this.time,
      required this.passPercent,
      required this.timeDoQuiz,
      required this.context})
      : super(
          preferredSize: Size.fromHeight(220),
          child: Container(
            decoration: BoxDecoration(
              image: DecorationImage(
                image: AssetImage("assets/images/appbar_bg1.png"),
                fit: BoxFit.fill,
              ),
            ),
            height: 220,
            child: Stack(
              children: [
                Container(
                  height: 140,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  width: double.maxFinite,
                  // decoration: BoxDecoration(
                  //   gradient: AppGradients.linear,
                  //   border: Border.fromBorderSide(
                  //       BorderSide(color: AppColors.border)),
                  // ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                          child: Align(
                        alignment: Alignment.centerLeft,
                        child: InkWell(
                          splashColor: Colors.transparent,
                          highlightColor: Colors.transparent,
                          onTap: () {
                            Navigator.pop(context);
                          },
                          child: Icon(
                            Icons.arrow_back_ios_new,
                            size: 20,
                            color: Colors.white,
                          ),
                        ),
                      )),
                      Expanded(
                          child: Align(
                        alignment: Alignment.center,
                        child: Text(
                          AppLocalizations.of(context).result,
                          style: AppStyles.appBarTitle
                              .copyWith(color: Colors.white),
                        ),
                      )),
                      Expanded(
                        child: Container(),
                      )
                    ],
                  ),
                ),
                Align(
                  alignment: Alignment(0.0, 1.0),
                  child: ScoreCardResultWidget(
                      correct: correct,
                      incorrect: incorrect,
                      percent: percent,
                      passPercent: passPercent,
                      num: num,
                      time: time,
                      timeDoQuiz: timeDoQuiz),
                )
              ],
            ),
          ),
        );
}
