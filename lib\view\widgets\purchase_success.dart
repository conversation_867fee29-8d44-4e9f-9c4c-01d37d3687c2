import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:psm_app/core/core.dart';
import 'package:psm_app/globals.dart';
import 'package:psm_app/localization.dart';
import 'package:psm_app/view/home.dart';

class PurchaseSuccessDialog extends StatelessWidget {
  final String text;
  const PurchaseSuccessDialog({Key? key, required this.text}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        logPage("Main");
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => Home()),
        );
      },
      child: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        color: Colors.transparent,
        child: Dialog(
          insetPadding: EdgeInsets.symmetric(horizontal: 16),
          child: SingleChildScrollView(
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.all(16),
              child: Stack(
                children: [
                  Positioned(
                      top: 0,
                      right: 0,
                      child: Icon(
                        Icons.close_outlined,
                        size: 30,
                      )),
                  Column(children: [
                    Padding(
                      padding: const EdgeInsets.only(top: 0),
                      child: SvgPicture.asset(
                        'assets/images/purchaseSuccess.svg',
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: Container(
                        margin: EdgeInsets.only(top: 10),
                        child: Column(
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(bottom: 10.0),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    "Congratulation!",
                                    textAlign: TextAlign.center,
                                    style: AppStyles.titleBold.copyWith(
                                        color: Color(0xff00326C), fontSize: 20),
                                  ),
                                  SizedBox(
                                    width: 5,
                                  ),
                                  SvgPicture.asset("assets/images/Congrat.svg")
                                ],
                              ),
                            ),
                            Text(
                              text,
                              textAlign: TextAlign.center,
                              style: AppStyles.dialogText,
                            ),
                          ],
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(
                          top: 8, left: 0, right: 0, bottom: 8),
                      child: Container(
                        width: MediaQuery.of(context).size.width,
                        child: Row(
                          children: [
                            Expanded(
                                child: Padding(
                              padding: const EdgeInsets.only(right: 0),
                              child: ElevatedButton(
                                onPressed: () {
                                  logPage("Main");
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                        builder: (context) => Home()),
                                  );
                                },
                                child: Text(
                                  "OK",
                                  style: AppStyles.primaryButton,
                                ),
                                style: ElevatedButton.styleFrom(
                                  foregroundColor: AppColors.white,
                                  backgroundColor: AppColors.greenPrimary,
                                  shadowColor: Color.fromARGB(92, 0, 166, 144),
                                  elevation: 0,
                                  minimumSize: Size(20, 44),
                                  side: BorderSide(
                                      color: AppColors.greenPrimary,
                                      width: 1.0,
                                      style: BorderStyle.solid),
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(4.0)),
                                ),
                              ),
                            )),
                          ],
                        ),
                      ),
                    )
                  ]),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
