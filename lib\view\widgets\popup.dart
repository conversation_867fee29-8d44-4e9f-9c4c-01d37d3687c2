import 'package:flutter/material.dart';
import 'package:psm_app/core/app_colors.dart';
import 'package:psm_app/core/app_styles.dart';
import 'package:psm_app/globals.dart';
import 'package:psm_app/localization.dart';
import 'package:psm_app/view/payment/payment.dart';

class PremiumPopup extends StatefulWidget {
  final String quiz_name;
  final String quiz_result;
  PremiumPopup({Key? key, required this.quiz_name, required this.quiz_result})
      : super(key: key);
  @override
  State<StatefulWidget> createState() {
    return _MyAppState();
  }
}

String device = 'phone';
String getDeviceType() {
  final data = MediaQueryData.fromWindow(WidgetsBinding.instance!.window);
  return data.size.shortestSide < 550 ? 'phone' : 'tablet';
}

class _MyAppState extends State<PremiumPopup> {
  bool _ignoring = false;
  @override
  void initState() {
    device = getDeviceType();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(0 /* left: 20.0, right: 20, bottom: 20 */),
      child: IgnorePointer(
        ignoring: _ignoring,
        child: AnimatedOpacity(
          opacity: _ignoring ? 0.0 : 1.0,
          duration: const Duration(milliseconds: 500),
          child: GestureDetector(
            onTap: () {
              logEvent("exam_result_try_now_click", {
                "test_name": widget.quiz_name,
                "test_result": widget.quiz_result
              });
              Navigator.push(context,
                  MaterialPageRoute(builder: (context) => PaymentPage()));
            },
            child: Container(
              //height: 196.0,
              width: device == 'phone'
                  ? MediaQuery.of(context).size.width * 0.90
                  : 600,
              decoration: BoxDecoration(
                color: AppColors.popUpBannerBG,
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(10),
                    topRight: Radius.circular(10),
                    bottomLeft: Radius.circular(10),
                    bottomRight: Radius.circular(10)),
                boxShadow: [
                  BoxShadow(
                    color: Color.fromRGBO(138, 138, 138, 0.25),
                    spreadRadius: 5,
                    blurRadius: 7,
                    offset: Offset(4, -4), // changes position of shadow
                  ),
                ],
              ),
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 12.0),
                child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(top: 11.0, bottom: 11),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              AppLocalizations.of(context).premiumVersion,
                              style: AppStyles.bodyBold
                                  .copyWith(color: Colors.white, fontSize: 16),
                            ),
                            GestureDetector(
                              onTap: () {
                                setState(() {
                                  _ignoring = true;
                                });
                              },
                              child: Icon(
                                Icons.close_outlined,
                                color: Color(0xFF7C9ABC),
                              ),
                            )
                          ],
                        ),
                      ),
                      new Divider(
                        color: Color(0xff7C9ABC),
                        height: 1,
                      ),
                      Padding(
                        padding: const EdgeInsets.only(top: 12.0, bottom: 12),
                        child: Text(
                          AppLocalizations.of(context).trialTitle,
                          style: AppStyles.bodyBold
                              .copyWith(color: Colors.white, fontSize: 14),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(top: 0.0),
                        child: Row(
                          children: [
                            Icon(
                              Icons.lens_rounded,
                              color: Color(0xffFEA319),
                              size: 8,
                            ),
                            SizedBox(
                              width: 10,
                            ),
                            Text(
                              AppLocalizations.of(context).unlockQR,
                              style: AppStyles.body.copyWith(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(top: 14.0),
                        child: Row(
                          children: [
                            Icon(
                              Icons.lens_rounded,
                              color: Color(0xffFEA319),
                              size: 8,
                            ),
                            SizedBox(
                              width: 10,
                            ),
                            Text(
                              AppLocalizations.of(context).unlockAllExam,
                              style: AppStyles.body.copyWith(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(top: 14.0),
                        child: Row(
                          children: [
                            Icon(
                              Icons.lens_rounded,
                              color: Color(0xffFEA319),
                              size: 8,
                            ),
                            SizedBox(
                              width: 10,
                            ),
                            Text(
                              AppLocalizations.of(context).withExplain,
                              style: AppStyles.body.copyWith(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(top: 14.0),
                        child: Row(
                          children: [
                            Icon(
                              Icons.lens_rounded,
                              color: Color(0xffFEA319),
                              size: 8,
                            ),
                            SizedBox(
                              width: 10,
                            ),
                            Text(
                              AppLocalizations.of(context)
                                  .fullyIUpdated
                                  .replaceAll(
                                      '%1', DateTime.now().year.toString()),
                              style: AppStyles.body.copyWith(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 12.0),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Text(
                              AppLocalizations.of(context).upgrade,
                              style: AppStyles.body.copyWith(
                                  color: Color(0xff00F9D7),
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold),
                            ),
                            SizedBox(
                              width: 6,
                            ),
                            Padding(
                              padding: const EdgeInsets.only(top: 2),
                              child: Icon(
                                Icons.arrow_forward_rounded,
                                color: Color(0xff00F9D7),
                                size: 20,
                              ),
                            )
                          ],
                        ),
                      )
                    ]),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
