import 'dart:convert';

class Other {
  Other({
    required this.appid,
    required this.app_icon,
    required this.drive_icon,
    required this.appname,
    required this.iosID,
    required this.androidID,
  });

  String appid;
  String app_icon;
  String drive_icon;
  String appname;
  String iosID;
  String androidID;

  @override
  String toString() {
    return "Other(appid: $appid, appname: $appname, app_icon: $app_icon, iosID: $iosID, drive_icon: $drive_icon, androidID: $androidID";
  }

  factory Other.fromJson(Map<String, dynamic> jsonData) {
    return Other(
      appid: jsonData['appid'],
      app_icon: jsonData['app_icon'],
      appname: jsonData['appname'],
      iosID: jsonData['iosID'] ?? "",
      androidID: jsonData['androidID'] ?? "",
      drive_icon: jsonData['drive_icon'] ?? "",
    );
  }

  static Map<String, dynamic> toMap(Other music) => {
        'appid': music.appid,
        'app_icon': music.app_icon,
        'appname': music.appname,
        'iosID': music.iosID,
        'androidID': music.androidID,
        'drive_icon': music.drive_icon
      };

  static String encode(List<Other> musics) => json.encode(
        musics
            .map<Map<String, dynamic>>((music) => Other.toMap(music))
            .toList(),
      );

  static List<Other> decode(String musics) =>
      (json.decode(musics) as List<dynamic>)
          .map<Other>((item) => Other.fromJson(item))
          .toList();
}
