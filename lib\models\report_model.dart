import 'package:intl/intl.dart';
import 'package:psm_app/view/question_review.dart';

class Report {
  String? reportId;
  String? reportQuestion;
  String? reportDetail;
  String? status;
  String? createdAt;

  Report({this.reportId, this.reportQuestion, this.status, this.reportDetail});

  ReportStatus get reportStatus {
    switch (status) {
      case "0":
        return ReportStatus.reviewing;
      case "1":
        return ReportStatus.answered;
      case "2":
        return ReportStatus.closed;
      default:
        return ReportStatus.reviewing;
    }
  }

  set reportStatus(ReportStatus status) {
    switch (status) {
      case ReportStatus.reviewing:
        this.status = "0";
        break;
      case ReportStatus.answered:
        this.status = "1";
        break;
      case ReportStatus.closed:
        this.status = "2";
        break;
      default:
        this.status = "0";
    }
  }

  String dateFormat(json) {
    final date = json["created_date"];
    try {
      final parse = DateTime.parse(json["created_date"]);
      return DateFormat('dd/MM/yyyy').format(parse);
    } catch (e) {
      print('------> Log catch: $e');
      return date;
    }
  }

  Report.fromJson(Map<String, dynamic> json) {
    reportId = json["report_id"];
    reportQuestion = json["report_question"];
    status = json["status"];
    reportDetail = json["report_detail"];
    createdAt = dateFormat(json);
  }

  static List<Report> fromList(List<Map<String, dynamic>> list) {
    return list.map((map) => Report.fromJson(map)).toList();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> _data = <String, dynamic>{};
    _data["report_id"] = reportId;
    _data["report_question"] = reportQuestion;
    _data["status"] = status;
    _data["report_detail"] = reportDetail;
    _data["created_date"] = createdAt;
    return _data;
  }
}

class ReportResponse {
  String? response;
  String? time;
  bool? isAdmin;

  ReportResponse({this.response, this.time, this.isAdmin});

  ReportResponse.fromJson(Map<String, dynamic> json) {
    response = json["response"];
    time = DateFormat('dd/MM/yyyy').format(DateTime.parse(json["time"]));
    isAdmin = json["is_admin"] == "1" ? true : false;
  }

  static List<ReportResponse> fromList(List<Map<String, dynamic>> list) {
    return list.map((map) => ReportResponse.fromJson(map)).toList();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> _data = <String, dynamic>{};
    _data["response"] = response;
    _data["time"] = time;
    _data["is_admin"] = isAdmin;
    return _data;
  }
}
