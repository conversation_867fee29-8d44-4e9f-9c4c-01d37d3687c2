import 'package:psm_app/core/app_colors.dart';
import 'package:psm_app/core/core.dart';
import 'package:flutter/material.dart';

class ResultChartWidget extends StatefulWidget {
  final double percent;
  const ResultChartWidget({Key? key, required this.percent}) : super(key: key);

  @override
  _ChartWidgetState createState() => _ChartWidgetState();
}

class _ChartWidgetState extends State<ResultChartWidget>
    with SingleTickerProviderStateMixin {
  //animação
  late AnimationController _controller;
  late Animation<double> _animation;
  @override
  dispose() {
    _controller.dispose(); // you need this
    super.dispose();
  }

  void _initAnimation() {
    _controller = AnimationController(
        vsync: this,
        duration: Duration(seconds: widget.percent >= 0.5 ? 2 : 1));
    _animation =
        Tween<double>(begin: 0.0, end: widget.percent).animate(_controller);
    _controller.forward();
  }

  @override
  void initState() {
    _initAnimation();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        height: 70,
        width: 70,
        child: AnimatedBuilder(
          animation: _animation,
          builder: (context, _) => Stack(children: [
            Center(
              child: Container(
                height: 70,
                width: 70,
                child: CircularProgressIndicator(
                  strokeWidth: 30,
                  value: _animation.value,
                  backgroundColor: Colors.red,
                  valueColor:
                      AlwaysStoppedAnimation<Color>(AppColors.chartPrimary),
                ),
              ),
            ),
            /* Center(
              child: Text(
                "${(_animation.value * 100).toInt()}%",
                style: AppTextStyles.heading,
              ),
            ) */
          ]),
        ));
  }
}
