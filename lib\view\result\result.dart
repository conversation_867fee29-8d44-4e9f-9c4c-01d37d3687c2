// ignore_for_file: prefer_const_constructors, sized_box_for_whitespace, unnecessary_new, prefer_const_declarations, unused_local_variable
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:psm_app/ad_helper.dart';
import 'package:psm_app/core/core.dart';
import 'package:psm_app/globals.dart';
import 'package:psm_app/localization.dart';

import 'package:psm_app/models/qlist_model.dart';
import 'package:http/http.dart' as http;
import 'package:psm_app/models/question_api_model.dart';

import 'package:psm_app/models/result_model.dart';
import 'package:psm_app/view/exam/exam_result.dart';
import 'package:psm_app/view/exam/new_quiz_list.dart';
import 'package:psm_app/view/exam_detail.dart';
import 'package:psm_app/view/home.dart';
import 'package:psm_app/view/home/<USER>/chart/chart_widget.dart';
import 'package:psm_app/view/result/new_result.dart';
import 'package:psm_app/view/widgets/popup.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../home/<USER>/chart/result_list_chart_widget.dart';

Future<List<Result>> getList() async {
  final SharedPreferences prefs = await SharedPreferences.getInstance();
  final String resultString = await prefs.getString('result') ?? "";
  //print(resultString);
  if (resultString != "") {
    var a = Result.decode(resultString).reversed.toList();
    List<Result> b = [];
    for (var i = 0; i < a.length; i++) {
      if (a[i].environment == environment.text || a[i].environment == null) {
        b.add(a[i]);
      }
    }
    return b;
  } else {
    return <Result>[];
  }
}

class Widget_Result extends StatefulWidget {
  @override
  _MyAppState createState() => _MyAppState();
}

_goBack(BuildContext context) {
  logPage("Main");
  Navigator.pop(context);
}

class _MyAppState extends State<Widget_Result> {
  late Future<List<Result>> futurePost;

  late bool status;

  @override
  void initState() {
    super.initState();
    //Future<String?> token = attempPost();
    logPage("Exam Result");
    futurePost = getList();
  }

  formatedTime({required int timeInSecond}) {
    int sec = timeInSecond % 60;
    int min = (timeInSecond / 60).floor();
    String minute = min.toString().length <= 1 ? "$min" : "$min";
    String second = sec.toString().length <= 1 ? "$sec" : "$sec";
    if (second == "0")
      return "$minute " + AppLocalizations.of(context).minutesShort;
    else if (minute == "0")
      return "$second " + AppLocalizations.of(context).secondShort;
    else
      return "$minute " +
          AppLocalizations.of(context).minutesShort +
          " $second " +
          AppLocalizations.of(context).secondShort;
  }

  final ButtonStyle raisedButtonStyle = ElevatedButton.styleFrom(
    foregroundColor: Colors.black87,
    backgroundColor: Colors.lightBlueAccent,
    minimumSize: Size(88, 36),
    padding: EdgeInsets.symmetric(horizontal: 16),
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.all(Radius.circular(5)),
    ),
  );
  @override
  Widget build(BuildContext context) {
    var apbar_height = AppBar().preferredSize.height;
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios_new),
          iconSize: 20.0,
          color: AppColors.blackText,
          onPressed: () {
            _goBack(context);
          },
        ),
        title: Text(
          AppLocalizations.of(context).resultList,
          style: AppStyles.appBarTitle,
        ),
        backgroundColor: Colors.white,
        elevation: 0,
      ),
      body: Container(
        color: Colors.white,
        child: Stack(
          alignment: Alignment.bottomCenter,
          children: [
            FutureBuilder<List<Result>>(
              future: futurePost,
              builder: (context, snapshot) {
                if (snapshot.hasData) {
                  if (snapshot.data?.isEmpty ?? true) {
                    return Container(
                      margin: EdgeInsets.only(bottom: apbar_height),
                      child: Center(
                          child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Padding(
                            padding: const EdgeInsets.all(10),
                            child:
                                SvgPicture.asset('assets/images/no_item.svg'),
                          ),
                          Padding(
                            padding: const EdgeInsets.only(top: 20),
                            child: Column(
                              children: [
                                Text(AppLocalizations.of(context).noQuizResult,
                                    style: AppStyles.body.copyWith(
                                        color: Colors.black, fontSize: 16)),
                                SizedBox(
                                  height: 10,
                                ),
                                GestureDetector(
                                  child: Text(
                                      AppLocalizations.of(context).viewExamList,
                                      style: AppStyles.bodyBold.copyWith(
                                          fontSize: 16,
                                          color: AppColors.greenPrimary)),
                                  onTap: () {
                                    Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                            builder: (context) =>
                                                Widget_Qlist2()));
                                  },
                                )
                              ],
                            ),
                          ),
                        ],
                      )),
                    );
                  } else {
                    return ListView.builder(
                      itemCount: snapshot.data!.length,
                      padding: const EdgeInsets.only(top: 5.0),
                      itemBuilder: (BuildContext context, int index) {
                        return Container(
                          //height: 100,
                          margin: const EdgeInsets.only(
                              left: 16.0, right: 16.0, bottom: 16),
                          decoration: BoxDecoration(
                              boxShadow: [
                                BoxShadow(
                                  color: Color.fromRGBO(0, 0, 0, 0.16),
                                  spreadRadius: 0,
                                  blurRadius: 10,
                                  offset: Offset(
                                      4, 4), // changes position of shadow
                                ),
                              ],
                              color: AppColors.white,
                              borderRadius: BorderRadius.circular(8)),
                          child: InkWell(
                              onTap: () {
                                Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => NewExamResult(
                                        answers: Map<int, dynamic>.from(snapshot
                                            .data![index].answers
                                            .asMap()),
                                        questions:
                                            snapshot.data![index].questions,
                                        answersText: Map<int, dynamic>.from(
                                            snapshot.data![index].answersText
                                                .asMap()),
                                        exam_quiz: "0",
                                        quizType:
                                            snapshot.data![index].quizType ??
                                                "1",
                                        quizId: snapshot.data![index].quizId,
                                        quizName: snapshot.data![index].name,
                                        passPercent:
                                            snapshot.data![index].passPercent,
                                        bookmark: Map<int, dynamic>.from(
                                            snapshot.data![index].bookmark
                                                .asMap()),
                                        time: snapshot.data![index].time,
                                        quizDuration:
                                            snapshot.data![index].quizDuration,
                                        timeDoQuiz:
                                            snapshot.data![index].timeDoQuiz,
                                        shuffleOptions: snapshot
                                                .data![index].shuffleOptions ??
                                            false,
                                      ),
                                    ));
                              },
                              child: Padding(
                                padding: const EdgeInsets.all(12.0),
                                child: Column(
                                  children: [
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        Row(
                                          children: [
                                            snapshot.data![index].quizType ==
                                                    "0"
                                                ? SvgPicture.asset(
                                                    'assets/images/set.svg')
                                                : SvgPicture.asset(
                                                    'assets/images/shuffle.svg'),
                                            SizedBox(
                                              width: 8,
                                            ),
                                            snapshot.data![index].quizType ==
                                                    "0"
                                                ? Text(
                                                    AppLocalizations.of(context)
                                                        .questionSet
                                                        .toUpperCase(),
                                                    style: AppStyles.body
                                                        .copyWith(
                                                            color: Color(
                                                                0xff00326C),
                                                            fontWeight:
                                                                FontWeight.w600,
                                                            fontSize: 12),
                                                  )
                                                : Text(
                                                    AppLocalizations.of(context)
                                                        .randomSet
                                                        .toUpperCase(),
                                                    style: AppStyles.body
                                                        .copyWith(
                                                            color: Color(
                                                                0xff00326C),
                                                            fontWeight:
                                                                FontWeight.w600,
                                                            fontSize: 12),
                                                  ),
                                          ],
                                        ),
                                        Row(
                                          /* crossAxisAlignment:
                                                      CrossAxisAlignment.center, */
                                          children: [
                                            SizedBox(
                                              width: 8,
                                            ),
                                            Container(
                                              decoration: BoxDecoration(
                                                  color: snapshot.data![index]
                                                              .status ==
                                                          "Pass"
                                                      ? Color(0xffDCF0E3)
                                                      : Color(0xffFFEDED),
                                                  borderRadius:
                                                      BorderRadius.circular(4)),
                                              child: Padding(
                                                  padding: const EdgeInsets
                                                      .symmetric(
                                                      vertical: 4,
                                                      horizontal: 6),
                                                  child: Text(
                                                    snapshot
                                                        .data![index].status,
                                                    style: AppStyles.body.copyWith(
                                                        color: snapshot
                                                                    .data![
                                                                        index]
                                                                    .status ==
                                                                "Pass"
                                                            ? Color(0xff219653)
                                                            : Color(0xffAF3610),
                                                        fontWeight:
                                                            FontWeight.w500,
                                                        fontSize: 12,
                                                        height: 1.5),
                                                  )),
                                            ),
                                          ],
                                        )
                                      ],
                                    ),
                                    Divider(
                                      thickness: 1,
                                      color: Color(0xFFD9E2E8),
                                      height: 16,
                                    ),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        Expanded(
                                          child: Column(
                                            mainAxisAlignment:
                                                MainAxisAlignment.start,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Padding(
                                                padding: const EdgeInsets.only(
                                                    bottom: 10.0),
                                                child: Text(
                                                  snapshot.data![index].name,
                                                  style: AppStyles.body
                                                      .copyWith(
                                                          color:
                                                              Color(0xff1F1F1F),
                                                          fontWeight:
                                                              FontWeight.w700,
                                                          fontSize: 14,
                                                          height: 1.57),
                                                ),
                                              ),
                                              Container(
                                                width: MediaQuery.of(context)
                                                        .size
                                                        .width *
                                                    0.65,
                                                child: Wrap(
                                                  alignment:
                                                      WrapAlignment.start,
                                                  direction: Axis.horizontal,
                                                  children: [
                                                    Padding(
                                                      padding:
                                                          const EdgeInsets.only(
                                                              right: 5),
                                                      child: Icon(
                                                          Icons
                                                              .sticky_note_2_outlined,
                                                          color: AppColors
                                                              .lightGreyText,
                                                          size: 15),
                                                    ),
                                                    (snapshot.data![index]
                                                                        .timeDoQuiz /
                                                                    60)
                                                                .round() !=
                                                            0
                                                        ? Padding(
                                                            padding:
                                                                const EdgeInsets
                                                                    .only(
                                                                    right: 5),
                                                            child: Text(
                                                                /* (snapshot.data![index].timeDoQuiz /
                                                                            60)
                                                                        .round()
                                                                        .toString() +
                                                                    ' ' +
                                                                    AppLocalizations.of(
                                                                            context)
                                                                        .minutesShort */
                                                                formatedTime(
                                                                    timeInSecond: snapshot
                                                                        .data![
                                                                            index]
                                                                        .timeDoQuiz),
                                                                style: AppStyles
                                                                    .listQuizInfo),
                                                          )
                                                        : Padding(
                                                            padding:
                                                                const EdgeInsets
                                                                    .only(
                                                                    right: 5),
                                                            child: Text(
                                                                snapshot
                                                                        .data![
                                                                            index]
                                                                        .timeDoQuiz
                                                                        .toString() +
                                                                    ' ' +
                                                                    AppLocalizations.of(
                                                                            context)
                                                                        .secondShort,
                                                                style: AppStyles
                                                                    .listQuizInfo),
                                                          ),
                                                    Padding(
                                                      padding:
                                                          const EdgeInsets.only(
                                                              top: 5, right: 5),
                                                      child: Icon(
                                                        Icons.circle_rounded,
                                                        size: 5,
                                                        color:
                                                            Color(0xff7A8694),
                                                      ),
                                                    ),
                                                    Wrap(children: [
                                                      Padding(
                                                          padding:
                                                              const EdgeInsets
                                                                  .only(
                                                                  right: 5),
                                                          child: Icon(
                                                            Icons.access_time,
                                                            color: AppColors
                                                                .lightGreyText,
                                                            size: 15,
                                                          )),
                                                      Text(
                                                          snapshot.data![index]
                                                              .time,
                                                          style: AppStyles
                                                              .listQuizInfo)
                                                    ])
                                                  ],
                                                ),
                                              )
                                            ],
                                          ),
                                        ),
                                        ResultListChartWidget(
                                          percent: double.parse(snapshot
                                                      .data![index].percentage)
                                                  .round() /
                                              100,
                                          status: snapshot.data![index].status,
                                        ),
                                      ],
                                    )
                                  ],
                                ),
                              )),
                        );
                      },
                    );
                  }
                } else {
                  return Center(child: CircularProgressIndicator());
                }
              },
            ),
          ],
        ),
      ),
    );
  }
}
