import 'package:flutter/material.dart';
import 'package:psm_app/data_sources/purchase_api.dart';
import 'package:psm_app/globals.dart';
import 'package:purchases_flutter/purchases_flutter.dart';

class InAppPurchase extends StatefulWidget {
  const InAppPurchase({Key? key}) : super(key: key);

  @override
  State<InAppPurchase> createState() => _InAppPurchaseState();
}

class _InAppPurchaseState extends State<InAppPurchase> {
  late Future getOffers;
  bool premium = Common.premium;
  String premiumType = Common.premiumType;
  @override
  void initState() {
    getOffers = fetchOffer();
    super.initState();
  }

  @override
  Future fetchOffer() async {
    final offerings = await PurchaseApi.fetchOffer();
    if (offerings.isEmpty) {
      return null;
    } else {
      final packages = offerings
          .map((offer) => offer.availablePackages)
          .expand((pair) => pair)
          .toList();
      return packages;
    }
  }

  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(),
      body: FutureBuilder(
        future: getOffers,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.hasData) {
            return Column(
              children: [
                Expanded(
                  child: Column(
                    children: [
                      ElevatedButton(
                          onPressed: () async {
                            bool restore = await PurchaseApi.restorePurchase();
                            if (restore) {
                              showDialog<String>(
                                  context: context,
                                  builder: (BuildContext context) =>
                                      AlertDialog(
                                        content:
                                            Text('Restore premium success'),
                                      ));
                              setState(() {
                                premium = Common.premium;
                                premiumType = Common.premiumType;
                              });
                            }
                          },
                          child: Text("Restore Purchase")),
                      Text(premium ? "Premium User" : "Free User"),
                      Text(premiumType)
                    ],
                  ),
                ),
                Expanded(
                  child: ListView.builder(
                      itemCount: snapshot.data.length,
                      itemBuilder: (BuildContext context, int index) {
                        return Container(
                          child: InkWell(
                            onTap: () async {
                              bool purchase = await PurchaseApi.purchasePackage(
                                  snapshot.data[index]);
                              if (purchase) {
                                showDialog<String>(
                                    context: context,
                                    builder: (BuildContext context) =>
                                        AlertDialog(
                                          content: Text('Subcribe Success'),
                                        ));
                                setState(() {
                                  premium = Common.premium;
                                  premiumType = Common.premiumType;
                                });
                              }
                            },
                            child: Column(
                              children: [
                                Text(snapshot.data[index].product.title),
                                Text(snapshot.data[index].product.description),
                                Text(snapshot.data[index].product.priceString),
                              ],
                            ),
                          ),
                        );
                      }),
                ),
              ],
            );
          } else {
            return Container();
          }
        },
      ),
    );
  }
}
