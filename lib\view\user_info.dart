import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io' show Platform;
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:launch_review/launch_review.dart';
import 'package:notification_permissions/notification_permissions.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:provider/provider.dart';
import 'package:psm_app/core/core.dart';
import 'package:psm_app/globals.dart';
import 'package:psm_app/helper/helper.dart';
import 'package:psm_app/localization.dart';

import 'package:psm_app/models/language_model.dart';
import 'package:psm_app/models/other_app_model.dart';
import 'package:psm_app/models/qlist_model.dart';
import 'package:psm_app/noficationServices.dart';
import 'package:psm_app/provider/locale_provider.dart';
import 'package:psm_app/view/about_us/new_about_us.dart';
import 'package:psm_app/view/detail_result/detail_result.dart';
import 'package:psm_app/view/home.dart';
import 'package:psm_app/view/payment/payment.dart';
import 'package:psm_app/view/widgets/success_dialog.dart';
import 'package:share_plus/share_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:store_redirect/store_redirect.dart';
import 'package:url_launcher/url_launcher.dart';

import '../data_sources/api_servies.dart';

class UserInfo extends StatefulWidget {
  String userName;
  UserInfo({Key? key, required this.userName}) : super(key: key);
  @override
  State<UserInfo> createState() => _UserInfoState();
}

class _UserInfoState extends State<UserInfo> {
  late Future<List<Other>> futurePost;
  late Timer _timer;
  late final NameController = TextEditingController(
      text: isUserNameValidate == true ? widget.userName : userName);
  late final focusNode = FocusNode();
  TimeOfDay selectedTime = TimeOfDay.now();
  bool notificationStatus = false;
  LanguageModel? _chosenValue;
  bool isUserNameValidate = true;
  String version = '';
  String plan = '';
  List<DaysModel> dayOfWeek = [
    DaysModel(name: 'Monday', isSelected: false, id: DateTime.monday),
    DaysModel(name: 'Tuesday', isSelected: false, id: DateTime.tuesday),
    DaysModel(name: 'Wednesday', isSelected: false, id: DateTime.wednesday),
    DaysModel(name: 'Thursday', isSelected: false, id: DateTime.thursday),
    DaysModel(name: 'Friday', isSelected: false, id: DateTime.friday),
    DaysModel(name: 'Saturday', isSelected: false, id: DateTime.saturday),
    DaysModel(name: 'Sunday', isSelected: false, id: DateTime.sunday),
  ];
  List<DaysModel> selectedDays = [
    DaysModel(name: 'Monday', isSelected: true, id: DateTime.monday)
  ];
  List<LanguageModel> _languages = List.empty(growable: true);
  List<String> _fontSize = ['20', "21", "22", "23", "24"];
  String fontvalue = CommonFont.size;
  saveUserName() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    await preferences.setString('userName', userName);
  }

  saveFontSize() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    await preferences.setString('font', fontvalue);
    CommonFont.size = fontvalue;
  }

  saveLanguage(String lang) async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    await preferences.setString('lang', lang);
  }

  showLangName(String code) {
    for (var child in _languages) {
      if (child.code.toString() == code) {
        return child.name;
      }
    }
  }

  _goBack(BuildContext context) {
    logPage("Main");
    Navigator.pop(context);
  }

  getPlan() {
    if (Common.trial) {
      plan = "";
    } else if (Common.premiumType.contains('monthly')) {
      plan = " (1 ${AppLocalizations.of(context).month})";
    } else if (Common.premiumType.contains('quarterly')) {
      plan = " (3 ${AppLocalizations.of(context).months})";
    } else if (Common.premiumType.contains('yearly')) {
      plan = " (12 ${AppLocalizations.of(context).months})";
    } else if (Common.premiumType == '') {
      plan = "";
    }
  }

  late Future<String> permissionStatusFuture;

  var permGranted = "granted";
  var permDenied = "denied";
  var permUnknown = "unknown";
  var permProvisional = "provisional";

  Future<String> getCheckNotificationPermStatus() {
    return NotificationPermissions.getNotificationPermissionStatus()
        .then((status) {
      switch (status) {
        case PermissionStatus.denied:
          return permDenied;
        case PermissionStatus.granted:
          return permGranted;
        case PermissionStatus.unknown:
          return permUnknown;
        case PermissionStatus.provisional:
          return permProvisional;
        default:
          return "";
      }
    });
  }

  Future requestNotification() async {
    if (Common.platform == 'iOS') {
      final permissionStatus = await getCheckNotificationPermStatus();
      if (permissionStatus == "denied" || permissionStatus == "unknown") {
        NotificationPermissions.requestNotificationPermissions(
            iosSettings: const NotificationSettingsIos(
              alert: true,
              badge: true,
              sound: true,
            ),
            openSettings: false);
      }
      FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
          FlutterLocalNotificationsPlugin();
      final bool? result = await flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
              IOSFlutterLocalNotificationsPlugin>()
          ?.requestPermissions(
            alert: true,
            badge: true,
            sound: true,
          );
      setState(() {
        notificationStatus = result ?? false;
        saveNotificationStatus(notificationStatus);
      });
    } else {
      notificationStatus = !notificationStatus;
      saveNotificationStatus(notificationStatus);
    }
  }

  Future getDaysLocal() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    var days = await preferences.getString('dayOfWeek');
    if (days != "" && days != null) {
      List<DaysModel> arr = DaysModel.decode(jsonDecode(days));
      selectedDays = arr;
      //inspect(dayOfWeek);
      for (var i = 0; i < dayOfWeek.length; i++) {
        for (var j = 0; j < selectedDays.length; j++) {
          if (selectedDays[j].id == dayOfWeek[i].id) {
            dayOfWeek[i].isSelected = true;
            break;
          }
        }
      }
    } else {
      selectedDays = [
        DaysModel(name: 'Monday', isSelected: true, id: DateTime.monday)
      ];
      dayOfWeek[0].isSelected = true;
    }
    var time = preferences.getString('notificationTime');
    if (time != "" && time != null) {
      var savedTime = jsonDecode(time);
      setState(() {
        selectedTime = TimeOfDay(hour: savedTime[0], minute: savedTime[1]);
      });
    }
    if (notificationStatus) {
      sendNotificationRequest();
    }
  }

  Future saveNotificationStatus(bool status) async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    await preferences.setString('notificationStatus', status.toString());
  }

  Future getNotificationStatus() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    var local = preferences.getString('notificationStatus');
    if (local != null && local != "") {
      if (local.toLowerCase() == 'true') {
        setState(() {
          notificationStatus = true;
        });
      } else if (local.toLowerCase() == 'false') {
        setState(() {
          notificationStatus = false;
        });
      }
    }
  }

  Future sendNotificationRequest() async {
    final now = DateTime.now();
    DateTime scheduleTime = DateTime(
        now.year, now.month, now.day, selectedTime.hour, selectedTime.minute);
    List<int> arrayDays = [];
    for (var value in selectedDays) {
      arrayDays.add(value.id);
    }
    NotificationService().showNotification(
        1,
        AppLocalizations.of(context).notification,
        AppLocalizations.of(context).notificationDetail,
        scheduleTime,
        arrayDays);
  }

  Future<List<Other>> fetchOtherApp() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    final String resultString = preferences.getString('otherApp') ?? "";
    final List<Other> musics;

    if (resultString != '') {
      List<dynamic> map = jsonDecode(resultString);
      musics = Other.decode(resultString);
      bool found = musics.any((music) => music.appid == Common.appid);
      if (found) {
        Other currentApp =
            musics.firstWhere((element) => element.appid == Common.appid);
        if (currentApp.iosID != "") {
          setState(() {
            Common.appleAppId = currentApp.iosID;
          });
        }
      }
      return musics;
    } else {
      throw Exception('Failed to load list');
    }
  }

  void clearLocalListQuiz() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    preferences.remove('quizList');
    preferences.remove('quizListTimer');
  }

  @override
  void initState() {
    super.initState();
    focusNode.addListener(onSelection);
    NameController.addListener(onSelection);
    _languages.add(LanguageModel(code: 'en', name: 'English'));
    _languages.add(LanguageModel(code: 'vi', name: 'Tiếng Việt'));
    _languages.add(LanguageModel(code: 'de', name: 'German'));
    _languages.add(LanguageModel(code: 'fr', name: 'French'));
    _languages.add(LanguageModel(code: 'hi', name: 'Indian'));
    logPage("Setting");
    getNotificationStatus();
    getDaysLocal();
    futurePost = fetchOtherApp();
  }

  void onSelection() {
    if (focusNode.hasFocus) {
      NameController.selection = TextSelection.fromPosition(
        TextPosition(
          offset: NameController.text.length,
        ),
      );
    }
  }

  @override
  void dispose() {
    NameController.dispose();
    focusNode.dispose();
    super.dispose();
  }

  void updateTimeLang() {
    dayOfWeek[0].name = AppLocalizations.of(context).monday;
    dayOfWeek[1].name = AppLocalizations.of(context).tuesday;
    dayOfWeek[2].name = AppLocalizations.of(context).wednesday;
    dayOfWeek[3].name = AppLocalizations.of(context).thursday;
    dayOfWeek[4].name = AppLocalizations.of(context).friday;
    dayOfWeek[5].name = AppLocalizations.of(context).saturday;
    dayOfWeek[6].name = AppLocalizations.of(context).sunday;
  }

  Future success() => showDialog(
      context: context,
      builder: (context) =>
          SuccessDialog(text: AppLocalizations.of(context).deleteSuccess));
  String valueText = '';
  TextEditingController _textFieldController = TextEditingController();

  Future<void> _displayTextInputDialog(BuildContext context) async {
    return showDialog(
        context: context,
        builder: (context) {
          return AlertDialog(
            insetPadding: EdgeInsets.zero,
            contentPadding: EdgeInsets.zero,
            title: Center(
                child: Text(AppLocalizations.of(context).yourName,
                    style: AppStyles.bodyBold.copyWith(
                        color: Colors.black,
                        fontSize: 18,
                        fontWeight: FontWeight.w700))),
            content: ConstrainedBox(
              constraints: const BoxConstraints(
                maxWidth: 600.0,
              ),
              child: SizedBox(
                width: MediaQuery.of(context).size.width * 0.91,
                child: Padding(
                  padding: const EdgeInsets.only(
                      top: 24.0, left: 16, right: 16, bottom: 12),
                  child: TextFormField(
                    inputFormatters: [
                      LengthLimitingTextInputFormatter(18),
                    ],
                    /* controller: isUserNameValidate == true
                        ? TextEditingController(text: widget.userName)
                        : TextEditingController(text: userName), */
                    controller: NameController,
                    textCapitalization: TextCapitalization.words,
                    autocorrect: false,
                    keyboardType: TextInputType.visiblePassword,
                    decoration: InputDecoration(
                      contentPadding: const EdgeInsets.all(10.0),
                      fillColor: const Color(0xffFAFAFA),
                      filled: true,

                      /* errorText: isUserNameValidate == false
                          ? AppLocalizations.of(context).noUserName
                          : null, */
                      border: OutlineInputBorder(
                          borderSide: BorderSide(color: AppColors.greenButton)),
                    ),
                    style: AppStyles.body.copyWith(
                        color: Colors.black,
                        fontSize: 16,
                        fontWeight: FontWeight.w700),
                    onChanged: (text) {
                      userName = text;
                    },
                  ),
                ),
              ),
            ),
            actions: <Widget>[
              Padding(
                padding: const EdgeInsets.only(right: 8.0, left: 8, bottom: 4),
                child: Row(
                  children: [
                    Expanded(
                        child: Padding(
                      padding:
                          const EdgeInsets.only(top: 0, right: 6, bottom: 12),
                      child: ElevatedButton(
                        onPressed: () {
                          setState(() {
                            Navigator.pop(context);
                          });
                        },
                        style: ElevatedButton.styleFrom(
                          foregroundColor: AppColors.greenPrimary,
                          backgroundColor: Colors.white,
                          shadowColor: Colors.white,
                          elevation: 0,
                          minimumSize: const Size(20, 48),
                          side: BorderSide(
                              color: AppColors.greenPrimary,
                              width: 1.0,
                              style: BorderStyle.solid),
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(4.0)),
                        ),
                        child: Text(
                          AppLocalizations.of(context).cancel,
                          style: AppStyles.secondaryButton,
                          textAlign: TextAlign.center,
                        ),
                      ),
                    )),
                    Expanded(
                        child: Padding(
                      padding:
                          const EdgeInsets.only(top: 0, left: 6, bottom: 12),
                      child: ElevatedButton(
                        onPressed: () {
                          if (!userName.isEmpty) {
                            /* userName == '' ? userName = widget.userName : null; */
                            saveUserName();
                            Common.username = userName;
                            Navigator.pop(context);

                            setState(() {
                              widget.userName = userName;
                            });
                            isUserNameValidate = false;
                            logEvent("setting_name_update", {});
                          } else {
                            showDialog(
                              context: context,
                              builder: (BuildContext context) {
                                return alert;
                              },
                            );

                            setState(() {
                              userName = widget.userName;
                            });
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          foregroundColor: AppColors.white,
                          backgroundColor: AppColors.greenPrimary,
                          shadowColor: const Color.fromARGB(92, 0, 166, 144),
                          elevation: 4,
                          minimumSize: const Size(20, 48),
                          side: BorderSide(
                              color: AppColors.greenPrimary,
                              width: 1.0,
                              style: BorderStyle.solid),
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(4.0)),
                        ),
                        child: Text(
                          AppLocalizations.of(context).save,
                          style: AppStyles.primaryButton,
                        ),
                      ),
                    ))
                  ],
                ),
              ),
            ],
          );
        });
  }

  void _showConfirmDialog() {
    showDialog<String>(
      context: context,
      builder: (BuildContext context) => Dialog(
        // contentPadding: EdgeInsets.fromLTRB(16, 30, 16, 24),
        insetPadding: const EdgeInsets.symmetric(horizontal: 16),
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.fromLTRB(16, 30, 16, 24),
            child: Column(
              children: [
                Container(
                  width: 196,
                  height: 139,
                  decoration: const BoxDecoration(
                      image: DecorationImage(
                    image: AssetImage("assets/images/info_dialog.png"),
                    fit: BoxFit.fill,
                  )),
                ),
                Text(
                  AppLocalizations.of(context).confirmDeleteQuestionReview,
                  style: AppStyles.dialogText,
                  textAlign: TextAlign.center,
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 24),
                  child: SizedBox(
                    width: MediaQuery.of(context).size.width,
                    child: Row(
                      children: [
                        Expanded(
                            child: Padding(
                          padding: const EdgeInsets.only(right: 6),
                          child: ElevatedButton(
                            onPressed: () {
                              Navigator.pop(context);
                            },
                            style: ElevatedButton.styleFrom(
                              foregroundColor: AppColors.greenPrimary,
                              backgroundColor: Colors
                                  .white, //specify the color of the button's text and icons as well as the overlay colors used to indicate the hover, focus, and pressed states
                              shadowColor: Colors
                                  .white, //specify the button's elevation color
                              elevation: 0, //buttons Material shadow
                              // padding: const EdgeInsets.only(top: 4.0, bottom: 4.0, right: 8.0, left: 8.0), //specify the button's Padding
                              minimumSize: const Size(20,
                                  44), //specify the button's first: width and second: height
                              side: BorderSide(
                                  color: AppColors.greenPrimary,
                                  width: 1.0,
                                  style: BorderStyle
                                      .solid), //set border for the button
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(4.0)),
                            ),
                            child: Text(
                              AppLocalizations.of(context).no,
                              style: AppStyles.secondaryButton,
                              textAlign: TextAlign.center,
                            ),
                          ),
                        )),
                        Expanded(
                            child: Padding(
                          padding: const EdgeInsets.only(left: 6),
                          child: ElevatedButton(
                            onPressed: () async {
                              await resetData();
                              Navigator.pop(context);
                              success();
                            },
                            style: ElevatedButton.styleFrom(
                              foregroundColor: AppColors.white,
                              backgroundColor: AppColors.greenPrimary,
                              shadowColor:
                                  const Color.fromARGB(92, 0, 166, 144),
                              elevation: 0,
                              minimumSize: const Size(20, 44),
                              side: BorderSide(
                                  color: AppColors.greenPrimary,
                                  width: 1.0,
                                  style: BorderStyle.solid),
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(4.0)),
                            ),
                            child: Text(
                              AppLocalizations.of(context).yes,
                              style: AppStyles.primaryButton,
                            ),
                          ),
                        ))
                      ],
                    ),
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> resetData() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    preferences.remove("incorrectQuestion");
    preferences.remove("bookmarkQuestion");
    preferences.remove("reportedQuestion");
    preferences.remove("resumeQuiz");
    preferences.remove("result");
    preferences.remove('flashcard_favorite');
    preferences.remove('flashcard_skip');
    preferences.remove('flashcard_done');
    preferences.remove('flashcard_current_index');
    preferences.remove('flashcard_show_end');
    String localQuestionList = preferences.getString('quizList') ?? '';
    if (localQuestionList != '') {
      final parsed =
          json.decode(localQuestionList).cast<Map<String, dynamic>>();
      List<Qlist> qlist =
          parsed.map<Qlist>((json) => Qlist.fromMap(json)).toList();
      for (int i = 0; i < qlist.length; i++) {
        if (preferences.getInt('timeDoQuiz_${qlist[i].quid}') != null) {
          preferences.remove('timeDoQuiz_${qlist[i].quid}');
        }
        if (preferences.getString('answeredText_${qlist[i].quid}') != null) {
          preferences.remove('answeredText_${qlist[i].quid}');
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    var language = Localizations.localeOf(context).toString();
    userName = Common.username;
    getPlan();
    _selectTime(BuildContext context) async {
      SharedPreferences preferences = await SharedPreferences.getInstance();
      final TimeOfDay? timeOfDay = await showTimePicker(
        context: context,
        initialTime: selectedTime,
        initialEntryMode: TimePickerEntryMode.dial,
      );
      if (timeOfDay != null && timeOfDay != selectedTime) {
        //await preferences.setString('time', lang);
        var arr = [timeOfDay.hour, timeOfDay.minute];
        var timeDetails = jsonEncode(arr);
        await preferences.setString('notificationTime', timeDetails);
        setState(() {
          selectedTime = timeOfDay;
        });
        logEvent("setting_reminder_time_update",
            {"reminder_time_update": "${timeOfDay.hour}:${timeOfDay.minute}"});
        if (notificationStatus) {
          sendNotificationRequest();
        }
      }
    }

    return Scaffold(
      resizeToAvoidBottomInset: true,
      backgroundColor: const Color(0xffFAFAFA),
      appBar: AppBar(
        /* iconTheme: IconThemeData(
          color: Colors.black, //change your color here
        ), */

        leading: Padding(
          padding: const EdgeInsets.only(top: 10.0),
          child: IconButton(
            icon: const Icon(Icons.arrow_back_ios_new),
            iconSize: 20.0,
            color: AppColors.blackText,
            onPressed: () => _goBack(context),
          ),
        ),
        centerTitle: true,
        backgroundColor: const Color(0xffFAFAFA),
        title: Padding(
          padding: const EdgeInsets.only(top: 15.0),
          child: Text(
            AppLocalizations.of(context).userDetail,
            style: AppStyles.appBarTitle
                .copyWith(fontSize: 20, fontWeight: FontWeight.w700),
          ),
        ),
        bottom: const PreferredSize(
            preferredSize: Size.zero,
            child: Divider(
              color: Color(0xffE6E6E7),
              thickness: 2,
            )),
        elevation: 0,
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            //flex: 1,

            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Common.premium == false
                        ? GestureDetector(
                            onTap: () {
                              logEvent("setting_premium_click", {});
                              Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          const PaymentPage()));
                            },
                            child: Stack(
                              clipBehavior: Clip.none,
                              children: [
                                Container(
                                  margin: EdgeInsets.only(
                                      top: language == "de"
                                          ? 20
                                          : language == "hi"
                                              ? 35
                                              : 40,
                                      left: 16,
                                      right: 16),
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(8),
                                      gradient: LinearGradient(
                                        begin: Alignment.topCenter,
                                        end: Alignment.bottomCenter,
                                        colors: [
                                          AppColors.gradientBannerTop,
                                          AppColors.gradientBannerBottom,
                                        ],
                                      )),
                                  //height: 140,
                                  width: MediaQuery.of(context).size.width,
                                  child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        SizedBox(
                                          width: MediaQuery.of(context)
                                                  .size
                                                  .width *
                                              0.66,
                                          child: Padding(
                                            padding: const EdgeInsets.all(15.0),
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                    AppLocalizations.of(context)
                                                        .upgradeToGetFeature,
                                                    style: AppStyles.bodyBold
                                                        .copyWith(
                                                            color: Colors.white,
                                                            fontSize: 18)),
                                                Padding(
                                                  padding:
                                                      const EdgeInsets.only(
                                                          top: 10.0),
                                                  child: Row(
                                                    children: [
                                                      Text(
                                                        AppLocalizations.of(
                                                                context)
                                                            .upgrade,
                                                        style: AppStyles
                                                            .body
                                                            .copyWith(
                                                                color: const Color(
                                                                    0xff00F9D7),
                                                                fontSize: 12,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .bold),
                                                      ),
                                                      const SizedBox(
                                                        width: 3,
                                                      ),
                                                      const Padding(
                                                        padding:
                                                            EdgeInsets.only(
                                                                top: 2),
                                                        child: Icon(
                                                          Icons
                                                              .arrow_forward_rounded,
                                                          size: 16,
                                                          color:
                                                              Color(0xff00F9D7),
                                                        ),
                                                      )
                                                    ],
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        )
                                      ]),
                                ),
                                Positioned(
                                  bottom: -8,
                                  right: 0,
                                  child: SvgPicture.asset(
                                    'assets/images/SettingManIcon.svg',
                                    width: MediaQuery.of(context).size.width <
                                            400
                                        ? MediaQuery.of(context).size.width *
                                            0.45
                                        : 150,
                                  ),
                                ),
                              ],
                            ),
                          )
                        : Container(),
                    Padding(
                      padding: const EdgeInsets.only(
                          bottom: 10.0, top: 20, left: 16, right: 16),
                      child: Text(AppLocalizations.of(context).detail,
                          style: AppStyles.titleBody16.copyWith(
                              fontSize: 18, fontWeight: FontWeight.w600)),
                    ),
                    Container(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Column(children: [
                          GestureDetector(
                            behavior: HitTestBehavior.opaque,
                            onTap: () {
                              _displayTextInputDialog(context);
                            },
                            child: Padding(
                              padding: const EdgeInsets.fromLTRB(0, 10, 8, 10),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Wrap(
                                    children: [
                                      SvgPicture.asset(
                                          'assets/images/FrameProfile.svg',
                                          fit: BoxFit.fitHeight),
                                      Padding(
                                        padding: const EdgeInsets.only(
                                            top: 2.0, left: 8),
                                        child: Text(
                                          AppLocalizations.of(context).name,
                                          style: AppStyles.body.copyWith(
                                            color: Colors.black,
                                            fontSize: 16,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  Row(
                                    children: [
                                      Text(widget.userName,
                                          style: AppStyles.body.copyWith(
                                              color: const Color(0xff7A8694),
                                              fontSize: 16,
                                              fontWeight: FontWeight.w500)),
                                      const SizedBox(
                                        width: 5,
                                      ),
                                      const Icon(
                                        Icons.arrow_forward_ios_rounded,
                                        size: 14,
                                        color: Color(0xff7A8694),
                                      )
                                    ],
                                  )
                                ],
                              ),
                            ),
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          Padding(
                            padding: const EdgeInsets.only(right: 11),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Wrap(
                                  children: [
                                    SvgPicture.asset('assets/images/Plan.svg',
                                        fit: BoxFit.fitHeight),
                                    Padding(
                                      padding: const EdgeInsets.only(
                                          top: 2.0, left: 8),
                                      child: Text(
                                        AppLocalizations.of(context).plan,
                                        style: AppStyles.body.copyWith(
                                          color: Colors.black,
                                          fontSize: 16,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                Row(
                                  children: [
                                    Common.premium
                                        ? SizedBox(
                                            width: 14,
                                            height: 14,
                                            child: SvgPicture.asset(
                                              'assets/images/star.svg',
                                              fit: BoxFit.contain,
                                              //width: MediaQuery.of(context).size.width * 0.6,
                                            ),
                                          )
                                        : Container(),
                                    Padding(
                                      padding: const EdgeInsets.only(left: 4),
                                      child: RichText(
                                        text: TextSpan(
                                          text: Common.premium
                                              ? Common.trial
                                                  ? AppLocalizations.of(context)
                                                      .trial
                                                  : "Premium"
                                              : "Free",
                                          style: Common.premium
                                              ? AppStyles.body.copyWith(
                                                  color:
                                                      const Color(0xff116C78),
                                                  fontSize: 16,
                                                  fontWeight: FontWeight.w600)
                                              : AppStyles.body.copyWith(
                                                  color:
                                                      const Color(0xff7A8694),
                                                  fontSize: 16,
                                                  fontWeight: FontWeight.w500),
                                          children: <TextSpan>[
                                            TextSpan(
                                                text: plan,
                                                style: AppStyles.body.copyWith(
                                                    color:
                                                        const Color(0xff7A8694),
                                                    fontSize: 16,
                                                    fontWeight:
                                                        FontWeight.w500)),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ],
                                )
                              ],
                            ),
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                        ]),
                      ),
                    ),
                    const Divider(
                      color: Color(0xffF2F5FC),
                      thickness: 5,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(
                          bottom: 10.0, top: 10, left: 16),
                      child: Text(AppLocalizations.of(context).userDetail,
                          style: AppStyles.titleBody16.copyWith(
                              fontSize: 18, fontWeight: FontWeight.w600)),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            GestureDetector(
                              behavior: HitTestBehavior.opaque,
                              onTap: () {
                                showModalLang(context);
                              },
                              child: Padding(
                                padding:
                                    const EdgeInsets.fromLTRB(0, 10, 8, 10),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Wrap(
                                      children: [
                                        SvgPicture.asset(
                                            'assets/images/GlobeHemisphereWest.svg',
                                            fit: BoxFit.fitHeight),
                                        Padding(
                                          padding: const EdgeInsets.only(
                                              top: 2.0, left: 8),
                                          child: Text(
                                            AppLocalizations.of(context).lang,
                                            style: AppStyles.body.copyWith(
                                              color: Colors.black,
                                              fontSize: 16,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                    Row(
                                      children: [
                                        Text(
                                            showLangName(
                                                Localizations.localeOf(context)
                                                    .toString()),
                                            style: AppStyles.body.copyWith(
                                                color: const Color(0xff7A8694),
                                                fontSize: 16,
                                                fontWeight: FontWeight.w500)),
                                        const SizedBox(
                                          width: 5,
                                        ),
                                        const Icon(
                                          Icons.arrow_forward_ios_rounded,
                                          size: 16,
                                          color: Color(0xff7A8694),
                                        )
                                      ],
                                    )
                                  ],
                                ),
                              ),
                            ),
                            GestureDetector(
                              behavior: HitTestBehavior.opaque,
                              onTap: () {
                                showModal(context);
                              },
                              child: Padding(
                                padding:
                                    const EdgeInsets.fromLTRB(0, 10, 8, 10),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Wrap(
                                      children: [
                                        SvgPicture.asset(
                                            'assets/images/TextT.svg',
                                            fit: BoxFit.fitHeight),
                                        Padding(
                                          padding: const EdgeInsets.only(
                                              top: 2.0, left: 8),
                                          child: Text(
                                            AppLocalizations.of(context)
                                                .fontExam,
                                            style: AppStyles.body.copyWith(
                                              color: Colors.black,
                                              fontSize: 16,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                    Row(
                                      children: [
                                        Text(fontvalue,
                                            style: AppStyles.body.copyWith(
                                                color: const Color(0xff7A8694),
                                                fontSize: 14,
                                                fontWeight: FontWeight.w600)),
                                        const SizedBox(
                                          width: 5,
                                        ),
                                        const Icon(
                                          Icons.arrow_forward_ios_rounded,
                                          size: 16,
                                          color: Color(0xff7A8694),
                                        )
                                      ],
                                    )
                                  ],
                                ),
                              ),
                            ),
                            GestureDetector(
                              behavior: HitTestBehavior.opaque,
                              onTap: () {},
                              child: Padding(
                                padding:
                                    const EdgeInsets.fromLTRB(0, 10, 8, 10),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Wrap(
                                      children: [
                                        SvgPicture.asset(
                                            'assets/images/Bell.svg',
                                            fit: BoxFit.fitHeight),
                                        Padding(
                                          padding: const EdgeInsets.only(
                                              top: 2.0, left: 8),
                                          child: Text(
                                            AppLocalizations.of(context)
                                                .notificationStatus,
                                            style: AppStyles.body.copyWith(
                                              color: Colors.black,
                                              fontSize: 16,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                    Padding(
                                      padding:
                                          const EdgeInsets.only(right: 0.0),
                                      child: buildSwitch(),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            notificationStatus
                                ? Wrap(
                                    children: [
                                      GestureDetector(
                                        behavior: HitTestBehavior.opaque,
                                        onTap: () {
                                          _selectTime(context);
                                        },
                                        child: Padding(
                                          padding: const EdgeInsets.fromLTRB(
                                              0, 10, 8, 10),
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Wrap(
                                                children: [
                                                  SvgPicture.asset(
                                                      'assets/images/Timer.svg',
                                                      fit: BoxFit.fitHeight),
                                                  Padding(
                                                    padding:
                                                        const EdgeInsets.only(
                                                            top: 2.0, left: 8),
                                                    child: Text(
                                                      AppLocalizations.of(
                                                              context)
                                                          .notificationTime,
                                                      style: AppStyles.body
                                                          .copyWith(
                                                        color: Colors.black,
                                                        fontSize: 16,
                                                        fontWeight:
                                                            FontWeight.w500,
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                              Row(
                                                children: [
                                                  Padding(
                                                    padding:
                                                        const EdgeInsets.only(
                                                            right: 5.0),
                                                    child: Text(
                                                        "${selectedTime.hour.toString().padLeft(2, "0")}:${selectedTime.minute.toString().padLeft(2, "0")}",
                                                        style: AppStyles
                                                            .body
                                                            .copyWith(
                                                                color: const Color(
                                                                    0xff7A8694),
                                                                fontSize: 16,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w500)),
                                                  ),
                                                  const Icon(
                                                    Icons
                                                        .arrow_forward_ios_rounded,
                                                    size: 16,
                                                    color: Color(0xff7A8694),
                                                  )
                                                ],
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                      GestureDetector(
                                        behavior: HitTestBehavior.opaque,
                                        onTap: () {
                                          showModalSelectDate(context);
                                        },
                                        child: Padding(
                                          padding: const EdgeInsets.fromLTRB(
                                              0, 10, 8, 10),
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Wrap(
                                                children: [
                                                  SvgPicture.asset(
                                                      'assets/images/Timer.svg',
                                                      fit: BoxFit.fitHeight),
                                                  Padding(
                                                    padding:
                                                        const EdgeInsets.only(
                                                            top: 2.0, left: 8),
                                                    child: Text(
                                                      AppLocalizations.of(
                                                              context)
                                                          .notificationDays,
                                                      style: AppStyles.body
                                                          .copyWith(
                                                        color: Colors.black,
                                                        fontSize: 16,
                                                        fontWeight:
                                                            FontWeight.w500,
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                              Row(
                                                children: [
                                                  const Icon(
                                                    Icons
                                                        .arrow_forward_ios_rounded,
                                                    size: 16,
                                                    color: Color(0xff7A8694),
                                                  )
                                                ],
                                              )
                                            ],
                                          ),
                                        ),
                                      ),
                                    ],
                                  )
                                : Container(),
                            if (Common.premium) ...{
                              GestureDetector(
                                behavior: HitTestBehavior.opaque,
                                onTap: _showConfirmDialog,
                                child: Padding(
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 10),
                                  child: Row(
                                    children: [
                                      Expanded(
                                        child: Wrap(
                                          children: [
                                            SvgPicture.asset(
                                                'assets/images/ArrowsCounterClockwise.svg',
                                                fit: BoxFit.fitHeight),
                                            Padding(
                                              padding: const EdgeInsets.only(
                                                  top: 2.0, left: 8),
                                              child: Text(
                                                  AppLocalizations.of(context)
                                                      .resetData,
                                                  style: AppStyles.body
                                                      .copyWith(
                                                          color: const Color(
                                                              0xffc22930),
                                                          fontSize: 16,
                                                          fontWeight:
                                                              FontWeight.w500)),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              )
                            }
                          ]),
                    ),
                    const Divider(
                      color: Color(0xffF2F5FC),
                      thickness: 5,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(
                          bottom: 10.0, top: 10, left: 16),
                      child: Text(AppLocalizations.of(context).otherApp,
                          style: AppStyles.titleBody16.copyWith(
                              fontSize: 18, fontWeight: FontWeight.w600)),
                    ),
                    Container(
                      width: MediaQuery.of(context).size.width,
                      decoration: const BoxDecoration(
                          color: Colors.transparent,
                          borderRadius: BorderRadius.all(Radius.circular(8))),
                      child: Padding(
                        padding:
                            const EdgeInsets.only(top: 8, left: 8, right: 8),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SingleChildScrollView(
                              scrollDirection: Axis.horizontal,
                              child: Center(
                                child: SizedBox(
                                  height: 137,
                                  /* width:
                                                MediaQuery.of(context).size.width, */
                                  child: FutureBuilder<List<Other>>(
                                      future: futurePost,
                                      builder: (BuildContext context,
                                          AsyncSnapshot snapshot) {
                                        if (snapshot.hasData) {
                                          return Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.start,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.center,
                                            children: [
                                              ListView.builder(
                                                  itemCount:
                                                      snapshot.data.length,
                                                  shrinkWrap: true,
                                                  scrollDirection:
                                                      Axis.horizontal,
                                                  itemBuilder:
                                                      (BuildContext context,
                                                          int index) {
                                                    if (snapshot.data[index]
                                                            .appid !=
                                                        Common.appid) {
                                                      return GestureDetector(
                                                        onTap: () {
                                                          logEvent(
                                                              "about_us_other_apps_click",
                                                              {
                                                                "other_app_click_name":
                                                                    snapshot
                                                                        .data[
                                                                            index]
                                                                        .appname
                                                              });
                                                          if (snapshot
                                                                      .data[
                                                                          index]
                                                                      .iosID !=
                                                                  "" &&
                                                              snapshot
                                                                      .data[
                                                                          index]
                                                                      .androidID !=
                                                                  "") {
                                                            StoreRedirect.redirect(
                                                                androidAppId:
                                                                    snapshot
                                                                        .data[
                                                                            index]
                                                                        .androidID,
                                                                iOSAppId: snapshot
                                                                    .data[index]
                                                                    .iosID);
                                                          }
                                                        },
                                                        child: Padding(
                                                          padding:
                                                              const EdgeInsets
                                                                  .all(8.0),
                                                          child: SizedBox(
                                                            width: 70,
                                                            height: 70,
                                                            child: Column(
                                                              children: [
                                                                SizedBox(
                                                                  width: 70,
                                                                  height: 70,
                                                                  /* decoration:
                                                                              BoxDecoration(
                                                                            borderRadius:
                                                                                BorderRadius.all(Radius.circular(10)),
                                                                            image:
                                                                                DecorationImage(
                                                                              image:
                                                                                  /*  AssetImage("assets/launcher_icon/${snapshot.data[index].app_icon}") */ NetworkImage("https://drive.google.com/uc?export=view&id=1xp1jKbh7XXKuZE42Uot53gymb8L4q6vuss", ),
                                                                              onError: (error, stackTrace) =>
                                                                                  AssetImage("assets/launcher_icon/${snapshot.data[index].app_icon}"),
                                                                              fit:
                                                                                  BoxFit.fill,
                                                                            ),
                                                                          ), */
                                                                  child:
                                                                      CachedNetworkImage(
                                                                    imageUrl:
                                                                        "https://dev.scrumpass.com/images/app_icon/${snapshot.data[index].app_icon}",
                                                                    placeholder: (context, url) => SizedBox(
                                                                        height:
                                                                            10,
                                                                        width:
                                                                            10,
                                                                        child: const Center(
                                                                            child:
                                                                                CircularProgressIndicator())),
                                                                    errorWidget: (context,
                                                                            url,
                                                                            error) =>
                                                                        Image(
                                                                            image:
                                                                                AssetImage("assets/launcher_icon/${snapshot.data[index].app_icon}")),
                                                                  ),
                                                                ),
                                                                const SizedBox(
                                                                  height: 6,
                                                                ),
                                                                Text(
                                                                  snapshot
                                                                      .data[
                                                                          index]
                                                                      .appname,
                                                                  textAlign:
                                                                      TextAlign
                                                                          .center,
                                                                  style: AppStyles.body.copyWith(
                                                                      color: const Color(
                                                                          0xff7A8694),
                                                                      fontSize:
                                                                          12,
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .w500),
                                                                  maxLines: 3,
                                                                  overflow:
                                                                      TextOverflow
                                                                          .ellipsis,
                                                                ),
                                                              ],
                                                            ),
                                                          ),
                                                        ),
                                                      );
                                                    } else {
                                                      return Container();
                                                    }
                                                  }),
                                            ],
                                          );
                                        } else if (snapshot.hasError) {
                                          return Center(
                                            child: Text(
                                                AppLocalizations.of(context)
                                                    .noInfo,
                                                style: AppStyles.body.copyWith(
                                                    color: Colors.black,
                                                    fontSize: 16,
                                                    fontWeight:
                                                        FontWeight.w500)),
                                          );
                                        } else {
                                          return const Center(
                                              child:
                                                  CircularProgressIndicator());
                                        }
                                      }),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const Divider(
                      color: Color(0xffF2F5FC),
                      thickness: 5,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(
                          bottom: 10.0, top: 10, left: 16),
                      child: Text(AppLocalizations.of(context).support,
                          style: AppStyles.titleBody16.copyWith(
                              fontSize: 18, fontWeight: FontWeight.w600)),
                    ),
                    Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                            child: Wrap(
                              children: [
                                GestureDetector(
                                  behavior: HitTestBehavior.opaque,
                                  onTap: () {
                                    Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                            builder: (context) =>
                                                NewAboutUs()));
                                  },
                                  child: Padding(
                                    padding:
                                        const EdgeInsets.fromLTRB(0, 10, 8, 10),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Wrap(
                                          children: [
                                            SvgPicture.asset(
                                                'assets/images/Info.svg',
                                                fit: BoxFit.fitHeight),
                                            Padding(
                                              padding: const EdgeInsets.only(
                                                  top: 2.0, left: 8),
                                              child: Text(
                                                AppLocalizations.of(context)
                                                    .aboutUs,
                                                style: AppStyles.body.copyWith(
                                                  color: Colors.black,
                                                  fontSize: 16,
                                                  fontWeight: FontWeight.w500,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                        Row(
                                          children: [
                                            const SizedBox(
                                              width: 5,
                                            ),
                                            const Icon(
                                              Icons.arrow_forward_ios_rounded,
                                              size: 16,
                                              color: Color(0xff7A8694),
                                            )
                                          ],
                                        )
                                      ],
                                    ),
                                  ),
                                ),
                                GestureDetector(
                                  behavior: HitTestBehavior.opaque,
                                  onTap: () {
                                    logEvent("about_us_rate_us_click", {});
                                    LaunchReview.launch(
                                        androidAppId: Common.appid,
                                        iOSAppId: Common.appleAppId);
                                  },
                                  child: Padding(
                                    padding:
                                        const EdgeInsets.fromLTRB(0, 10, 8, 10),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Wrap(
                                          children: [
                                            SvgPicture.asset(
                                                'assets/images/StarIcon.svg',
                                                fit: BoxFit.fitHeight),
                                            Padding(
                                              padding: const EdgeInsets.only(
                                                  top: 2.0, left: 8),
                                              child: Text(
                                                AppLocalizations.of(context)
                                                    .rateUs,
                                                style: AppStyles.body.copyWith(
                                                  color: Colors.black,
                                                  fontSize: 16,
                                                  fontWeight: FontWeight.w500,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                        Row(
                                          children: [
                                            const SizedBox(
                                              width: 5,
                                            ),
                                            const Icon(
                                              Icons.arrow_forward_ios_rounded,
                                              size: 16,
                                              color: Color(0xff7A8694),
                                            )
                                          ],
                                        )
                                      ],
                                    ),
                                  ),
                                ),
                                GestureDetector(
                                  behavior: HitTestBehavior.opaque,
                                  onTap: () {
                                    final box = context.findRenderObject()
                                        as RenderBox?;
                                    Share.share(
                                      '${AppLocalizations.of(context).getCertificate.replaceAll("[certificate_name]", Common.certificate_name).replaceAll("[app_name]", Common.appName)}.\n${AppLocalizations.of(context).iosLink}${Common.appleAppId}\n${AppLocalizations.of(context).androidLink}${Common.appid}',
                                      sharePositionOrigin:
                                          box!.localToGlobal(Offset.zero) &
                                              box.size,
                                    );
                                  },
                                  child: Padding(
                                    padding:
                                        const EdgeInsets.fromLTRB(0, 10, 8, 10),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Wrap(
                                          children: [
                                            SvgPicture.asset(
                                                'assets/images/ShareNetwork.svg',
                                                fit: BoxFit.fitHeight),
                                            Padding(
                                              padding: const EdgeInsets.only(
                                                  top: 2.0, left: 8),
                                              child: Text(
                                                AppLocalizations.of(context)
                                                    .shareApp,
                                                style: AppStyles.body.copyWith(
                                                  color: Colors.black,
                                                  fontSize: 16,
                                                  fontWeight: FontWeight.w500,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                        Row(
                                          children: [
                                            const SizedBox(
                                              width: 5,
                                            ),
                                            const Icon(
                                              Icons.arrow_forward_ios_rounded,
                                              size: 16,
                                              color: Color(0xff7A8694),
                                            )
                                          ],
                                        )
                                      ],
                                    ),
                                  ),
                                ),
                                //Them
                                GestureDetector(
                                  behavior: HitTestBehavior.opaque,
                                  onTap: () async {
                                    Clipboard.setData(
                                            ClipboardData(text: Common.debugId))
                                        .then((_) {
                                      ScaffoldMessenger.of(context)
                                          .showSnackBar(const SnackBar(
                                              content: Text(
                                                  'Copied to your clipboard !')));
                                    });
                                  },
                                  child: Padding(
                                    padding:
                                        const EdgeInsets.fromLTRB(0, 10, 8, 10),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.end,
                                      children: [
                                        Wrap(
                                          children: [
                                            SvgPicture.asset(
                                                'assets/images/BugDroid.svg',
                                                fit: BoxFit.fitHeight),
                                            Padding(
                                              padding: const EdgeInsets.only(
                                                  top: 2.0, left: 8),
                                              child: Text(
                                                "Debug ID",
                                                style: AppStyles.body.copyWith(
                                                  color: Colors.black,
                                                  fontSize: 16,
                                                  fontWeight: FontWeight.w500,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                        Wrap(
                                          children: [
                                            Padding(
                                              padding: const EdgeInsets.only(
                                                  top: 3.0, right: 4),
                                              child: Text(Common.debugId,
                                                  style: AppStyles.body
                                                      .copyWith(
                                                          color: const Color(
                                                              0xff7A8694),
                                                          fontSize: 16,
                                                          fontWeight:
                                                              FontWeight.w500)),
                                            ),
                                            GestureDetector(
                                              onTap: () async {
                                                Clipboard.setData(ClipboardData(
                                                        text: Common.debugId))
                                                    .then((_) {
                                                  ScaffoldMessenger.of(context)
                                                      .showSnackBar(const SnackBar(
                                                          content: Text(
                                                              'Copied to your clipboard !')));
                                                });
                                              },
                                              child: SvgPicture.asset(
                                                  'assets/images/CopySimple.svg',
                                                  fit: BoxFit.fitHeight),
                                            ),
                                          ],
                                        )
                                      ],
                                    ),
                                  ),
                                ),
                                GestureDetector(
                                  behavior: HitTestBehavior.opaque,
                                  onPanCancel: () => _timer.cancel(),
                                  onPanDown: (_) => {
                                    _timer =
                                        Timer(const Duration(seconds: 2), () {
                                      // Your function goes here
                                      _showDialog();
                                    })
                                  },
                                  child: Padding(
                                    padding:
                                        const EdgeInsets.fromLTRB(0, 10, 8, 10),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        /*  Flexible(
                                      child: Text(
                                        AppLocalizations.of(context).version,
                                        style: AppStyles.body.copyWith(
                                          color: Colors.black,
                                          fontSize: 16,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ), */
                                        Wrap(
                                          children: [
                                            SvgPicture.asset(
                                                'assets/images/GitBranch.svg',
                                                fit: BoxFit.fitHeight),
                                            Padding(
                                              padding: const EdgeInsets.only(
                                                  top: 2.0, left: 8),
                                              child: Text(
                                                AppLocalizations.of(context)
                                                    .version,
                                                style: AppStyles.body.copyWith(
                                                  color: Colors.black,
                                                  fontSize: 16,
                                                  fontWeight: FontWeight.w500,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                        Text(Common.version,
                                            style: AppStyles.body.copyWith(
                                                color: const Color(0xff7A8694),
                                                fontSize: 16,
                                                fontWeight: FontWeight.w500)),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const Divider(
                            color: Color(0xffF2F5FC),
                            thickness: 5,
                          ),
                          Center(
                            child: ConstrainedBox(
                              constraints: const BoxConstraints(
                                maxWidth: 600.0,
                              ),
                              child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    const SizedBox(
                                      height: 16,
                                    ),
                                    Center(
                                      child: SizedBox(
                                        width:
                                            MediaQuery.of(context).size.width *
                                                0.5,
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceAround,
                                          children: [
                                            GestureDetector(
                                              onTap: () {
                                                launchUrl(
                                                  Uri.parse(
                                                      "https://www.facebook.com/examsimulatorapp/"),
                                                  mode: LaunchMode
                                                      .externalApplication,
                                                );
                                              },
                                              child: SvgPicture.asset(
                                                'assets/images/Facebook.svg',
                                                fit: BoxFit.contain,
                                              ),
                                            ),
                                            GestureDetector(
                                                onTap: () {
                                                  launchUrl(
                                                    Uri.parse(
                                                      "https://m.me/examsimulatorapp/",
                                                    ),
                                                    mode: LaunchMode
                                                        .externalApplication,
                                                  );
                                                },
                                                child: SizedBox(
                                                  //width: 20,
                                                  height: 22,
                                                  child: Image.asset(
                                                      'assets/images/messengerIcon.jpg',
                                                      color: const Color(
                                                          0xff00397C),
                                                      fit: BoxFit.fill),
                                                )),
                                            GestureDetector(
                                              onTap: () {
                                                launchUrl(Uri(
                                                  scheme: 'tel',
                                                  path: '+84945694499',
                                                ));
                                              },
                                              child: SvgPicture.asset(
                                                'assets/images/Call.svg',
                                                fit: BoxFit.contain,
                                              ),
                                            ),
                                            GestureDetector(
                                              onTap: () {
                                                launchUrl(
                                                  Uri.parse(
                                                      "https://examsimulator.net/"),
                                                  mode: LaunchMode
                                                      .externalApplication,
                                                );
                                              },
                                              child: SvgPicture.asset(
                                                'assets/images/Web.svg',
                                                fit: BoxFit.contain,
                                              ),
                                            ),
                                            GestureDetector(
                                              onTap: () {
                                                launchUrl(Uri.parse(
                                                    "mailto:<EMAIL>"));
                                              },
                                              child: SvgPicture.asset(
                                                'assets/images/Mail.svg',
                                                fit: BoxFit.contain,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                    const SizedBox(
                                      height: 20,
                                    ),
                                    IntrinsicHeight(
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          GestureDetector(
                                            onTap: () {
                                              launch(
                                                  "https://scrumpass.com/terms-of-service/");
                                            },
                                            child: Container(
                                              child: Text(
                                                AppLocalizations.of(context)
                                                    .term,
                                                textAlign: TextAlign.center,
                                                style: AppStyles.bodyBold
                                                    .copyWith(
                                                        color: const Color(
                                                            0xff00397C),
                                                        fontSize: 14),
                                              ),
                                            ),
                                          ),
                                          const VerticalDivider(
                                            color: Color(0xff00397C),
                                            thickness: 2,
                                            width: 10,
                                          ),
                                          GestureDetector(
                                            onTap: () {
                                              launch(
                                                  "https://examsimulator.net/privacy-policy");
                                            },
                                            child: Container(
                                              child: Text(
                                                AppLocalizations.of(context)
                                                    .privacyPolicy,
                                                textAlign: TextAlign.center,
                                                style: AppStyles.bodyBold
                                                    .copyWith(
                                                        color: const Color(
                                                            0xff00397C),
                                                        fontSize: 14),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    const SizedBox(
                                      height: 40,
                                    ),
                                  ]),
                            ),
                          ),
                        ]),

                    /* Padding(
                      padding: const EdgeInsets.only(
                          bottom: 50, top: 20, left: 20, right: 16),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          GestureDetector(
                            onPanCancel: () => _timer.cancel(),
                            onPanDown: (_) => {
                              _timer = Timer(const Duration(seconds: 2), () {
                                // Your function goes here
                                _showDialog();
                              })
                            },
                            child: Container(
                              color: Colors.transparent,
                              child: Text(
                                AppLocalizations.of(context).version +
                                    " " +
                                    Common.version,
                                textAlign: TextAlign.center,
                                style: AppStyles.body.copyWith(
                                    color: const Color(0xff7A8694),
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500),
                              ),
                            ),
                          ),
                          GestureDetector(
                            onLongPress: () => copyDebugToClipboard(context),
                            onDoubleTap: () => copyDebugToClipboard(context),
                            child: Container(
                              color: Colors.transparent,
                              child: Text(
                                "Debug ID: " + Common.debugId,
                                textAlign: TextAlign.center,
                                style: AppStyles.body.copyWith(
                                    color: const Color(0xff7A8694),
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500),
                              ),
                            ),
                          )
                        ],
                      ),
                    ), */
                  ],
                ),
              ),
            ),
          ],
        ),

        /* Container(
                  child: Padding(
                    padding: const EdgeInsets.only(bottom: 20),
                    child: ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        minimumSize: Size.fromHeight(40),
                        primary: Color(0xff00A690),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Text(
                          AppLocalizations.of(context).save,
                          textAlign: TextAlign.center,
                          style: AppStyles.bodyBold
                              .copyWith(color: AppColors.white, fontSize: 18),
                        ),
                      ),
                      onPressed: widget.userName != ''
                          ? () {
                              print(userName);
                              if (!userName.isEmpty) {
                                /* userName == '' ? userName = widget.userName : null; */
                                print(widget.userName);
                                saveUserName();
                                Common.username = userName;
                                Navigator.pop(context);
                              }
                              isUserNameValidate = false;
                              setState(() {});
                            }
                          : () {
                              showDialog(
                                context: context,
                                builder: (BuildContext context) {
                                  return alert;
                                },
                              );
                            },
                    ),
                  ),
                ), */
      ),
    );
  }

  void copyDebugToClipboard(context) async {
    await Clipboard.setData(ClipboardData(text: Common.debugId));
    ScaffoldMessenger.of(context).showSnackBar(SnackBar(
      content: Text(AppLocalizations.of(context).copyToClipboard),
    ));
  }

  void showModal(context) {
    showModalBottomSheet(
        context: context,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
              topRight: Radius.circular(20.0), topLeft: Radius.circular(20.0)),
        ),
        barrierColor: Colors.black.withOpacity(0.5),
        builder: (context) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                decoration: const BoxDecoration(
                  color: Color(0xFFF0F0F0),
                  borderRadius: BorderRadius.only(
                      topRight: Radius.circular(20.0),
                      topLeft: Radius.circular(20.0)),
                ),
                height: 65,
                //width: 1000,
                //color: Colors.amber,
                child: Column(
                  children: [
                    const SizedBox(
                      height: 10,
                    ),
                    Image.asset(
                      'assets/images/Rectangle 18.png',
                      fit: BoxFit.fitHeight,
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    Text(
                      AppLocalizations.of(context).fontExam,
                      style: AppStyles.bodyBold.copyWith(
                          color: Colors.black,
                          fontSize: getDeviceType() == 'tablet' ? 20 : 16,
                          fontWeight: FontWeight.w600),
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                  ],
                ),
              ),
              ConstrainedBox(
                constraints:
                    const BoxConstraints(maxHeight: 300, minHeight: 56.0),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  //height: 300,
                  alignment: Alignment.center,
                  child: ListView.separated(
                      itemCount: _fontSize.length,
                      separatorBuilder: (context, int) {
                        return const Divider();
                      },
                      itemBuilder: (context, index) {
                        return GestureDetector(
                            behavior: HitTestBehavior.translucent,
                            child: Row(
                              children: [
                                const CircleAvatar(
                                  backgroundColor: Colors.transparent,
                                  radius: 20,
                                  child: Icon(
                                    Icons.fiber_manual_record,
                                    color: Colors.orange,
                                    size: 10,
                                  ),
                                ),
                                Text(
                                  _fontSize[index],
                                  style: AppStyles.textWeight500.copyWith(
                                    color: Colors.black,
                                    fontSize:
                                        getDeviceType() == 'tablet' ? 18 : 14,
                                  ),
                                ),
                                _fontSize[index] == fontvalue
                                    ? Expanded(
                                        child: Align(
                                          alignment: Alignment.topRight,
                                          child: Padding(
                                            padding: const EdgeInsets.only(
                                                right: 10.0),
                                            child: SvgPicture.asset(
                                              'assets/images/Frame.svg',
                                              //width: MediaQuery.of(context).size.width * 0.6,
                                            ),
                                          ),
                                        ),
                                      )
                                    : Container(),
                              ],
                            ),
                            onTap: () {
                              setState(() {
                                fontvalue = _fontSize[index];
                                saveFontSize();
                              });
                              Navigator.of(context).pop();
                            });
                      }),
                ),
              ),
            ],
          );
        });
  }

  void showModalLang(context) {
    showModalBottomSheet(
        context: context,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
              topRight: Radius.circular(20.0), topLeft: Radius.circular(20.0)),
        ),
        barrierColor: Colors.black.withOpacity(0.5),
        builder: (context) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                decoration: const BoxDecoration(
                  color: Color(0xFFF0F0F0),
                  borderRadius: BorderRadius.only(
                      topRight: Radius.circular(20.0),
                      topLeft: Radius.circular(20.0)),
                ),
                height: 65,
                //width: 1000,
                //color: Colors.amber,
                child: Column(
                  children: [
                    const SizedBox(
                      height: 10,
                    ),
                    Image.asset(
                      'assets/images/Rectangle 18.png',
                      fit: BoxFit.fitHeight,
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    Text(
                      AppLocalizations.of(context).chooseLang,
                      style: AppStyles.bodyBold.copyWith(
                          color: Colors.black,
                          fontSize: getDeviceType() == 'tablet' ? 20 : 16,
                          fontWeight: FontWeight.w600),
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                  ],
                ),
              ),
              ConstrainedBox(
                constraints:
                    const BoxConstraints(maxHeight: 300, minHeight: 56.0),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  //height: 150,
                  alignment: Alignment.center,
                  child: ListView.separated(
                      itemCount: _languages.length,
                      separatorBuilder: (context, int) {
                        return const Divider();
                      },
                      itemBuilder: (context, index) {
                        return GestureDetector(
                            behavior: HitTestBehavior.translucent,
                            child: Row(
                              children: [
                                const CircleAvatar(
                                  backgroundColor: Colors.transparent,
                                  radius: 20,
                                  child: Icon(
                                    Icons.fiber_manual_record,
                                    color: Colors.orange,
                                    size: 10,
                                  ),
                                ),
                                Text(
                                  _languages[index].name.toString(),
                                  style: AppStyles.textWeight500.copyWith(
                                    color: Colors.black,
                                    fontSize:
                                        getDeviceType() == 'tablet' ? 18 : 14,
                                  ),
                                ),
                                _languages[index].code ==
                                        Localizations.localeOf(context)
                                            .toString()
                                    ? Expanded(
                                        child: Align(
                                          alignment: Alignment.topRight,
                                          child: Padding(
                                            padding: const EdgeInsets.only(
                                                right: 10.0),
                                            child: SvgPicture.asset(
                                              'assets/images/Frame.svg',
                                              //width: MediaQuery.of(context).size.width * 0.6,
                                            ),
                                          ),
                                        ),
                                      )
                                    : Container(),
                              ],
                            ),
                            onTap: () {
                              setState(() {
                                //fontvalue = _fontSize[index];
                                //saveFontSize();
                                _chosenValue = _languages[index];
                                Provider.of<LocaleProvider>(context,
                                        listen: false)
                                    .setLocale(Locale(_languages[index].code!));
                                saveLanguage(_chosenValue!.code!);
                                logEvent("setting_lang_update",
                                    {"lang_update_name": _chosenValue!.code!});
                              });
                              Navigator.of(context).pop();
                            });
                      }),
                ),
              ),
            ],
          );
        });
  }

  void showModalSelectDate(context) {
    setState(() {
      updateTimeLang();
    });
    showModalBottomSheet(
        context: context,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
              topRight: Radius.circular(20.0), topLeft: Radius.circular(20.0)),
        ),
        barrierColor: Colors.black.withOpacity(0.5),
        builder: (context) {
          return StatefulBuilder(
              builder: (BuildContext context, StateSetter setState2) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  decoration: const BoxDecoration(
                    color: Color(0xFFF0F0F0),
                    borderRadius: BorderRadius.only(
                        topRight: Radius.circular(20.0),
                        topLeft: Radius.circular(20.0)),
                  ),
                  height: 60,
                  //width: 1000,
                  //color: Colors.amber,
                  child: Column(
                    children: [
                      const SizedBox(
                        height: 10,
                      ),
                      Image.asset(
                        'assets/images/Rectangle 18.png',
                        fit: BoxFit.fitHeight,
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      Text(
                        AppLocalizations.of(context).notificationDays,
                        style: AppStyles.bodyBold.copyWith(
                            color: Colors.black,
                            fontSize: getDeviceType() == 'tablet' ? 20 : 16,
                            fontWeight: FontWeight.w600),
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                    ],
                  ),
                ),
                ConstrainedBox(
                  constraints:
                      const BoxConstraints(maxHeight: 300, minHeight: 56.0),
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    //height: 150,
                    alignment: Alignment.center,
                    child: ListView.separated(
                        itemCount: dayOfWeek.length,
                        separatorBuilder: (context, int) {
                          return const Divider();
                        },
                        itemBuilder: (context, index) {
                          return ContactItem(
                              dayOfWeek[index].name,
                              dayOfWeek[index].id,
                              dayOfWeek[index].isSelected,
                              index,
                              setState2);
                        }),
                  ),
                ),
              ],
            );
          });
        }).whenComplete(() => {setDays(selectedDays)});
  }

  Widget ContactItem(String name, int id, bool isSelected, int index,
      Function(void Function()) setState2) {
    return GestureDetector(
        behavior: HitTestBehavior.translucent,
        child: Row(
          children: [
            const CircleAvatar(
              backgroundColor: Colors.transparent,
              radius: 20,
              child: Icon(
                Icons.fiber_manual_record,
                color: Colors.orange,
                size: 10,
              ),
            ),
            Text(
              name,
              style: AppStyles.textWeight500.copyWith(
                color: Colors.black,
                fontSize: getDeviceType() == 'tablet' ? 18 : 14,
              ),
            ),
            isSelected
                ? Expanded(
                    child: Align(
                      alignment: Alignment.topRight,
                      child: Padding(
                        padding: const EdgeInsets.only(right: 10.0),
                        child: SvgPicture.asset(
                          'assets/images/Frame.svg',
                          //width: MediaQuery.of(context).size.width * 0.6,
                        ),
                      ),
                    ),
                  )
                : Container(),
          ],
        ),
        onTap: () {
          setState2(() {
            dayOfWeek[index].isSelected = !dayOfWeek[index].isSelected;
            if (dayOfWeek[index].isSelected == true) {
              selectedDays.add(DaysModel(name: name, isSelected: true, id: id));
            } else if (dayOfWeek[index].isSelected == false) {
              if (selectedDays.length > 1)
                selectedDays.removeWhere(
                    (element) => element.id == dayOfWeek[index].id);
              else {
                dayOfWeek[index].isSelected = !dayOfWeek[index].isSelected;
              }
            }
          });
        });
  }

  Widget buildSwitch() => Transform.scale(
        scale: 1.1,
        child: SizedBox(
          height: 25,
          child: Switch.adaptive(
            value: notificationStatus,
            onChanged: (value) {
              setState(() {
                // notificationStatus = value;
                // saveNotificationStatus(notificationStatus);
                if (value) {
                  requestNotification();
                  sendNotificationRequest();
                  logEvent("setting_study_reminder_on", {});
                } else {
                  NotificationService().cancelNotification();
                  setState(() {
                    notificationStatus = value;
                    saveNotificationStatus(value);
                  });
                  logEvent("setting_study_reminder_off", {});
                }
              });
            },
          ),
        ),
      );
  Future setDays(List<DaysModel> arr) async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    var saveDays = DaysModel.encode(arr);
    await preferences.setString('dayOfWeek', jsonEncode(saveDays));
    List<int> arrayDays = [];
    String logDays = "";
    for (var value in arr) {
      arrayDays.add(value.id);
      logDays += "${value.name},";
    }
    logEvent("setting_reminder_days_update", {"reminder_days_update": logDays});
    if (notificationStatus) {
      sendNotificationRequest();
    }
    /* NotificationService().showNotification(1, "Notification",
        "Come back and do your test", scheduleTime, arrayDays); */
  }

  void _showDialog() {
    showDialog<String>(
      context: context,
      builder: (BuildContext context) => Dialog(
        // contentPadding: EdgeInsets.fromLTRB(16, 30, 16, 24),
        insetPadding: const EdgeInsets.symmetric(horizontal: 16),
        child: SingleChildScrollView(
          child: ConstrainedBox(
            constraints: const BoxConstraints(
              maxWidth: 600.0,
            ),
            child: SizedBox(
              width: MediaQuery.of(context).size.width * 1,
              child: Padding(
                padding: const EdgeInsets.fromLTRB(16, 30, 16, 24),
                child: Column(
                  children: [
                    Container(
                      width: 196,
                      height: 139,
                      decoration: const BoxDecoration(
                          image: DecorationImage(
                        image: AssetImage("assets/images/info_dialog.png"),
                        fit: BoxFit.fill,
                      )),
                    ),
                    Text(
                      "Đang môi trường ${environment.toString()}",
                      style: AppStyles.dialogText,
                      textAlign: TextAlign.center,
                    ),
                    Text(
                      " Đổi môi trường",
                      style: AppStyles.dialogText,
                      textAlign: TextAlign.center,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(top: 24),
                      child: SizedBox(
                        width: MediaQuery.of(context).size.width * 0.8,
                        child: Row(
                          children: [
                            Expanded(
                                child: Padding(
                              padding: const EdgeInsets.only(right: 6),
                              child: ElevatedButton(
                                onPressed: () {
                                  environment = Environment.dev;

                                  Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                          builder: (context) => const Home()));
                                },
                                style: ElevatedButton.styleFrom(
                                  foregroundColor: AppColors.greenPrimary,
                                  backgroundColor: Colors
                                      .white, //specify the color of the button's text and icons as well as the overlay colors used to indicate the hover, focus, and pressed states
                                  shadowColor: Colors
                                      .white, //specify the button's elevation color
                                  elevation: 0, //buttons Material shadow
                                  // padding: const EdgeInsets.only(top: 4.0, bottom: 4.0, right: 8.0, left: 8.0), //specify the button's Padding
                                  minimumSize: const Size(20,
                                      44), //specify the button's first: width and second: height
                                  side: BorderSide(
                                      color: AppColors.greenPrimary,
                                      width: 1.0,
                                      style: BorderStyle
                                          .solid), //set border for the button
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(4.0)),
                                ),
                                child: Text(
                                  "Dev",
                                  style: AppStyles.secondaryButton,
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            )),
                            Expanded(
                                child: Padding(
                              padding: const EdgeInsets.only(left: 6),
                              child: ElevatedButton(
                                onPressed: () {
                                  environment = Environment.prod;
                                  clearLocalListQuiz();
                                  Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                          builder: (context) => const Home()));
                                },
                                style: ElevatedButton.styleFrom(
                                  foregroundColor: AppColors.white,
                                  backgroundColor: AppColors.greenPrimary,
                                  shadowColor:
                                      const Color.fromARGB(92, 0, 166, 144),
                                  elevation: 0,
                                  minimumSize: const Size(20, 44),
                                  side: BorderSide(
                                      color: AppColors.greenPrimary,
                                      width: 1.0,
                                      style: BorderStyle.solid),
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(4.0)),
                                ),
                                child: Text(
                                  "Prod",
                                  style: AppStyles.primaryButton,
                                ),
                              ),
                            ))
                          ],
                        ),
                      ),
                    )
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

AlertDialog alert = const AlertDialog(
  title: Text("Error"),
  content: Text("Please insert a name."),
);

class DaysModel {
  String name;
  bool isSelected;
  int id;

  DaysModel({required this.name, required this.isSelected, required this.id});

  factory DaysModel.fromJson(Map<String, dynamic> jsonData) {
    return DaysModel(
        name: jsonData['name'],
        isSelected: jsonData['isSelected'],
        id: jsonData['id']);
  }
  static Map<String, dynamic> toMap(DaysModel music) => {
        'name': music.name,
        'isSelected': music.isSelected,
        'id': music.id,
      };
  static String encode(List<DaysModel> musics) => json.encode(
        musics
            .map<Map<String, dynamic>>((music) => DaysModel.toMap(music))
            .toList(),
      );

  static List<DaysModel> decode(String musics) =>
      (json.decode(musics) as List<dynamic>)
          .map<DaysModel>((item) => DaysModel.fromJson(item))
          .toList();
}

extension StringCasingExtension on String {
  String toCapitalized() =>
      length > 0 ? '${this[0].toUpperCase()}${substring(1).toLowerCase()}' : '';
  String toTitleCase() => replaceAll(RegExp(' +'), ' ')
      .split(' ')
      .map((str) => str.toCapitalized())
      .join(' ');
}
