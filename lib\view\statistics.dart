// ignore_for_file: prefer_const_constructors, sized_box_for_whitespace, unnecessary_new, prefer_const_declarations, unused_local_variable
import 'dart:convert';
import 'dart:developer';
import 'package:firebase_performance/firebase_performance.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:psm_app/core/app_colors.dart';
import 'package:psm_app/core/app_styles.dart';
import 'package:psm_app/globals.dart';
import 'package:psm_app/localization.dart';
import 'package:psm_app/models/qlist_model.dart';
import 'package:http/http.dart' as http;
import 'package:psm_app/models/result_model.dart';
import 'package:psm_app/view/exam_detail.dart';
import 'package:psm_app/view/payment/payment.dart';
import 'package:psm_app/view/widgets/error_dialog.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:collection/collection.dart';

class Statistics extends StatefulWidget {
  const Statistics({Key? key}) : super(key: key);

  @override
  _MyAppState createState() => _MyAppState();
}

_goBack(BuildContext context) {
  Navigator.pop(context);
}

class _MyAppState extends State<Statistics> {
  double avg = 0;
  int quizDone = 0;
  int passCount = 0;
  double latestScore = 0;
  int streak = 0;
  int questionDone = 0;
  int wrongCount = 0;
  int rightCount = 0;
  int markCount = 0;
  int totalTime = 0;
  double each = 0;
  double totalScore = 0;
  List recentScore = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
  List recentTest = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
  int numX = 0;
  bool status = false;
  List uniqueQuestion = [];
  String device = "";
  List countedWrongQuestion = [];
  countIncorrectQuestion() async {
    List questions = [];
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    final String incorrectString =
        await prefs.getString('incorrectQuestion') ?? "";
    if (incorrectString != "") questions = jsonDecode(incorrectString);
    List result = [];
    for (int i = 0; i < questions.length; i++) {
      int count = 0;
      var questionId = questions[i]['id'];
      if (countedWrongQuestion.contains(questionId)) {
        continue;
      } else {
        questions.forEach((value) => {if (value['id'] == questionId) count++});
      }
      questions[i]['count'] = count;
      result.add(questions[i]);
      countedWrongQuestion.add(questionId);
    }
  }

  List countedBookmarkId = [];
  countBookmarkQuestion() async {
    List bookmark = [];
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    final String bookmarkString =
        await prefs.getString('bookmarkQuestion') ?? "";
    if (bookmarkString != "") bookmark = jsonDecode(bookmarkString);
    List result = [];
    for (int i = 0; i < bookmark.length; i++) {
      var questionId = bookmark[i]['id'];
      if (countedBookmarkId.contains(questionId)) {
        continue;
      }
      result.add(bookmark[i]);
      countedBookmarkId.add(questionId);
    }
  }

  String getDeviceType() {
    final data = MediaQueryData.fromWindow(WidgetsBinding.instance.window);
    return data.size.shortestSide < 600 ? 'phone' : 'tablet';
  }

  Future<void> getCountQuiz() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    // Fetch and decode data
    final String resultString = await prefs.getString('result') ?? "";
    final List<Result> results = [];
    if (resultString != '') {
      for (var i = 0; i < Result.decode(resultString).length; i++) {
        if (Result.decode(resultString)[i].environment == null ||
            Result.decode(resultString)[i].environment == environment.text) {
          results.add(Result.decode(resultString)[i]);
        }
      }
    }

    inspect(results);
    int consecutive = 0;
    if (results.isNotEmpty) {
      recentScore = [];
      recentTest = [];
    }
    for (var i = 0; i < results.length; i++) {
      // đếm lần pass liên tiếp dài nhất
      if (results[i].status == 'Fail') {
        consecutive = 0;
      } else {
        passCount++;
        consecutive++;
        if (consecutive > streak) {
          streak = consecutive;
        }
      }
      // đếm số lượng bài test
      quizDone++;
      // Lấy tổng số điểm để tính avg
      totalScore +=
          double.parse(double.parse(results[i].percentage).toStringAsFixed(0));
      //Lấy tổng thời gian
      totalTime += results[i].timeDoQuiz;
      //Đếm số câu unique
      for (var t = 0; t < results[i].questions.length; t++) {
        if (!uniqueQuestion.contains(results[i].questions[t].id)) {
          uniqueQuestion.add(results[i].questions[t].id);
        }
      }
      // đếm số câu đã làm
      for (var j = 0; j < results[i].answers.length; j++) {
        if (results[i].answers[j].length != 0 &&
            results[i].answers[j].length != null) {
          questionDone++;
        }
        // đếm câu sai
        if (results[i].questions[j].correct == results[i].answers[j]) {
          rightCount++;
        } else {
          List userAns = [];
          if (results[i].answers[j] is List) {
            for (var t = 0; t < results[i].answers[j].length; t++) {
              if (results[i].answers[j][t] == true) {
                userAns.add(t);
              }
            }
            Function eq = const ListEquality().equals;
            if (eq(userAns, results[i].questions[j].correctIndex)) {
              rightCount++;
            } else {
              wrongCount++;
            }
          } else {
            if (results[i].answers[j] != "" && results[i].answers[j] != null) {
              wrongCount++;
            }
          }
        }
      }
      // đếm câu bookmark
      for (var t = 0; t < results[i].bookmark.length; t++) {
        if (results[i].bookmark[t] == 1) {
          markCount++;
        }
      }
      //10 bai test gan nhat
      if (i == results.length - 10) {
        status = true;
      }
      if (results.length < 10) {
        /* recentScore.insert(
            numX, double.parse(musics[i].percentage).toStringAsFixed(2)); */
        recentScore.add(double.parse(results[i].percentage).toStringAsFixed(2));
        recentTest.add(i + 1);
        numX++;
      } else {
        if (status == true) {
          recentScore
              .add(double.parse(results[i].percentage).toStringAsFixed(2));
          recentTest.add(i + 1);
          numX++;
        }
      }
    }
    //diem gan nhat
    if (results.isNotEmpty) {
      latestScore = double.parse(results[(results.length) - 1].percentage);
    }
    setState(() {});
  }

  @override
  void initState() {
    super.initState();
    logPage("Statistics");
    getCountQuiz();
    countIncorrectQuestion();
    countBookmarkQuestion();
    device = getDeviceType();
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: AppLocalizations.of(context).aboutUs,
      theme: ThemeData(
        appBarTheme: AppBarTheme(
          //shadowColor: Color.fromRGBO(0, 0, 0, 0.16),
          shadowColor: Colors.black,
        ),
      ),
      debugShowCheckedModeBanner: false,
      home: Scaffold(
        appBar: AppBar(
          centerTitle: true,
          leading: IconButton(
            icon: Icon(Icons.arrow_back_ios_new),
            iconSize: 20.0,
            color: AppColors.blackText,
            onPressed: () {
              _goBack(context);
            },
          ),
          title: Text(
            AppLocalizations.of(context).statistics,
            style: AppStyles.appBarTitle,
          ),
          backgroundColor: Colors.white,
          elevation: 0,
        ),
        body: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 0),
            child: Column(
              children: [
                if (!Common.premium) ...[
                  SizedBox(
                    height: 15,
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: GestureDetector(
                      onTap: () {
                        logEvent("statistic_try_now_click", {});
                        Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) => PaymentPage()));
                      },
                      child: Stack(
                        clipBehavior: Clip.none,
                        children: [
                          Container(
                            margin: EdgeInsets.only(
                                top: device == "tablet" ? 20 : 0),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(8),
                                gradient: LinearGradient(
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                  colors: [
                                    AppColors.gradientBannerTop,
                                    AppColors.gradientBannerBottom,
                                  ],
                                )),
                            //height: 140,
                            width: MediaQuery.of(context).size.width,
                            child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Container(
                                    width:
                                        MediaQuery.of(context).size.width * 0.6,
                                    child: Padding(
                                      padding: const EdgeInsets.all(15.0),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                              AppLocalizations.of(context)
                                                  .upgradeToGetFeature,
                                              style: AppStyles.bodyBold
                                                  .copyWith(
                                                      color: Colors.white,
                                                      fontSize: 20)),
                                          Padding(
                                            padding: const EdgeInsets.only(
                                                top: 10.0),
                                            child: Row(
                                              children: [
                                                Text(
                                                  AppLocalizations.of(context)
                                                      .upgrade,
                                                  style: AppStyles.body
                                                      .copyWith(
                                                          color:
                                                              Color(0xff00F9D7),
                                                          fontSize: 12,
                                                          fontWeight:
                                                              FontWeight.bold),
                                                ),
                                                SizedBox(
                                                  width: 3,
                                                ),
                                                Padding(
                                                  padding:
                                                      const EdgeInsets.only(
                                                          top: 2),
                                                  child: Icon(
                                                    Icons.arrow_forward_rounded,
                                                    size: 16,
                                                    color: Color(0xff00F9D7),
                                                  ),
                                                )
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  )
                                ]),
                          ),
                          Positioned(
                            bottom: -8,
                            right: -20,
                            child: SvgPicture.asset(
                              'assets/images/SettingManIcon.svg',
                              width: MediaQuery.of(context).size.width < 400
                                  ? MediaQuery.of(context).size.width * 0.4
                                  : 150,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ] else ...[
                  Container(),
                ],
                numX > 1
                    ? Padding(
                        padding: const EdgeInsets.only(top: 0, right: 20),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(top: 15.0),
                              child: Padding(
                                padding: EdgeInsets.only(left: 25),
                                child: Text(
                                    AppLocalizations.of(context).statistics),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(
                                  bottom: 20.0, left: 25, top: 10),
                              child: Text(
                                AppLocalizations.of(context).score,
                                style: AppStyles.body.copyWith(fontSize: 12),
                              ),
                            ),
                            Center(
                              child: Container(
                                height: 300,
                                width: MediaQuery.of(context).size.width * 0.9,
                                child: LineChart(
                                  mainData(),
                                ),
                              ),
                            ),
                            Center(
                              child: Text(
                                AppLocalizations.of(context).testNumber,
                                style: AppStyles.body.copyWith(fontSize: 12),
                              ),
                            ),
                          ],
                        ),
                      )
                    : Container(),
                Wrap(
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(top: 15.0),
                      child: Padding(
                        padding: EdgeInsets.only(left: 25),
                        child: Text(AppLocalizations.of(context).quizDetail),
                      ),
                    ),
                    Column(
                      children: [
                        Card(
                            quizDone != 0
                                ? (totalScore / quizDone).toStringAsFixed(2)
                                : AppLocalizations.of(context).noData,
                            AppLocalizations.of(context).avgScore,
                            Color.fromRGBO(140, 184, 255, 0.4),
                            2,
                            quizDone == 0 ? true : false,
                            Color(0xff00326C),
                            Color(0xff708AA9),
                            device),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Card(
                                quizDone.toString(),
                                AppLocalizations.of(context).totalQuizDone,
                                Color.fromRGBO(140, 184, 255, 0.4),
                                1,
                                false,
                                Color(0xff00326C),
                                Color(0xff708AA9),
                                device),
                            Card(
                                streak.toString(),
                                AppLocalizations.of(context).streak,
                                Color(0xffCED9FF),
                                1,
                                false,
                                Color(0xff00326C),
                                Color(0xff708AA9),
                                device)
                          ],
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Card(
                                passCount.toString(),
                                AppLocalizations.of(context).passedExam,
                                Color(0xffCED9FF),
                                1,
                                false,
                                Color(0xff00326C),
                                Color(0xff708AA9),
                                device),
                            Card(
                                "${latestScore.round()}%",
                                AppLocalizations.of(context).latestScore,
                                Color(0xffCED9FF),
                                1,
                                false,
                                Color(0xff00326C),
                                Color(0xff708AA9),
                                device)
                          ],
                        ),
                      ],
                    )
                  ],
                ),
                SizedBox(
                  height: 30,
                ),
                Wrap(
                  children: [
                    Padding(
                      padding: EdgeInsets.only(left: 25),
                      child: Text(AppLocalizations.of(context).questionDetail),
                    ),
                    SizedBox(
                      height: 20,
                    ),
                    Column(
                      children: [
                        Card(
                            uniqueQuestion.length.toString(),
                            AppLocalizations.of(context).questionDone,
                            Color(0xffB8E4EE),
                            2,
                            false,
                            Color(0xff1A5461),
                            Color(0xff62756F),
                            device),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Card(
                                (countedWrongQuestion.length).toString(),
                                AppLocalizations.of(context).wrongAns,
                                Color(0xffB8E4EE),
                                1,
                                false,
                                Color(0xff1A5461),
                                Color(0xff62756F),
                                device),
                            Card(
                                (countedBookmarkId.length).toString(),
                                AppLocalizations.of(context).marked,
                                Color(0xffB8E4EE),
                                1,
                                false,
                                Color(0xff1A5461),
                                Color(0xff62756F),
                                device)
                          ],
                        ),
                      ],
                    )
                  ],
                ),
                SizedBox(
                  height: 30,
                ),
                Wrap(
                  children: [
                    Padding(
                      padding: EdgeInsets.only(left: 25),
                      child: Text(AppLocalizations.of(context).timeDetail),
                    ),
                    SizedBox(
                      height: 20,
                    ),
                    Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Card(
                                "${(totalTime / 60).toStringAsFixed(0)} ${AppLocalizations.of(context).minutesShort}",
                                AppLocalizations.of(context).totalTime,
                                Color(0xffFFCECE),
                                1,
                                false,
                                Color(0xff502222),
                                Color(0xff674E4E),
                                device),
                            Card(
                                questionDone != 0
                                    ? "${(totalTime / questionDone).toStringAsFixed(0)} ${AppLocalizations.of(context).secondShort}"
                                    : " ${AppLocalizations.of(context).noData}",
                                AppLocalizations.of(context).eachQuestion,
                                Color(0xffFFCECE),
                                1,
                                questionDone == 0 ? true : false,
                                Color(0xff502222),
                                Color(0xff674E4E),
                                device),
                          ],
                        ),
                      ],
                    )
                  ],
                ),
                SizedBox(
                  height: 50,
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  LineChartData mainData() {
    return LineChartData(
      lineTouchData: LineTouchData(
          touchTooltipData: LineTouchTooltipData()),
      gridData: FlGridData(
        show: true,
        drawVerticalLine: true,
        horizontalInterval: 1,
        verticalInterval: 1,
        getDrawingHorizontalLine: (value) {
          if (value == 20) {
            return FlLine(
                color: Color(0xffEBEBEB), strokeWidth: 1, dashArray: [6, 6]);
          } else if (value == 40) {
            return FlLine(
                color: Color(0xffEBEBEB), strokeWidth: 1, dashArray: [6, 6]);
          } else if (value == 60) {
            return FlLine(
                color: Color(0xffEBEBEB), strokeWidth: 1, dashArray: [6, 6]);
          } else if (value == 80) {
            return FlLine(
                color: Color(0xffEBEBEB), strokeWidth: 1, dashArray: [6, 6]);
          } else if (value == 100) {
            return FlLine(
                color: Color(0xffEBEBEB), strokeWidth: 1, dashArray: [6, 6]);
          } else {
            return FlLine(
              color: Colors.white,
              strokeWidth: 1,
            );
          }
        },
        getDrawingVerticalLine: (value) {
          return FlLine(
              color: Color(0xffEBEBEB), strokeWidth: 1, dashArray: [6, 6]);
        },
      ),
      titlesData: FlTitlesData(
        show: true,
        rightTitles: AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        topTitles: AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        bottomTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            reservedSize: 30,
            interval: 1,
          ),
        ),
        leftTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            interval: 20,
            reservedSize: 42,
          ),
        ),
      ),
      borderData: FlBorderData(
        show: true,
        border: Border(
          bottom: BorderSide(color: Color(0xffBBC9D0)),
          left: BorderSide(color: Color(0xffBBC9D0)),
        ),
      ),
      minX: recentTest[0].toDouble(),
      maxX: status ? recentTest[9].toDouble() : numX.toDouble(),
      minY: 0,
      maxY: 100,
      lineBarsData: [
        LineChartBarData(
          preventCurveOverShooting: true,
          spots: [
            /* FlSpot(recentTest[0].toDouble(), recentScore[0].toDouble()),
            FlSpot(recentTest[1].toDouble(), recentScore[1].toDouble()),
            FlSpot(recentTest[2].toDouble(), recentScore[2].toDouble()),
            FlSpot(recentTest[3].toDouble(), recentScore[3].toDouble()),
            FlSpot(recentTest[4].toDouble(), recentScore[4].toDouble()),
            FlSpot(recentTest[5].toDouble(), recentScore[5].toDouble()),
            FlSpot(recentTest[6].toDouble(), recentScore[6].toDouble()),
            FlSpot(recentTest[7].toDouble(), recentScore[7].toDouble()),
            FlSpot(recentTest[8].toDouble(), recentScore[8].toDouble()),
            FlSpot(recentTest[9].toDouble(), recentScore[9].toDouble()), */
            for (int i = 0; i < numX; i++)
              FlSpot(recentTest[i].toDouble(), double.parse(recentScore[i])),
          ],
          isCurved: true,
          color: Color.fromRGBO(7, 108, 225, 0.58),
          barWidth: 2,
          isStrokeCapRound: true,
          dotData: FlDotData(
            show: false,
          ),
          belowBarData: BarAreaData(
            show: true,
            gradient: LinearGradient(
              colors: [
                Color(0xffB2D5FF).withOpacity(0.5),
                Color(0xffFFFFFF).withOpacity(0.5)
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
        ),
      ],
    );
  }

  List<Color> gradientColors = [
    const Color(0xff076CE1),
    const Color(0xffFFFFFF)
  ];
}

class Card extends StatelessWidget {
  String score;
  String title;
  String device;
  Color color;
  int style;
  bool noData;
  Color colorText;
  Color colorTitle;
  Card(this.score, this.title, this.color, this.style, this.noData,
      this.colorText, this.colorTitle, this.device,
      {Key? key})
      : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 10),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: const [
            BoxShadow(
              color: Color.fromRGBO(140, 184, 255, 0.4),
              spreadRadius: 0,
              blurRadius: 12,
              offset: Offset(3, 6), // changes position of shadow
            ),
          ],
        ),
        width: style == 1
            ? device == "tablet"
                ? MediaQuery.of(context).size.width * 0.47
                : MediaQuery.of(context).size.width * 0.42
            : device == "tablet"
                ? MediaQuery.of(context).size.width * 0.94 + 16
                : MediaQuery.of(context).size.width * 0.84 + 16,
        height: 90,
        child: Stack(
          children: [
            Align(
              alignment: AlignmentDirectional.topStart, // <-- SEE HERE
              child: Container(
                  width: 10,
                  height: 115,
                  decoration: BoxDecoration(
                    color: color,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(
                        8,
                      ),
                      bottomLeft: Radius.circular(
                        8,
                      ),
                    ),
                  )),
            ),
            style == 1
                ? Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 18),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          title,
                          style: AppStyles.bodyBold
                              .copyWith(color: colorTitle, fontSize: 12),
                        ),
                        SizedBox(
                          height: 10,
                        ),
                        Text(
                          score,
                          style: AppStyles.titleBold.copyWith(
                              color: colorText, fontSize: noData ? 16 : 24),
                        ),
                      ],
                    ))
                : Padding(
                    padding: const EdgeInsets.only(left: 16, right: 16, top: 0),
                    child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Center(
                            //padding: const EdgeInsets.only(left: 10.0, top: 0),
                            child: Text(
                              title,
                              style: AppStyles.bodyBold
                                  .copyWith(color: colorTitle, fontSize: 18),
                            ),
                          ),
                          Flexible(
                            child: Padding(
                              padding: const EdgeInsets.only(
                                top: 0.0,
                                right: 10,
                                left: 20,
                              ),
                              child: Text(
                                score,
                                style: AppStyles.titleBold.copyWith(
                                    color: colorText,
                                    fontSize: noData ? 18 : 28),
                              ),
                            ),
                          ),
                        ]),
                  ),
          ],
        ),
      ),
    );
  }
}
