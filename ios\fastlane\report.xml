<?xml version="1.0" encoding="UTF-8"?>
<testsuites>
  <testsuite name="fastlane.lanes">
    
    
    
      
      <testcase classname="fastlane.lanes" name="00: default_platform" time="0.00034">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="01: Switch to ios build lane" time="0.000266">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="02: Switch to ios sh_on_root lane" time="0.000184">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="03: cd /Users/<USER>/Work/Project/exam_simulator &amp;&amp; flutter clean &amp;&amp; rm -Rf ios/Pods" time="19.28186">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="04: Switch to ios sh_on_root lane" time="0.000235">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="05: cd /Users/<USER>/Work/Project/exam_simulator &amp;&amp; flutter build ipa" time="291.802874">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="06: Switch to ios archive lane" time="0.000574">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="07: update_code_signing_settings" time="0.206468">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="08: Switch to ios increment_version lane" time="0.000378">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="09: firebase_app_distribution_get_latest_release" time="1.388154">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="10: increment_build_number" time="1.14661">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="11: update_project_team" time="0.029339">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="12: build_app" time="196.694615">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="13: firebase_app_distribution" time="12.959425">
        
      </testcase>
    
  </testsuite>
</testsuites>
