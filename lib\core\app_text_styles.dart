import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import 'app_colors.dart';

class AppTextStyles {
  static final TextStyle title = GoogleFonts.roboto(
    color: AppColors.white,
    fontSize: 20,
    fontWeight: FontWeight.w400,
  );

  static final TextStyle titleBold = GoogleFonts.roboto(
    color: AppColors.white,
    fontSize: 20,
    fontWeight: FontWeight.w600,
  );

  static final TextStyle heading = GoogleFonts.roboto(
    color: AppColors.black,
    fontSize: 18,
    fontWeight: FontWeight.w600,
  );
  static final TextStyle headingPass = GoogleFonts.roboto(
    color: Colors.green,
    fontSize: 30,
    fontWeight: FontWeight.w600,
  );
  static final TextStyle headingFail = GoogleFonts.roboto(
    color: Colors.red,
    fontSize: 30,
    fontWeight: FontWeight.w600,
  );

  static final TextStyle heading40 = GoogleFonts.roboto(
    color: AppColors.black,
    fontSize: 40,
    fontWeight: FontWeight.w600,
  );
  static final TextStyle heading30 = GoogleFonts.roboto(
    color: AppColors.black,
    fontSize: 30,
    fontWeight: FontWeight.w600,
  );

  static final TextStyle heading15 = GoogleFonts.roboto(
    color: AppColors.black,
    fontSize: 15,
    fontWeight: FontWeight.w600,
  );
  static final TextStyle heading20Pass = GoogleFonts.roboto(
    color: Colors.green,
    fontSize: 20,
    fontWeight: FontWeight.w600,
  );
  static final TextStyle heading20Fail = GoogleFonts.roboto(
    color: Colors.red,
    fontSize: 20,
    fontWeight: FontWeight.w600,
  );

  static final TextStyle body = GoogleFonts.roboto(
    color: AppColors.grey,
    fontSize: 13,
    fontWeight: FontWeight.normal,
  );
  static final TextStyle body40 = GoogleFonts.roboto(
    color: AppColors.grey,
    fontSize: 18,
    fontWeight: FontWeight.normal,
  );

  static final TextStyle bodyBold = GoogleFonts.roboto(
    color: AppColors.grey,
    fontSize: 13,
    fontWeight: FontWeight.bold,
  );

  static final TextStyle bodylightGrey = GoogleFonts.roboto(
    color: AppColors.lightGreen,
    fontSize: 13,
    fontWeight: FontWeight.normal,
  );
  static final TextStyle bodyDarkGreen = GoogleFonts.roboto(
    color: AppColors.darkGreen,
    fontSize: 13,
    fontWeight: FontWeight.w500,
  );

  static final TextStyle bodyDarkRed = GoogleFonts.roboto(
    color: AppColors.darkRed,
    fontSize: 13,
    fontWeight: FontWeight.w500,
  );

  static final TextStyle body20 = GoogleFonts.roboto(
    color: AppColors.grey,
    fontSize: 20,
    fontWeight: FontWeight.normal,
  );
  static final TextStyle bodyLightGrey20 = GoogleFonts.roboto(
    color: AppColors.lightGrey,
    fontSize: 20,
    fontWeight: FontWeight.normal,
  );

  static final TextStyle bodyWhite20 = GoogleFonts.roboto(
    color: AppColors.white,
    fontSize: 20,
    fontWeight: FontWeight.normal,
  );
  static final TextStyle body11 = GoogleFonts.roboto(
    color: AppColors.grey,
    fontSize: 11,
    fontWeight: FontWeight.normal,
  );
}
