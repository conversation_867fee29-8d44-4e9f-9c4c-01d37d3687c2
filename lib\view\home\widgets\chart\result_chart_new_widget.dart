import 'package:psm_app/core/app_colors.dart';
import 'package:psm_app/core/core.dart';
import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

class Pie<PERSON>hart extends StatefulWidget {
  final int right;
  final int wrong;
  final int unans;
  @override
  _ChartWidgetState createState() => _ChartWidgetState();
  PieChart(
      {Key? key, required this.right, required this.wrong, required this.unans})
      : super(key: key);
}

class _ChartWidgetState extends State<PieChart> {
  late List<GDPData> _chartData;
  late TooltipBehavior _tooltipBehavior;
  @override
  void initState() {
    _chartData = getChartData(widget.right, widget.wrong, widget.unans);
    _tooltipBehavior = TooltipBehavior(enable: true);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 112,
      width: MediaQuery.of(context).size.width * 0.3,
      child: SfCircularChart(
        margin: EdgeInsets.zero,
        legend:
            Legend(isVisible: false, overflowMode: LegendItemOverflowMode.wrap),
        tooltipBehavior: _tooltipBehavior,
        series: <CircularSeries>[
          DoughnutSeries<GDPData, String>(
            dataSource: _chartData,
            xValueMapper: (GDPData data, _) => data.continent,
            yValueMapper: (GDPData data, _) => data.gdp,
            pointColorMapper: (GDPData data, _) => data.color,
            innerRadius: '30%',
            radius: '100%',
            dataLabelSettings: const DataLabelSettings(isVisible: false),
            enableTooltip: false,
          )
        ],
      ),
    );
  }
}

List<GDPData> getChartData(int right, int wrong, int unans) {
  final List<GDPData> chartData = [
    GDPData('Đúng', right, AppColors.chartRight),
    GDPData('Sai', wrong, AppColors.chartWrong),
    GDPData('Không', unans, AppColors.chartUnans),
  ];
  return chartData;
}

class GDPData {
  GDPData(this.continent, this.gdp, this.color);
  final String continent;
  final int gdp;
  final Color color;
}
