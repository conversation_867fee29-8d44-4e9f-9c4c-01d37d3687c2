import 'package:flutter/material.dart';
import 'package:showcaseview/showcaseview.dart';

import '../../core/core.dart';
import '../../localization.dart';

class ShowcaseTooltip extends StatelessWidget {
  final String? text;
  final Function()? next;
  final Function()? back;
  final bool showBack;
  final bool isArrowUp;
  final bool lastItem;
  final double? posittionLeft;
  final double? posittionTop;
  final double? posittionBottom;
  final double? posittionRight;
  final BuildContext showcaseContext;
  const ShowcaseTooltip(
      {Key? key,
      this.text,
      this.back,
      this.next,
      this.showBack = false,
      this.isArrowUp = true,
      required this.showcaseContext,
      this.posittionLeft,
      this.posittionTop,
      this.posittionBottom,
      this.posittionRight,
      this.lastItem = false})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        Container(
          width: 300,
          // height: 120,
          decoration: BoxDecoration(
              color: AppColors.white, borderRadius: BorderRadius.circular(8)),
          child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.all(12),
                  child: Text(
                    text ?? "",
                    style: const TextStyle(
                        color: Colors.black,
                        fontSize: 16,
                        height: 1.625,
                        fontWeight: FontWeight.w500),
                  ),
                ),
                Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      if (showBack) ...{
                        ElevatedButton(
                          onPressed: () =>
                              ShowCaseWidget.of(showcaseContext).previous(),
                          child: Text(
                            AppLocalizations.of(context).back,
                            style: AppStyles.secondaryButton.copyWith(
                                fontWeight: FontWeight.w500,
                                height: 1,
                                color: AppColors.greenPrimary),
                            textAlign: TextAlign.center,
                          ),
                          style: ElevatedButton.styleFrom(
                            foregroundColor: AppColors.white,
                            backgroundColor: AppColors.white,
                            elevation: 0,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 22, vertical: 0),
                            side: BorderSide(
                                color: AppColors.greenPrimary,
                                width: 1,
                                style: BorderStyle.solid),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(4.0),
                            ),
                          ),
                        ),
                      } else ...{
                        const SizedBox()
                      },
                      ElevatedButton(
                        onPressed: () =>
                            ShowCaseWidget.of(showcaseContext).next(),
                        child: Text(
                          lastItem ? "OK" : AppLocalizations.of(context).next,
                          style: AppStyles.secondaryButton.copyWith(
                              fontWeight: FontWeight.w500,
                              height: 1,
                              color: Colors.white),
                          textAlign: TextAlign.center,
                        ),
                        style: ElevatedButton.styleFrom(
                          foregroundColor: AppColors.greenLight,
                          backgroundColor: AppColors.greenPrimary,
                          elevation: 0,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 23, vertical: 0),
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(4.0)),
                        ),
                      ),
                    ],
                  ),
                )
              ]),
        ),
        Positioned(
            top: posittionTop,
            left: posittionLeft,
            bottom: posittionBottom,
            right: posittionRight,
            child: CustomPaint(
              size: const Size(11, 11),
              painter: PaintTriangle(
                  backgroundColor: AppColors.white, isUp: isArrowUp),
            ))
      ],
    );
  }
}

class PaintTriangle extends CustomPainter {
  final Color backgroundColor;
  final bool isUp;

  PaintTriangle({required this.backgroundColor, this.isUp = true});

  @override
  void paint(Canvas canvas, Size size) {
    final y = size.height;
    final x = size.width;

    final paint = Paint()..color = backgroundColor;
    final path = Path();

    if (isUp) {
      path
        ..moveTo(0, y)
        ..lineTo((x / 2), 0)
        ..lineTo(x, y);
    } else {
      path
        ..moveTo(0, 0)
        ..lineTo((x / 2), (y))
        ..lineTo(x, 0);
    }

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) {
    return true;
  }
}
